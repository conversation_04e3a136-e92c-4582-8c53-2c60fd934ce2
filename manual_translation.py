import os
import re
import sys

def add_translation_tags_to_file(file_path, replacements):
    """Add translation tags to a file based on a list of replacements."""
    with open(file_path, 'r', encoding='utf-8', errors='ignore') as f:
        content = f.read()
    
    for old_text, new_text in replacements:
        content = content.replace(old_text, new_text)
    
    with open(file_path, 'w', encoding='utf-8') as f:
        f.write(content)
    
    print(f"Updated {file_path} with {len(replacements)} replacements")

# Example usage:
# replacements = [
#     ('<h1>Hello World</h1>', '<h1>{% trans "Hello World" %}</h1>'),
#     ('<p>Welcome to my site</p>', '<p>{% trans "Welcome to my site" %}</p>'),
# ]
# add_translation_tags_to_file('templates/example.html', replacements)

# Base template replacements
base_replacements = [
    ('<title>Legend Fitness Club</title>', '<title>{% trans "Legend Fitness Club" %}</title>'),
    ('Gym Management System', '{% trans "Gym Management System" %}'),
    ('Dashboard', '{% trans "Dashboard" %}'),
    ('Members', '{% trans "Members" %}'),
    ('Packages', '{% trans "Packages" %}'),
    ('Products', '{% trans "Products" %}'),
    ('Payments', '{% trans "Payments" %}'),
    ('Reports', '{% trans "Reports" %}'),
    ('Settings', '{% trans "Settings" %}'),
    ('Logout', '{% trans "Logout" %}'),
    ('Profile', '{% trans "Profile" %}'),
    ('Copyright', '{% trans "Copyright" %}'),
]

# Sidebar template replacements
sidebar_replacements = [
    ('Dashboard', '{% trans "Dashboard" %}'),
    ('Pay-per-visit', '{% trans "Pay-per-visit" %}'),
    ('Members', '{% trans "Members" %}'),
    ('Packages', '{% trans "Packages" %}'),
    ('Products', '{% trans "Products" %}'),
    ('Categories', '{% trans "Categories" %}'),
    ('Inventory', '{% trans "Inventory" %}'),
    ('Suppliers', '{% trans "Suppliers" %}'),
    ('Purchases', '{% trans "Purchases" %}'),
    ('Sales', '{% trans "Sales" %}'),
    ('POS', '{% trans "POS" %}'),
    ('Payments', '{% trans "Payments" %}'),
    ('Member Payments', '{% trans "Member Payments" %}'),
    ('Bill Management', '{% trans "Bill Management" %}'),
    ('Salary', '{% trans "Salary" %}'),
    ('Employee Salary', '{% trans "Employee Salary" %}'),
    ('Salary Payments', '{% trans "Salary Payments" %}'),
    ('Financial Reports', '{% trans "Financial Reports" %}'),
    ('Income Report', '{% trans "Income Report" %}'),
    ('Expense Report', '{% trans "Expense Report" %}'),
    ('Balance Report', '{% trans "Balance Report" %}'),
    ('Settings', '{% trans "Settings" %}'),
    ('General', '{% trans "General" %}'),
    ('System', '{% trans "System" %}'),
    ('UI', '{% trans "UI" %}'),
    ('Pay-per-visit Settings', '{% trans "Pay-per-visit Settings" %}'),
    ('Product Settings', '{% trans "Product Settings" %}'),
    ('User Management', '{% trans "User Management" %}'),
    ('Employees', '{% trans "Employees" %}'),
    ('System Access', '{% trans "System Access" %}'),
]

# Login template replacements
login_replacements = [
    ('Legend Fitness Club', '{% trans "Legend Fitness Club" %}'),
    ('ប្រព័ន្ធគ្រប់គ្រង​ ក្លឹបហាត់ប្រាណ', '{% trans "ប្រព័ន្ធគ្រប់គ្រង​ ក្លឹបហាត់ប្រាណ" %}'),
    ('Copyright &copy; 2025 Legend Fitness Club. All rights reserved.', '{% trans "Copyright" %} &copy; 2025 {% trans "Legend Fitness Club. All rights reserved." %}'),
    ('ចូលប្រើប្រាស់', '{% trans "ចូលប្រើប្រាស់" %}'),
    ('placeholder="ឈ្មោះអ្នកប្រើប្រាស់"', 'placeholder="{% trans "ឈ្មោះអ្នកប្រើប្រាស់" %}"'),
    ('placeholder="ពាក្យសម្ងាត់"', 'placeholder="{% trans "ពាក្យសម្ងាត់" %}"'),
]

if __name__ == "__main__":
    # Add translation tags to base template
    add_translation_tags_to_file('templates/base.html', base_replacements)
    
    # Add translation tags to sidebar template
    add_translation_tags_to_file('templates/sidebar.html', sidebar_replacements)
    
    # Add translation tags to login template
    add_translation_tags_to_file('templates/auth/login.html', login_replacements)
