# Generated manually to populate employee IDs

from django.db import migrations


def populate_employee_ids(apps, schema_editor):
    """
    Populate the emp_id field for all employees that don't have it set.
    """
    User = apps.get_model('user', 'User')
    
    # Get all employees without an emp_id
    employees_without_id = User.objects.filter(is_employee=True, emp_id__isnull=True)
    
    # Update each employee with a new emp_id
    for i, employee in enumerate(employees_without_id, 1):
        employee.emp_id = f"LFC-{i:03d}"  # Format: LFC-001, LFC-002, etc.
        employee.save()


class Migration(migrations.Migration):

    dependencies = [
        ('user', '0015_remove_user_management_features'),
    ]

    operations = [
        migrations.RunPython(populate_employee_ids),
    ]
