"""
<PERSON><PERSON><PERSON> to verify permissions for all roles in the system.
Run this script to check if permissions are correctly set up.
"""
import os
import sys
import django

# Set up Django environment
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'core.settings')
django.setup()

from settings.models import RolePermission, MODULE_CHOICES
from user.models import ROLE_CHOICES
from settings.utils import get_default_permissions

def print_header(text):
    """Print a header with decoration."""
    print("\n" + "=" * 80)
    print(f" {text} ".center(80, "="))
    print("=" * 80)

def print_subheader(text):
    """Print a subheader with decoration."""
    print("\n" + "-" * 60)
    print(f" {text} ".center(60, "-"))
    print("-" * 60)

def check_permissions():
    """Check permissions for all roles."""
    print_header("PERMISSION VERIFICATION")
    
    # Get all roles
    roles = [role[0] for role in ROLE_CHOICES]
    
    # Get all modules
    modules = [module[0] for module in MODULE_CHOICES]
    
    # Get default permissions
    default_permissions = get_default_permissions()
    
    # Check each role
    for role in roles:
        print_subheader(f"Role: {role}")
        
        # Get permissions for this role
        role_permissions = {}
        for perm in RolePermission.objects.filter(role=role):
            role_permissions[perm.module] = perm.permission_level
        
        # Check each module
        for module in modules:
            # Get current permission level
            current_level = role_permissions.get(module, 'none')
            
            # Get default permission level
            default_level = default_permissions.get(role, {}).get(module, 'none')
            
            # Check if permission matches default
            matches_default = current_level == default_level
            
            # Determine access levels
            has_view = current_level in ['view', 'edit', 'full']
            has_edit = current_level in ['edit', 'full']
            has_full = current_level == 'full'
            
            # Print permission status
            status = "✓" if matches_default else "✗"
            print(f"{module:15} | Current: {current_level:5} | Default: {default_level:5} | Match: {status}")
            print(f"{'':15} | View: {'✓' if has_view else '✗'}, Edit: {'✓' if has_edit else '✗'}, Full: {'✓' if has_full else '✗'}")
            print(f"{'':15} | {'Would appear in sidebar' if has_view else 'Would NOT appear in sidebar'}")
            print("-" * 60)

def verify_url_access():
    """Verify URL access for all roles."""
    print_header("URL ACCESS VERIFICATION")
    
    # Get all roles
    roles = [role[0] for role in ROLE_CHOICES]
    
    # Define critical modules to check
    critical_modules = [
        'user',      # User management
        'settings',  # Settings
        'finance',   # Finance
        'bill',      # Bill management
    ]
    
    # Check each role
    for role in roles:
        print_subheader(f"Role: {role}")
        
        # Check each critical module
        for module in critical_modules:
            # Check view access
            has_view = RolePermission.has_permission(role, module, 'view')
            has_edit = RolePermission.has_permission(role, module, 'edit')
            has_full = RolePermission.has_permission(role, module, 'full')
            
            print(f"{module:15} | View: {'Allowed' if has_view else 'Blocked'}, "
                  f"Edit: {'Allowed' if has_edit else 'Blocked'}, "
                  f"Full: {'Allowed' if has_full else 'Blocked'}")
        
        print("-" * 60)

def main():
    """Main function."""
    check_permissions()
    verify_url_access()

if __name__ == "__main__":
    main()
