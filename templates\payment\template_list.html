{% extends 'base.html' %}
{% load custom_filters %}



{% block body %}
<div class="conponentSection p-5 bg-gray-200">
    <div class="componentWrapper">
        <!-- Header with navigation -->
        <div class="flex justify-between items-center mb-4">
            <h3 class="text-2xl font-bold">Payment Receipt Templates</h3>
            <div class="flex space-x-2">
                <a href="{% url 'payment:index' %}" class="bg-blue-900 text-white px-4 py-2 rounded">
                    <i class="fa-solid fa-arrow-left mr-2"></i>Back to Payments
                </a>
                <a href="{% url 'payment:create_template' %}" class="bg-green-600 text-white px-4 py-2 rounded">
                    <i class="fa-solid fa-plus mr-2"></i>Create New Template
                </a>
            </div>
        </div>

        <!-- Templates List -->
        <div class="bg-white p-6 rounded-lg shadow-md">
            <div class="relative overflow-x-auto">
                <table class="w-full text-sm text-left">
                    <thead class="text-sm uppercase bg-blue-900 text-gray-50"><tr>
                            <th scope="col" class="px-6 py-3">Name</th>
                            <th scope="col" class="px-6 py-3">Language</th>
                            <th scope="col" class="px-6 py-3">Status</th>
                            <th scope="col" class="px-6 py-3">Created</th>
                            <th scope="col" class="px-6 py-3">Last Updated</th>
                            <th scope="col" class="px-6 py-3">Actions</th>
                        </tr>
                    </thead>
                    <tbody>
                        {% for template in templates %}
                        <tr class="bg-white border">
                            <td class="px-6 py-4 font-medium">{{ template.name }}</td>
                            <td class="px-6 py-4">{{ template.get_language_display }}</td>
                            <td class="px-6 py-4">
                                {% if template.is_default %}
                                <span class="bg-green-100 text-green-800 text-xs font-medium px-2.5 py-0.5 rounded">Default</span>
                                {% else %}
                                <span class="bg-gray-100 text-gray-800 text-xs font-medium px-2.5 py-0.5 rounded">Custom</span>
                                {% endif %}
                            </td>
                            <td class="px-6 py-4">{{ template.created_at|date:"d-M-Y" }}</td>
                            <td class="px-6 py-4">{{ template.updated_at|date:"d-M-Y H:i" }}</td>
                            <td class="px-6 py-4">
                                <div class="flex space-x-2">
                                    <a href="{% url 'payment:preview_template' template.id %}" class="text-blue-600 hover:underline">Preview</a>
                                    <a href="{% url 'payment:edit_template' template.id %}" class="text-green-600 hover:underline">Edit</a>
                                    {% if not template.is_default %}
                                    <a href="{% url 'payment:delete_template' template.id %}" class="text-red-600 hover:underline"
                                       onclick="return confirm('Are you sure you want to delete this template?')">Delete</a>
                                    {% endif %}
                                </div>
                            </td>
                        </tr>
                        {% empty %}
                        <tr class="bg-white border">
                            <td colspan="6" class="px-6 py-4 text-center">No templates found. Create your first template!</td>
                        </tr>
                        {% endfor %}
                    </tbody>
                </table>
            </div>
        </div>
        
        <!-- Template Information -->
        <div class="bg-white p-6 rounded-lg shadow-md mt-6">
            <h4 class="text-xl font-semibold mb-4">About Payment Receipt Templates</h4>
            <div class="space-y-4">
                <p>Customize your payment receipt templates to match your organization's branding and requirements. You can create multiple templates for different purposes.</p>
                
                <div class="grid grid-cols-2 gap-6">
                    <div>
                        <h5 class="font-semibold mb-2">Features:</h5>
                        <ul class="list-disc pl-5 space-y-1">
                            <li>Multiple language support (English, Khmer, or bilingual)</li>
                            <li>Customizable colors and styling</li>
                            <li>Company logo upload</li>
                            <li>Custom header and footer text</li>
                            <li>Optional signature lines</li>
                        </ul>
                    </div>
                    <div>
                        <h5 class="font-semibold mb-2">Tips:</h5>
                        <ul class="list-disc pl-5 space-y-1">
                            <li>Set one template as default for all payment receipts</li>
                            <li>Create separate templates for different payment types</li>
                            <li>Use the preview feature to see how your template looks</li>
                            <li>Add custom CSS for advanced styling</li>
                        </ul>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}
