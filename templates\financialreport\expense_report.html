{% extends "base.html" %}
{% load custom_filters %}
{% load currency_filters %}



{% block head %}
<style>
    /* Print styles for financial reports */
    @media print {
        /* Hide everything by default */
        body * {
            visibility: hidden;
        }

        /* Hide sidebar specifically */
        .sidebar {
            display: none !important;
        }

        /* Make the print section visible */
        .print-section, .print-section * {
            visibility: visible !important;
        }

        /* Show elements with print:block class */
        .print\:block {
            display: block !important;
        }

        /* Position the print section at the top left */
        .print-section {
            position: absolute;
            left: 0;
            top: 0;
            width: 100%;
            margin: 0;
            padding: 20px;
            font-family: Arial, sans-serif !important;
            font-size: 12px !important;
            line-height: 1.5 !important;
            color: #333333 !important;
            background-color: white !important;
        }

        /* Remove margin that accommodates sidebar */
        .conponentSection {
            margin-left: 0 !important;
            background-color: white !important;
        }

        /* Hide elements with no-print class */
        .no-print {
            display: none !important;
        }

        /* Hide tabs and tab content except active one */
        [role="tabpanel"]:not(.active) {
            display: none !important;
        }

        /* Show all tab content when printing */
        [role="tabpanel"] {
            display: block !important;
        }

        /* Reset any background colors for printing */
        .bg-gray-100, .bg-gray-50, .bg-blue-50, .bg-red-50, .bg-green-50, .bg-yellow-50,
        .bg-white, .hover\:bg-gray-50 {
            background-color: white !important;
        }

        /* Remove shadows and borders */
        .shadow-sm, .border, .border-gray-200, .border-l-4, .border-red-600 {
            box-shadow: none !important;
            border-color: #ddd !important;
        }

        /* Format section titles */
        h3.text-xl {
            font-size: 16px !important;
            font-weight: bold !important;
            margin: 20px 0 10px 0 !important;
            color: #e11d48 !important;
            border-bottom: 1px solid #ddd !important;
            padding-bottom: 5px !important;
        }

        /* Ensure tables print well */
        table {
            width: 100% !important;
            border-collapse: collapse !important;
            margin-bottom: 20px !important;
        }

        th, td {
            border: 1px solid #ddd !important;
            padding: 8px !important;
            text-align: left !important;
            font-size: 11px !important;
        }

        th {
            background-color: #f8f9fa !important;
            font-weight: bold !important;
        }

        /* Adjust font sizes for printing */
        h1 {
            font-size: 24px !important;
            margin: 0 !important;
            color: #333333 !important;
        }

        h2 {
            font-size: 18px !important;
            margin: 5px 0 !important;
            color: #e11d48 !important;
        }

        .print-section h3 {
            font-size: 16px !important;
            font-weight: bold !important;
        }

        p {
            font-size: 12px !important;
        }

        /* Add summary card styling */
        .print-summary-card {
            border: 1px solid #ddd !important;
            border-radius: 5px !important;
            padding: 10px !important;
            margin-bottom: 20px !important;
            display: block !important;
        }

        .print-summary-title {
            font-size: 14px !important;
            font-weight: bold !important;
            margin-bottom: 5px !important;
            color: #666 !important;
        }

        .print-summary-value {
            font-size: 18px !important;
            font-weight: bold !important;
            color: #333 !important;
        }

        /* Ensure proper page breaks */
        .page-break {
            page-break-after: always !important;
        }

        /* Add footer */
        .print-footer {
            text-align: center !important;
            font-size: 10px !important;
            color: #666 !important;
            margin-top: 30px !important;
            border-top: 1px solid #ddd !important;
            padding-top: 10px !important;
            display: block !important;
        }
    }
</style>
{% endblock %}

{% block body %}
<div class="conponentSection p-5 sm:ml-64 bg-gray-100">
    <div class="componentWrapper max-w-7xl mx-auto">
        <div class="print-section">
            <!-- Print Header - Only visible when printing -->
            <div class="hidden print:block text-center mb-6">
                <h1 class="text-3xl font-bold">LEGEND FITNESS</h1>
                <h2 class="text-xl font-semibold mt-2">{{ title }}</h2>
                <p class="text-sm mt-2">Period: {{ date_range }}</p>
            </div>

            <!-- Print Summary - Only visible when printing -->
            <div class="hidden print:block print-summary-card">
                <div class="print-summary-title">Total Expenses</div>
                <div class="print-summary-value">{{ total_expenses|format_khr }}</div>
            </div>
        <!-- Header with title and actions -->
        <div class="bg-white p-6 rounded-lg shadow-sm mb-6 border-l-4 border-red-600">
            <div class="flex flex-col md:flex-row justify-between items-start md:items-center mb-6">
                <div>
                    <div class="flex items-center">
                        <a href="{% url 'financialreport:index' %}?filter={{ filter }}&start_date={{ start_date }}&end_date={{ end_date }}" class="mr-3 bg-gray-100 hover:bg-gray-200 text-gray-700 p-2 rounded-full transition-colors">
                            <i class="fa-solid fa-arrow-left"></i>
                        </a>
                        <div>
                            <h2 class="text-3xl font-bold text-gray-800">{{ title }}</h2>
                            <p class="text-gray-600 mt-1">Detailed breakdown of all expense transactions</p>
                        </div>
                    </div>
                </div>
                <div class="flex mt-4 md:mt-0">
                    <div class="relative inline-block">
                        <button id="printReportBtn" class="flex items-center bg-white border border-gray-300 text-gray-700 px-4 py-2 rounded-md hover:bg-gray-50 mr-2 transition-colors no-print"><i class="fa-solid fa-print mr-2 text-red-600"></i> Print Report</button>
                        <div id="printTemplateDropdown" class="hidden absolute z-10 mt-2 w-64 rounded-md shadow-lg bg-white ring-1 ring-black ring-opacity-5">
                            <div class="py-1">
                                <div class="px-4 py-2 text-sm text-gray-700 font-medium border-b">Select Template</div>
                                {% for template in templates %}
                                <div class="flex px-4 py-2 hover:bg-gray-50">
                                    <div class="flex-grow">
                                        <div class="text-sm text-gray-700">{{ template.name }}</div>
                                        {% if template.is_default %}<span class="text-xs text-green-600">(Default)</span>{% endif %}
                                    </div>
                                    <div class="flex space-x-2">
                                        <a href="{% url 'financialreport:preview_report' 'expense' 'print' %}?filter={{ filter }}&start_date={{ start_date }}&end_date={{ end_date }}&template={{ template.id }}"
                                           class="text-blue-600 hover:text-blue-800" title="Preview" target="_blank">
                                            <i class="fa-solid fa-eye"></i>
                                        </a>
                                        <a href="{% url 'financialreport:print_report' 'expense' %}?filter={{ filter }}&start_date={{ start_date }}&end_date={{ end_date }}&template={{ template.id }}"
                                           class="text-blue-600 hover:text-blue-800" title="Print" target="_blank">
                                            <i class="fa-solid fa-print"></i>
                                        </a>
                                    </div>
                                </div>
                                {% empty %}
                                <div class="px-4 py-2 text-sm text-gray-500">No templates available</div>
                                {% endfor %}
                                <div class="border-t border-gray-100 mt-1"></div>
                                <a href="{% url 'financialreport:template_list' %}" class="block px-4 py-2 text-sm text-blue-600 hover:bg-gray-100">
                                    <i class="fa-solid fa-cog mr-1"></i> Manage Templates
                                </a>
                            </div>
                        </div>
                    </div>
                    <div class="relative inline-block">
                        <button id="csvExportBtn" class="flex items-center bg-white border border-gray-300 text-gray-700 px-4 py-2 rounded-md hover:bg-gray-50 mr-2 transition-colors no-print"><i class="fa-solid fa-file-csv mr-2 text-green-600"></i> Export CSV</button>
                        <div id="csvTemplateDropdown" class="hidden absolute z-10 mt-2 w-64 rounded-md shadow-lg bg-white ring-1 ring-black ring-opacity-5">
                            <div class="py-1">
                                <div class="px-4 py-2 text-sm text-gray-700 font-medium border-b">Select Template</div>
                                {% for template in templates %}
                                <div class="flex px-4 py-2 hover:bg-gray-50">
                                    <div class="flex-grow">
                                        <div class="text-sm text-gray-700">{{ template.name }}</div>
                                        {% if template.is_default %}<span class="text-xs text-green-600">(Default)</span>{% endif %}
                                    </div>
                                    <div class="flex space-x-2">
                                        <a href="{% url 'financialreport:preview_report' 'expense' 'csv' %}?filter={{ filter }}&start_date={{ start_date }}&end_date={{ end_date }}&template={{ template.id }}"
                                           class="text-blue-600 hover:text-blue-800" title="Preview" target="_blank">
                                            <i class="fa-solid fa-eye"></i>
                                        </a>
                                        <a href="{% url 'financialreport:export_csv' 'expense' %}?filter={{ filter }}&start_date={{ start_date }}&end_date={{ end_date }}&template={{ template.id }}"
                                           class="text-blue-600 hover:text-blue-800" title="Download">
                                            <i class="fa-solid fa-download"></i>
                                        </a>
                                    </div>
                                </div>
                                {% empty %}
                                <div class="px-4 py-2 text-sm text-gray-500">No templates available</div>
                                {% endfor %}
                                <div class="border-t border-gray-100 mt-1"></div>
                                <a href="{% url 'financialreport:template_list' %}" class="block px-4 py-2 text-sm text-blue-600 hover:bg-gray-100">
                                    <i class="fa-solid fa-cog mr-1"></i> Manage Templates
                                </a>
                            </div>
                        </div>
                    </div>
                    <div class="relative inline-block">
                        <button id="pdfExportBtn" class="flex items-center bg-white border border-gray-300 text-gray-700 px-4 py-2 rounded-md hover:bg-gray-50 transition-colors no-print"><i class="fa-solid fa-file-pdf mr-2 text-red-600"></i> Export PDF</button>
                        <div id="templateDropdown" class="hidden absolute z-10 mt-2 w-64 rounded-md shadow-lg bg-white ring-1 ring-black ring-opacity-5">
                            <div class="py-1">
                                <div class="px-4 py-2 text-sm text-gray-700 font-medium border-b">Select Template</div>
                                {% for template in templates %}
                                <div class="flex px-4 py-2 hover:bg-gray-50">
                                    <div class="flex-grow">
                                        <div class="text-sm text-gray-700">{{ template.name }}</div>
                                        {% if template.is_default %}<span class="text-xs text-green-600">(Default)</span>{% endif %}
                                    </div>
                                    <div class="flex space-x-2">
                                        <a href="{% url 'financialreport:preview_report' 'expense' 'pdf' %}?filter={{ filter }}&start_date={{ start_date }}&end_date={{ end_date }}&template={{ template.id }}"
                                           class="text-blue-600 hover:text-blue-800" title="Preview" target="_blank">
                                            <i class="fa-solid fa-eye"></i>
                                        </a>
                                        <a href="{% url 'financialreport:export_pdf' 'expense' %}?filter={{ filter }}&start_date={{ start_date }}&end_date={{ end_date }}&template={{ template.id }}"
                                           class="text-blue-600 hover:text-blue-800" title="Download">
                                            <i class="fa-solid fa-download"></i>
                                        </a>
                                    </div>
                                </div>
                                {% empty %}
                                <div class="px-4 py-2 text-sm text-gray-500">No templates available</div>
                                {% endfor %}
                                <div class="border-t border-gray-100 mt-1"></div>
                                <a href="{% url 'financialreport:template_list' %}" class="block px-4 py-2 text-sm text-blue-600 hover:bg-gray-100">
                                    <i class="fa-solid fa-cog mr-1"></i> Manage Templates
                                </a>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Date Range Selector -->
            <div class="bg-gray-50 p-4 rounded-lg border border-gray-200 no-print">
                <div class="flex flex-wrap items-center mb-3">
                    <span class="text-sm font-medium text-gray-700 mr-3">Period:</span>
                    <div class="flex flex-wrap gap-2">
                        <a href="?filter=today" class="px-4 py-2 rounded-md text-sm font-medium transition-colors {% if filter == 'today' %}bg-red-600 text-white{% else %}bg-white border border-gray-300 text-gray-700 hover:bg-gray-50{% endif %}">Today</a>
                        <a href="?filter=week" class="px-4 py-2 rounded-md text-sm font-medium transition-colors {% if filter == 'week' %}bg-red-600 text-white{% else %}bg-white border border-gray-300 text-gray-700 hover:bg-gray-50{% endif %}">This Week</a>
                        <a href="?filter=month" class="px-4 py-2 rounded-md text-sm font-medium transition-colors {% if filter == 'month' %}bg-red-600 text-white{% else %}bg-white border border-gray-300 text-gray-700 hover:bg-gray-50{% endif %}">This Month</a>
                        <a href="?filter=year" class="px-4 py-2 rounded-md text-sm font-medium transition-colors {% if filter == 'year' %}bg-red-600 text-white{% else %}bg-white border border-gray-300 text-gray-700 hover:bg-gray-50{% endif %}">This Year</a>
                        <button id="custom-range-btn" class="px-4 py-2 rounded-md text-sm font-medium transition-colors {% if filter == 'custom' %}bg-red-600 text-white{% else %}bg-white border border-gray-300 text-gray-700 hover:bg-gray-50{% endif %}">Custom Range</button>
                    </div>
                </div>

                <!-- Custom Date Range Form -->
                <div id="custom-range-form" class="{% if filter != 'custom' %}hidden{% endif %} mt-3 pt-3 border-t border-gray-200">
                    <form method="get" class="flex flex-wrap gap-4 items-end">
                        <input type="hidden" name="filter" value="custom">
                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-1">Start Date</label>
                            <input type="date" name="start_date" value="{{ start_date }}" class="border border-gray-300 rounded-md p-2 focus:ring-red-500 focus:border-red-500">
                        </div>
                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-1">End Date</label>
                            <input type="date" name="end_date" value="{{ end_date }}" class="border border-gray-300 rounded-md p-2 focus:ring-red-500 focus:border-red-500">
                        </div>

                        <div class="flex gap-2">
                            <button type="submit" class="bg-red-600 text-white px-4 py-2 rounded-md hover:bg-red-700 transition-colors">Apply</button>
                            <a href="{% url 'financialreport:expense_report' %}" class="bg-gray-200 text-gray-700 px-4 py-2 rounded-md hover:bg-gray-300 transition-colors">Reset</a>
                        </div>
                    </form>
                </div>

                <div class="mt-3 text-sm text-gray-600">
                    <div class="flex items-center mb-1">
                        <i class="fa-regular fa-calendar mr-2"></i>
                        <span>Showing expense data for: <strong>{{ date_range }}</strong></span>
                    </div>

                </div>
            </div>
        </div>

        <!-- Expense Tabs -->
        <div class="mb-6">
            <div class="bg-white rounded-lg shadow-sm">
                <!-- Tab Navigation -->
                <div class="border-b border-gray-200 no-print">
                    <ul class="flex flex-wrap" id="expenseTabs" role="tablist">
                        <li class="mr-2" role="presentation">
                            <button class="inline-block px-6 py-4 text-red-600 hover:text-red-800 font-medium border-b-2 border-red-600 rounded-t-lg active" id="all-tab" data-tabs-target="#all" type="button" role="tab" aria-controls="all" aria-selected="true">All Expenses</button>
                        </li>
                        <li class="mr-2" role="presentation">
                            <button class="inline-block px-6 py-4 text-gray-500 hover:text-gray-700 font-medium border-b-2 border-transparent hover:border-gray-300 rounded-t-lg" id="salaries-tab" data-tabs-target="#salaries" type="button" role="tab" aria-controls="salaries" aria-selected="false">Salaries</button>
                        </li>
                        <li class="mr-2" role="presentation">
                            <button class="inline-block px-6 py-4 text-gray-500 hover:text-gray-700 font-medium border-b-2 border-transparent hover:border-gray-300 rounded-t-lg" id="bills-tab" data-tabs-target="#bills" type="button" role="tab" aria-controls="bills" aria-selected="false">Bills</button>
                        </li>
                        <li class="mr-2" role="presentation">
                            <button class="inline-block px-6 py-4 text-gray-500 hover:text-gray-700 font-medium border-b-2 border-transparent hover:border-gray-300 rounded-t-lg" id="purchases-tab" data-tabs-target="#purchases" type="button" role="tab" aria-controls="purchases" aria-selected="false">Product Purchases</button>
                        </li>
                        <li class="mr-2" role="presentation">
                            <button class="inline-block px-6 py-4 text-gray-500 hover:text-gray-700 font-medium border-b-2 border-transparent hover:border-gray-300 rounded-t-lg" id="withdrawals-tab" data-tabs-target="#withdrawals" type="button" role="tab" aria-controls="withdrawals" aria-selected="false">Withdrawals</button>
                        </li>
                    </ul>
                </div>

                <div id="expenseTabContent">
                    <!-- All Expenses Tab -->
                    <div class="p-6" id="all" role="tabpanel" aria-labelledby="all-tab">
                        <div class="flex justify-between items-center mb-4">
                            <h3 class="text-xl font-bold text-gray-800">All Expense Categories</h3>
                            <div class="text-sm text-gray-500">
                                <span class="font-medium">Total:</span>
                                {{ total_expenses|format_khr }}
                            </div>
                        </div>
                        <div class="overflow-x-auto bg-white rounded-lg border border-gray-200">
                            <table class="w-full text-sm text-left">
                                <thead><tr class="bg-gray-50 text-xs text-gray-700 uppercase">
                                        <th scope="col" class="px-6 py-3 font-medium">Date</th>
                                        <th scope="col" class="px-6 py-3 font-medium">Category</th>
                                        <th scope="col" class="px-6 py-3 font-medium">Description</th>
                                        <th scope="col" class="px-6 py-3 font-medium">Amount (KHR)</th>
                                        <th scope="col" class="px-6 py-3 font-medium">Payment Method</th>
                                        <th scope="col" class="px-6 py-3 font-medium">Paid By</th>
                                    </tr>
                                </thead>
                                <tbody class="divide-y divide-gray-200">
                                    {% for payment in salary_payments %}
                                    <tr class="hover:bg-gray-50">
                                        <td class="px-6 py-4 whitespace-nowrap">{{ payment.payment_date|date:"d-M-Y" }}</td>
                                        <td class="px-6 py-4">
                                            <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-red-100 text-red-800">Salary</span>
                                        </td>
                                        <td class="px-6 py-4">{{ payment.employee.name }} - {{ payment.payroll_id }}</td>
                                        <td class="px-6 py-4 font-medium text-gray-900">{{ payment.final_pay|format_khr }}</td>
                                        <td class="px-6 py-4">N/A</td>
                                        <td class="px-6 py-4">Admin</td>
                                    </tr>
                                    {% endfor %}

                                    {% for bill in bill_payments %}
                                    <tr class="hover:bg-gray-50">
                                        <td class="px-6 py-4 whitespace-nowrap">{{ bill.payment_date|date:"d-M-Y" }}</td>
                                        <td class="px-6 py-4">
                                            <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-orange-100 text-orange-800">
                                                {{ bill.get_category_display }}
                                            </span>
                                        </td>
                                        <td class="px-6 py-4">{{ bill.provider }} - {{ bill.bill_id }}</td>
                                        <td class="px-6 py-4 font-medium text-gray-900">{{ bill.amount_khr|format_khr }}</td>
                                        <td class="px-6 py-4">{{ bill.get_payment_method_display }}</td>
                                        <td class="px-6 py-4">{{ bill.paid_by.username }}</td>
                                    </tr>
                                    {% endfor %}

                                    {% for purchase in product_purchases %}
                                    <tr class="hover:bg-gray-50">
                                        <td class="px-6 py-4 whitespace-nowrap">{{ purchase.date|date:"d-M-Y" }}</td>
                                        <td class="px-6 py-4">
                                            <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-yellow-100 text-yellow-800">Product Purchase</span>
                                        </td>
                                        <td class="px-6 py-4">{{ purchase.supplier.name|default:"Unknown" }} - {{ purchase.trxId }}</td>
                                        <td class="px-6 py-4 font-medium text-gray-900">{{ purchase.total_amount|format_khr }}</td>
                                        <td class="px-6 py-4">N/A</td>
                                        <td class="px-6 py-4">{{ purchase.created_by.username|default:"N/A" }}</td>
                                    </tr>
                                    {% endfor %}

                                    {% for withdrawal in withdrawals %}
                                    <tr class="hover:bg-gray-50">
                                        <td class="px-6 py-4 whitespace-nowrap">{{ withdrawal.transaction_date|date:"d-M-Y" }}</td>
                                        <td class="px-6 py-4">
                                            <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-blue-100 text-blue-800">Withdrawal</span>
                                        </td>
                                        <td class="px-6 py-4">{{ withdrawal.description|default:"Cash Withdrawal" }} - {{ withdrawal.transaction_id }}</td>
                                        <td class="px-6 py-4 font-medium text-gray-900">{{ withdrawal.amount_khr|format_khr }}</td>
                                        <td class="px-6 py-4">{{ withdrawal.get_payment_method_display }}</td>
                                        <td class="px-6 py-4">{{ withdrawal.staff.username }}</td>
                                    </tr>
                                    {% endfor %}

                                    {% if not salary_payments and not bill_payments and not product_purchases and not withdrawals %}
                                    <tr>
                                        <td colspan="6" class="px-6 py-10 text-center text-gray-500">
                                            <div class="flex flex-col items-center">
                                                <i class="fa-solid fa-file-circle-exclamation text-gray-400 text-4xl mb-3"></i>
                                                <p class="text-lg font-medium">No expense data found</p>
                                                <p class="text-sm">Try changing the date range or check back later</p>
                                            </div>
                                        </td>
                                    </tr>
                                    {% endif %}
                                </tbody>
                            </table>
                        </div>
                    </div>

                    <!-- Salaries Tab -->
                    <div class="hidden p-6" id="salaries" role="tabpanel" aria-labelledby="salaries-tab">
                        <div class="flex justify-between items-center mb-4">
                            <h3 class="text-xl font-bold text-gray-800">Salary Expenses</h3>
                            <div class="text-sm text-gray-500">
                                <span class="font-medium">Total:</span>
                                {{ salary_total|format_khr }}
                            </div>
                        </div>
                        <div class="overflow-x-auto bg-white rounded-lg border border-gray-200">
                            <table class="w-full text-sm text-left">
                                <thead><tr class="bg-gray-50 text-xs text-gray-700 uppercase">
                                        <th scope="col" class="px-6 py-3 font-medium">Date</th>
                                        <th scope="col" class="px-6 py-3 font-medium">Payroll ID</th>
                                        <th scope="col" class="px-6 py-3 font-medium">Employee</th>
                                        <th scope="col" class="px-6 py-3 font-medium">Base Salary</th>
                                        <th scope="col" class="px-6 py-3 font-medium">Bonus</th>
                                        <th scope="col" class="px-6 py-3 font-medium">Deduction</th>
                                        <th scope="col" class="px-6 py-3 font-medium">Final Pay</th>
                                    </tr>
                                </thead>
                                <tbody class="divide-y divide-gray-200">
                                    {% for payment in salary_payments %}
                                    <tr class="hover:bg-gray-50">
                                        <td class="px-6 py-4 whitespace-nowrap">{{ payment.payment_date|date:"d-M-Y" }}</td>
                                        <td class="px-6 py-4">{{ payment.payroll_id }}</td>
                                        <td class="px-6 py-4">{{ payment.employee.name }}</td>
                                        <td class="px-6 py-4">{{ payment.base_salary|format_khr }}</td>
                                        <td class="px-6 py-4">{{ payment.bonus|format_khr }}</td>
                                        <td class="px-6 py-4">{{ payment.deduction|format_khr }}</td>
                                        <td class="px-6 py-4 font-medium text-gray-900">{{ payment.final_pay|format_khr }}</td>
                                    </tr>
                                    {% empty %}
                                    <tr>
                                        <td colspan="7" class="px-6 py-10 text-center text-gray-500">
                                            <div class="flex flex-col items-center">
                                                <i class="fa-solid fa-file-circle-exclamation text-gray-400 text-4xl mb-3"></i>
                                                <p class="text-lg font-medium">No salary payments found</p>
                                                <p class="text-sm">Try changing the date range or check back later</p>
                                            </div>
                                        </td>
                                    </tr>
                                    {% endfor %}
                                </tbody>
                            </table>
                        </div>
                    </div>

                    <!-- Bills Tab -->
                    <div class="hidden p-6" id="bills" role="tabpanel" aria-labelledby="bills-tab">
                        <div class="flex justify-between items-center mb-4">
                            <h3 class="text-xl font-bold text-gray-800">Bill Expenses</h3>
                            <div class="text-sm text-gray-500">
                                <span class="font-medium">Total:</span>
                                {{ bill_total|format_khr }}
                            </div>
                        </div>
                        <div class="overflow-x-auto bg-white rounded-lg border border-gray-200">
                            <table class="w-full text-sm text-left">
                                <thead><tr class="bg-gray-50 text-xs text-gray-700 uppercase">
                                        <th scope="col" class="px-6 py-3 font-medium">Date</th>
                                        <th scope="col" class="px-6 py-3 font-medium">Bill ID</th>
                                        <th scope="col" class="px-6 py-3 font-medium">Category</th>
                                        <th scope="col" class="px-6 py-3 font-medium">Provider</th>
                                        <th scope="col" class="px-6 py-3 font-medium">Amount (KHR)</th>
                                        <th scope="col" class="px-6 py-3 font-medium">Payment Method</th>
                                        <th scope="col" class="px-6 py-3 font-medium">Paid By</th>
                                    </tr>
                                </thead>
                                <tbody class="divide-y divide-gray-200">
                                    {% for bill in bill_payments %}
                                    <tr class="hover:bg-gray-50">
                                        <td class="px-6 py-4 whitespace-nowrap">{{ bill.payment_date|date:"d-M-Y" }}</td>
                                        <td class="px-6 py-4">{{ bill.bill_id }}</td>
                                        <td class="px-6 py-4">
                                            <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-orange-100 text-orange-800">
                                                {{ bill.get_category_display }}
                                            </span>
                                        </td>
                                        <td class="px-6 py-4">{{ bill.provider }}</td>
                                        <td class="px-6 py-4 font-medium text-gray-900">{{ bill.amount_khr|format_khr }}</td>
                                        <td class="px-6 py-4">{{ bill.get_payment_method_display }}</td>
                                        <td class="px-6 py-4">{{ bill.paid_by.username }}</td>
                                    </tr>
                                    {% empty %}
                                    <tr>
                                        <td colspan="7" class="px-6 py-10 text-center text-gray-500">
                                            <div class="flex flex-col items-center">
                                                <i class="fa-solid fa-file-circle-exclamation text-gray-400 text-4xl mb-3"></i>
                                                <p class="text-lg font-medium">No bill payments found</p>
                                                <p class="text-sm">Try changing the date range or check back later</p>
                                            </div>
                                        </td>
                                    </tr>
                                    {% endfor %}
                                </tbody>
                            </table>
                        </div>
                    </div>

                    <!-- Product Purchases Tab -->
                    <div class="hidden p-6" id="purchases" role="tabpanel" aria-labelledby="purchases-tab">
                        <div class="flex justify-between items-center mb-4">
                            <h3 class="text-xl font-bold text-gray-800">Product Purchase Expenses</h3>
                            <div class="text-sm text-gray-500">
                                <span class="font-medium">Total:</span>
                                {{ purchase_total|format_khr }}
                            </div>
                        </div>
                        <div class="overflow-x-auto bg-white rounded-lg border border-gray-200">
                            <table class="w-full text-sm text-left">
                                <thead><tr class="bg-gray-50 text-xs text-gray-700 uppercase">
                                        <th scope="col" class="px-6 py-3 font-medium">Date</th>
                                        <th scope="col" class="px-6 py-3 font-medium">Transaction ID</th>
                                        <th scope="col" class="px-6 py-3 font-medium">Supplier</th>
                                        <th scope="col" class="px-6 py-3 font-medium">Amount (KHR)</th>
                                        <th scope="col" class="px-6 py-3 font-medium">Created By</th>
                                        <th scope="col" class="px-6 py-3 font-medium">Notes</th>
                                    </tr>
                                </thead>
                                <tbody class="divide-y divide-gray-200">
                                    {% for purchase in product_purchases %}
                                    <tr class="hover:bg-gray-50">
                                        <td class="px-6 py-4 whitespace-nowrap">{{ purchase.date|date:"d-M-Y" }}</td>
                                        <td class="px-6 py-4">{{ purchase.trxId }}</td>
                                        <td class="px-6 py-4">{{ purchase.supplier.name|default:"Unknown" }}</td>
                                        <td class="px-6 py-4 font-medium text-gray-900">{{ purchase.total_amount|format_khr }}</td>
                                        <td class="px-6 py-4">{{ purchase.created_by.username|default:"N/A" }}</td>
                                        <td class="px-6 py-4">{{ purchase.notes|default:"-"|truncatechars:30 }}</td>
                                    </tr>
                                    {% empty %}
                                    <tr>
                                        <td colspan="6" class="px-6 py-10 text-center text-gray-500">
                                            <div class="flex flex-col items-center">
                                                <i class="fa-solid fa-file-circle-exclamation text-gray-400 text-4xl mb-3"></i>
                                                <p class="text-lg font-medium">No product purchases found</p>
                                                <p class="text-sm">Try changing the date range or check back later</p>
                                            </div>
                                        </td>
                                    </tr>
                                    {% endfor %}
                                </tbody>
                            </table>
                        </div>
                    </div>

                    <!-- Withdrawals Tab -->
                    <div class="hidden p-6" id="withdrawals" role="tabpanel" aria-labelledby="withdrawals-tab">
                        <div class="flex justify-between items-center mb-4">
                            <h3 class="text-xl font-bold text-gray-800">Withdrawals</h3>
                            <div class="text-sm text-gray-500">
                                <span class="font-medium">Total:</span>
                                {{ withdrawal_total|format_khr }}
                            </div>
                        </div>
                        <div class="overflow-x-auto bg-white rounded-lg border border-gray-200">
                            <table class="w-full text-sm text-left">
                                <thead><tr class="bg-gray-50 text-xs text-gray-700 uppercase">
                                        <th scope="col" class="px-6 py-3 font-medium">Date</th>
                                        <th scope="col" class="px-6 py-3 font-medium">Transaction ID</th>
                                        <th scope="col" class="px-6 py-3 font-medium">Description</th>
                                        <th scope="col" class="px-6 py-3 font-medium">Amount (KHR)</th>
                                        <th scope="col" class="px-6 py-3 font-medium">Payment Method</th>
                                        <th scope="col" class="px-6 py-3 font-medium">Created By</th>
                                    </tr>
                                </thead>
                                <tbody class="divide-y divide-gray-200">
                                    {% for withdrawal in withdrawals %}
                                    <tr class="hover:bg-gray-50">
                                        <td class="px-6 py-4 whitespace-nowrap">{{ withdrawal.transaction_date|date:"d-M-Y" }}</td>
                                        <td class="px-6 py-4">{{ withdrawal.transaction_id }}</td>
                                        <td class="px-6 py-4">{{ withdrawal.description|default:"Cash Withdrawal" }}</td>
                                        <td class="px-6 py-4 font-medium text-gray-900">{{ withdrawal.amount_khr|format_khr }}</td>
                                        <td class="px-6 py-4">{{ withdrawal.get_payment_method_display }}</td>
                                        <td class="px-6 py-4">{{ withdrawal.staff.username }}</td>
                                    </tr>
                                    {% empty %}
                                    <tr>
                                        <td colspan="6" class="px-6 py-10 text-center text-gray-500">
                                            <div class="flex flex-col items-center">
                                                <i class="fa-solid fa-file-circle-exclamation text-gray-400 text-4xl mb-3"></i>
                                                <p class="text-lg font-medium">No withdrawals found</p>
                                                <p class="text-sm">Try changing the date range or check back later</p>
                                            </div>
                                        </td>
                                    </tr>
                                    {% endfor %}
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>
                </div>

                <!-- Print Footer - Only visible when printing -->
                <div class="hidden print:block print-footer">
                    <p>Generated on {% now "d-M-Y H:i" %} | Legend Fitness Club</p>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block js %}
<script>
    document.addEventListener('DOMContentLoaded', function() {
        // Toggle custom date range form
        const customRangeBtn = document.getElementById('custom-range-btn');
        const customRangeForm = document.getElementById('custom-range-form');

        if (customRangeBtn) {
            customRangeBtn.addEventListener('click', function() {
                customRangeForm.classList.toggle('hidden');
            });
        }

        // Toggle PDF template dropdown
        const pdfExportBtn = document.getElementById('pdfExportBtn');
        const templateDropdown = document.getElementById('templateDropdown');

        if (pdfExportBtn && templateDropdown) {
            pdfExportBtn.addEventListener('click', function(e) {
                e.preventDefault();
                templateDropdown.classList.toggle('hidden');
                if (printTemplateDropdown) printTemplateDropdown.classList.add('hidden');
                if (csvTemplateDropdown) csvTemplateDropdown.classList.add('hidden');
            });

            // Close dropdown when clicking outside
            document.addEventListener('click', function(e) {
                if (!pdfExportBtn.contains(e.target) && !templateDropdown.contains(e.target)) {
                    templateDropdown.classList.add('hidden');
                }
            });
        }

        // Toggle Print template dropdown
        const printReportBtn = document.getElementById('printReportBtn');
        const printTemplateDropdown = document.getElementById('printTemplateDropdown');

        if (printReportBtn && printTemplateDropdown) {
            printReportBtn.addEventListener('click', function(e) {
                e.preventDefault();
                printTemplateDropdown.classList.toggle('hidden');
                if (templateDropdown) templateDropdown.classList.add('hidden');
                if (csvTemplateDropdown) csvTemplateDropdown.classList.add('hidden');
            });

            // Close dropdown when clicking outside
            document.addEventListener('click', function(e) {
                if (!printReportBtn.contains(e.target) && !printTemplateDropdown.contains(e.target)) {
                    printTemplateDropdown.classList.add('hidden');
                }
            });
        }

        // Toggle CSV template dropdown
        const csvExportBtn = document.getElementById('csvExportBtn');
        const csvTemplateDropdown = document.getElementById('csvTemplateDropdown');

        if (csvExportBtn && csvTemplateDropdown) {
            csvExportBtn.addEventListener('click', function(e) {
                e.preventDefault();
                csvTemplateDropdown.classList.toggle('hidden');
                if (templateDropdown) templateDropdown.classList.add('hidden');
                if (printTemplateDropdown) printTemplateDropdown.classList.add('hidden');
            });

            // Close dropdown when clicking outside
            document.addEventListener('click', function(e) {
                if (!csvExportBtn.contains(e.target) && !csvTemplateDropdown.contains(e.target)) {
                    csvTemplateDropdown.classList.add('hidden');
                }
            });
        }

        // Enhanced tabs functionality
        const tabs = document.querySelectorAll('[data-tabs-target]');
        const tabContents = document.querySelectorAll('[role="tabpanel"]');

        tabs.forEach(tab => {
            tab.addEventListener('click', () => {
                const target = document.querySelector(tab.dataset.tabsTarget);

                // Hide all tab contents
                tabContents.forEach(tabContent => {
                    tabContent.classList.add('hidden');
                });

                // Reset all tab styles
                tabs.forEach(tab => {
                    tab.classList.remove('active', 'text-red-600', 'border-red-600');
                    tab.classList.add('text-gray-500', 'border-transparent');
                    tab.setAttribute('aria-selected', 'false');
                });

                // Set active tab styles
                tab.classList.add('active', 'text-red-600', 'border-red-600');
                tab.classList.remove('text-gray-500', 'border-transparent');
                tab.setAttribute('aria-selected', 'true');

                // Show selected tab content
                target.classList.remove('hidden');

                // Smooth scroll to top of tab content on mobile
                if (window.innerWidth < 768) {
                    const tabsContainer = document.querySelector('.bg-white.rounded-lg.shadow-sm');
                    if (tabsContainer) {
                        tabsContainer.scrollIntoView({ behavior: 'smooth' });
                    }
                }
            });
        });
    });
</script>
{% endblock %}
