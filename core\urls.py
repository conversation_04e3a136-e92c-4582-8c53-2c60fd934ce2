from django.contrib import admin
from django.urls import path, re_path
from django.conf import settings
from django.conf.urls.static import static
from django.views.static import serve
from . import views
from decorator_include import decorator_include
from .decorators import module_permission_required
import os

urlpatterns = [
    # Language switching URL
    path('set-language/<str:language_code>/', views.set_language, name='set_language'),

    # Admin and authentication URLs
    path('admin/', admin.site.urls),
    path('', views.index , name='login'),
    path('logout/', views.logout, name='logout'),

    path('adminDashboard/', views.adminDashboard, name='adminDashboard'),
    path('employeeDashboard/', views.employeeDashboard, name='employeeDashboard'),

    # Dashboard API endpoints
    path('api/dashboard/paypervisit-report/', views.api_paypervisit_report, name='api_paypervisit_report'),
    path('api/dashboard/product-sales-report/', views.api_product_sales_report, name='api_product_sales_report'),
    path('api/dashboard/overview-report/', views.api_overview_report, name='api_overview_report'),
    path('api/dashboard/income-report/', views.api_income_report, name='api_income_report'),
    path('api/dashboard/expense-report/', views.api_expense_report, name='api_expense_report'),
    path('api/dashboard/balance-report/', views.api_balance_report, name='api_balance_report'),

    # Test dashboard
    path('test-dashboard/', views.test_dashboard, name='test_dashboard'),
    path('test-period-selection/', views.test_period_selection, name='test_period_selection'),



    # Employee management - Admin only
    path('user/', decorator_include(module_permission_required(module='user', required_level='view'),"user.urls",namespace="user")),

    # Member management - Based on permissions
    path('member/', decorator_include(module_permission_required(module='member', required_level='view'),"members.urls",namespace="member")),

    # Payment management - Based on permissions
    path('payment/', decorator_include(module_permission_required(module='payment', required_level='view'),"payment.urls",namespace="payment")),

    # Pay-per-visit - Based on permissions
    path('paypervisit/', decorator_include(module_permission_required(module='paypervisit', required_level='view'),"paypervisit.urls",namespace="paypervisit")),

    # Product management - Based on permissions
    path('product/', decorator_include(module_permission_required(module='product', required_level='view'),"product.urls",namespace="product")),

    # Payroll management - Based on permissions
    path('payroll/', decorator_include(module_permission_required(module='payroll', required_level='view'),"payroll.urls",namespace="payroll")),

    # Bill management - Based on permissions
    path('bill/', decorator_include(module_permission_required(module='bill', required_level='view'),"billmanagement.urls",namespace="billmanagement")),

    # Financial Reports - Based on permissions
    path('financialreport/', decorator_include(module_permission_required(module='financialreport', required_level='view'),"financialreport.urls",namespace="financialreport")),

    # Finance management - Based on permissions
    path('finance/', decorator_include(module_permission_required(module='finance', required_level='view'),"finance.urls",namespace="finance")),

    # Settings - Based on permissions
    path('settings/', decorator_include(module_permission_required(module='settings', required_level='view'),"settings.urls",namespace="settings")),

    # Data cleaning - Admin only (legacy route, now handled in settings)
    path('clean-data/', module_permission_required(module='settings', required_level='full')(views.clean_all_data), name='clean_all_data'),

    # Test currency formatting
    path('test-currency-formatting/', views.test_currency_formatting, name='test_currency_formatting'),
    path('test-js-currency-formatting/', views.test_js_currency_formatting, name='test_js_currency_formatting'),

    # WebSocket testing
    path('test-websocket/', views.test_websocket, name='test_websocket'),
    path('test-permission-update/', views.test_permission_update, name='test_permission_update'),
    path('test-notification/', views.test_notification, name='test_notification')

] + static(settings.MEDIA_URL, document_root=settings.MEDIA_ROOT)

# Serve backup files
backup_dir = os.path.join(settings.BASE_DIR, 'backups')
urlpatterns += [
    re_path(r'^backups/(?P<path>.*)$', serve, {
        'document_root': backup_dir,
    }),
]
