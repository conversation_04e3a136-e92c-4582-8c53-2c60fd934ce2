{% extends 'base.html' %}

{% load static %}
{% load permission_tags %}
{% load currency_filters %}



{% block extra_css %}
<link rel="stylesheet" href="{% static 'css/paypervisit.css' %}">
<style>
    /* Responsive table improvements */
    @media (max-width: 768px) {
        .paypervisit-table th:nth-child(4),
        .paypervisit-table td:nth-child(4) {
            display: none; /* Hide Date & Time column on mobile */
        }

        .paypervisit-table th:nth-child(5),
        .paypervisit-table td:nth-child(5) {
            display: none; /* Hide Cashier column on mobile */
        }

        .delete-paypervisit-btn {
            padding: 0.25rem 0.5rem !important;
            font-size: 0.625rem !important;
        }

        .delete-paypervisit-btn i {
            margin-right: 0 !important;
        }

        .delete-paypervisit-btn .btn-text {
            display: none;
        }
    }

    @media (max-width: 640px) {
        .paypervisit-table th:nth-child(2),
        .paypervisit-table td:nth-child(2) {
            display: none; /* Hide Visitors column on small mobile */
        }
    }
</style>
{% endblock %}

{% block body %}
<!-- Hidden CSRF token for JavaScript -->
<input type="hidden" name="csrfmiddlewaretoken" value="{{ csrf_token }}">

<div class="conponentSection p-5 bg-gray-200">
    <div class="componentWrapper">
        <!-- Header with Back Button -->
        <div class="flex justify-between items-center mb-4">
            <div class="flex items-center">
                <a href="{% url 'paypervisit:index' %}" class="bg-blue-900 text-white p-2 rounded-full hover:bg-blue-800 mr-3">
                    <i class="fas fa-arrow-left"></i>
                </a>
                <h2 class="text-2xl font-bold">Pay-per-visit Transaction History</h2>
            </div>
        </div>

        <!-- Filter Form -->
        <div class="bg-white p-4 rounded shadow-md mb-4">
            <h3 class="text-lg font-semibold mb-3">Filter Transactions</h3>
            <form method="get" class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
                <!-- Date Filters -->
                <div>
                    <label class="block text-sm font-medium mb-1">From Date</label>
                    <input type="date" name="start_date" class="border w-full p-2 leading-tight bg-slate-100 rounded"
                           value="{{ start_date }}">
                </div>
                <div>
                    <label class="block text-sm font-medium mb-1">To Date</label>
                    <input type="date" name="end_date" class="border w-full p-2 leading-tight bg-slate-100 rounded"
                           value="{{ end_date }}">
                </div>

                <!-- Payment Method Filter -->
                <div>
                    <label class="block text-sm font-medium mb-1">Payment Method</label>
                    <select name="payment_method" class="border w-full p-2 leading-tight bg-slate-100 rounded">
                        <option value="">All Payment Methods</option>
                        {% for value, display in payment_methods %}
                        <option value="{{ value }}" {% if payment_method_filter == value %}selected{% endif %}>{{ display }}</option>
                        {% endfor %}
                    </select>
                </div>

                <!-- Cashier Filter -->
                <div>
                    <label class="block text-sm font-medium mb-1">Cashier</label>
                    <select name="cashier" class="border w-full p-2 leading-tight bg-slate-100 rounded">
                        <option value="">All Cashiers</option>
                        {% for cashier in cashiers %}
                        <option value="{{ cashier.id }}" {% if cashier_filter == cashier.id|stringformat:"s" %}selected{% endif %}>{{ cashier.username }}</option>
                        {% endfor %}
                    </select>
                </div>

                <!-- Filter Buttons -->
                <div class="md:col-span-2 lg:col-span-4 flex items-center justify-end">
                    <button type="submit" class="bg-blue-900 hover:bg-blue-800 text-white font-bold py-2 px-4 rounded transition duration-200">
                        <i class="fas fa-filter mr-1"></i> Apply Filters
                    </button>
                    {% if filter_active %}
                    <a href="{% url 'paypervisit:transaction' %}" class="ml-2 bg-gray-500 hover:bg-gray-600 text-white font-bold py-2 px-4 rounded transition duration-200">
                        <i class="fas fa-redo mr-1"></i> Reset
                    </a>
                    {% endif %}
                </div>
            </form>
        </div>

        <!-- Summary Cards -->
        {% if transactions %}
        <div class="grid grid-cols-1 md:grid-cols-3 gap-4 mb-4">
            <div class="bg-gradient-to-r from-blue-800 to-blue-900 text-white p-4 rounded-lg shadow">
                <div class="flex items-center">
                    <div class="rounded-full bg-blue-700 p-3 mr-4">
                        <i class="fas fa-users text-xl"></i>
                    </div>
                    <div>
                        <p class="text-sm text-blue-200">Total Visitors</p>
                        <p class="text-2xl font-bold">{{ total_people }}</p>
                    </div>
                </div>
            </div>
            <div class="bg-gradient-to-r from-green-700 to-green-800 text-white p-4 rounded-lg shadow">
                <div class="flex items-center">
                    <div class="rounded-full bg-green-600 p-3 mr-4">
                        <i class="fas fa-money-bill-wave text-xl"></i>
                    </div>
                    <div>
                        <p class="text-sm text-green-200">Total Revenue</p>
                        <p class="text-2xl font-bold">{{ total_amount|format_khr }}</p>
                    </div>
                </div>
            </div>
            <div class="bg-gradient-to-r from-purple-700 to-purple-800 text-white p-4 rounded-lg shadow">
                <div class="flex items-center">
                    <div class="rounded-full bg-purple-600 p-3 mr-4">
                        <i class="fas fa-receipt text-xl"></i>
                    </div>
                    <div>
                        <p class="text-sm text-purple-200">Transactions</p>
                        <p class="text-2xl font-bold">{{ transaction_count }}</p>
                    </div>
                </div>
            </div>
        </div>
        {% endif %}

        <!-- Transactions Table -->
        <div class="bg-white rounded-lg border shadow-sm overflow-hidden">
            <div class="relative overflow-x-auto">
                <table class="w-full text-sm text-left paypervisit-table">
                    <thead class="text-xs uppercase bg-blue-900 text-gray-50">
                        <tr>
                            <th scope="col" class="px-6 py-3">Transaction ID</th>
                            <th scope="col" class="px-6 py-3">Visitors</th>
                            <th scope="col" class="px-6 py-3">Amount</th>
                            <th scope="col" class="px-6 py-3">Date & Time</th>
                            <th scope="col" class="px-6 py-3">Cashier</th>
                            <th scope="col" class="px-6 py-3">Actions</th>
                        </tr>
                    </thead>
                    <tbody>
                        {% for transaction in transactions %}
                        <tr class="bg-white border-b hover:bg-gray-50">
                            <td class="px-6 py-4 font-medium">{{ transaction.trxId }}</td>
                            <td class="px-6 py-4">
                                <span class="flex items-center">
                                    <i class="fas fa-users text-blue-700 mr-2"></i>
                                    {{ transaction.num_people }}
                                </span>
                            </td>
                            <td class="px-6 py-4 font-semibold">{{ transaction.amount|format_khr }}</td>
                            <td class="px-6 py-4">{{ transaction.date|date:"M d, Y H:i" }}</td>
                            <td class="px-6 py-4">
                                <span class="flex items-center">
                                    <i class="fas fa-user-circle text-gray-500 mr-2"></i>
                                    {{ transaction.received_by.username }}
                                </span>
                            </td>
                            <td class="px-6 py-4">
                                <div class="flex items-center space-x-2">
                                    <a href="{% url 'paypervisit:print_receipt' transaction.id %}" class="text-blue-600 hover:text-blue-800 flex items-center">
                                        <i class="fas fa-print mr-1"></i> Print
                                    </a>
                                    {% has_permission user 'paypervisit' 'full' as can_delete_paypervisit %}
                                    {% if can_delete_paypervisit %}
                                    <button
                                        type="button"
                                        class="bg-red-600 hover:bg-red-700 text-white font-medium py-1 px-3 rounded text-xs transition duration-200 delete-paypervisit-btn"
                                        data-transaction-id="{{ transaction.id }}"
                                        data-transaction-trx="{{ transaction.trxId }}"
                                        data-transaction-amount="{{ transaction.amount|format_khr }}"
                                        data-transaction-people="{{ transaction.num_people }}"
                                        data-transaction-cashier="{{ transaction.received_by.username|default:'Unknown' }}"
                                        title="Delete Transaction">
                                        <i class="fas fa-trash mr-1"></i><span class="btn-text">Delete</span>
                                    </button>
                                    {% else %}
                                    <span class="text-gray-400 text-xs">No Access</span>
                                    {% endif %}
                                </div>
                            </td>
                        </tr>
                        {% empty %}
                        <tr class="bg-white border-b">
                            <td colspan="6" class="px-6 py-8 text-center text-gray-500">
                                <i class="fas fa-receipt text-5xl mb-3 block opacity-30"></i>
                                {% if filter_active %}
                                <p>No pay-per-visit payments found for the selected filters.</p>
                                {% else %}
                                <p>No pay-per-visit payments recorded yet.</p>
                                {% endif %}
                            </td>
                        </tr>
                        {% endfor %}
                    </tbody>
                </table>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block js %}
<script>
    document.addEventListener('DOMContentLoaded', function() {
        // Delete pay-per-visit transaction functionality with confirmation
        document.querySelectorAll('.delete-paypervisit-btn').forEach(button => {
            button.addEventListener('click', function() {
                const transactionId = this.getAttribute('data-transaction-id');
                const transactionTrx = this.getAttribute('data-transaction-trx');
                const transactionAmount = this.getAttribute('data-transaction-amount');
                const transactionPeople = this.getAttribute('data-transaction-people');
                const cashier = this.getAttribute('data-transaction-cashier');

                // Use the visitor/visitors text based on the number of people
                const visitorText = transactionPeople == 1 ? "visitor" : "visitors";

                // Create confirmation dialog
                const confirmMessage = `Are you sure you want to delete this pay-per-visit transaction?\n\n` +
                    `Transaction ID: ${transactionTrx}\n` +
                    `Amount: ${transactionAmount}\n` +
                    `Visitors: ${transactionPeople} ${visitorText}\n` +
                    `Processed by: ${cashier}\n\n` +
                    `This action cannot be undone. The transaction amount will be deducted from gym funds.`;

                if (confirm(confirmMessage)) {
                    // Create a form and submit it to delete the transaction
                    const form = document.createElement('form');
                    form.method = 'POST';
                    form.action = `/paypervisit/transaction/delete/${transactionId}/`;

                    // Add CSRF token
                    const csrfToken = document.querySelector('[name=csrfmiddlewaretoken]').value;
                    const csrfInput = document.createElement('input');
                    csrfInput.type = 'hidden';
                    csrfInput.name = 'csrfmiddlewaretoken';
                    csrfInput.value = csrfToken;
                    form.appendChild(csrfInput);

                    // Submit the form
                    document.body.appendChild(form);
                    form.submit();
                }
            });
        });
    });
</script>
{% endblock %}
