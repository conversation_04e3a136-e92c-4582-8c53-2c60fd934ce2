{% extends "../base.html" %}
{% load custom_filters %}



{% block body %}
<!-- component sections starts  -->
<div class="conponentSection p-5 bg-gray-200">
  <div class="componentWrapper">
    <!-- Page header with breadcrumbs -->
    <div class="mb-6">
      <h1 class="text-3xl font-bold text-gray-800">System Access Registration</h1>
      <div class="flex items-center text-sm mt-2">
        <a href="{% url 'adminDashboard' %}" class="text-gray-500 hover:text-blue-600 transition-colors">
          <i class="fas fa-home mr-1"></i>Dashboard
        </a>
        <span class="mx-2 text-gray-400">/</span>
        <a href="{% url 'user:index' %}" class="text-gray-500 hover:text-blue-600 transition-colors">
          <i class="fas fa-users mr-1"></i>User Management
        </a>
        <span class="mx-2 text-gray-400">/</span>
        <span class="text-blue-600 font-medium">System Access</span>
      </div>
    </div>

    <!-- Action buttons -->
    <div class="flex justify-end mb-6">
      <a href="{% url 'user:index' %}" class="bg-gray-600 hover:bg-gray-700 text-white font-medium py-2 px-4 rounded flex items-center justify-center transition-colors">
        <i class="fa-solid fa-arrow-left mr-2"></i>Back to User Management
      </a>
    </div>

    <!-- Form card -->
    <div class="bg-white rounded-lg shadow-md overflow-hidden mb-6">
      <div class="border-b border-gray-200 px-6 py-4">
        <h2 class="text-xl font-semibold text-gray-800">System Access Registration</h2>
        <p class="text-gray-600 text-sm mt-1">Create system access accounts for employees who need to log in to the system.</p>
      </div>

      <form class="p-6" method="post">
        {% csrf_token %}

        <!-- Form sections with clear visual grouping -->
        <div class="mb-6">
          <h3 class="text-lg font-medium text-gray-700 mb-4 pb-2 border-b border-gray-200">Employee Selection</h3>
          <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
            <!-- Select Employee -->
            <div class="mb-4">
              <label for="employee" class="block text-sm font-medium text-gray-700 mb-1">Select Employee</label>
              <select name="employee" id="employee" class="border border-gray-300 rounded-md w-full p-3 bg-gray-50 focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-all" required>
                <option value="" hidden>Select Employee</option>
                {% for employee in employees_without_access %}
                  <option value="{{ employee.id }}">"{{ employee.name }} ({{ employee.role|default:"No role" }})"</option>
                {% endfor %}
              </select>
            </div>

            <!-- Role -->
            <div class="mb-4">
              <label for="role" class="block text-sm font-medium text-gray-700 mb-1">System Role</label>
              <select name="role" id="role" class="border border-gray-300 rounded-md w-full p-3 bg-gray-50 focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-all" required>
                <option value="" hidden>Select Role</option>
                <option value="cashier">Cashier</option>
                <option value="coach">Coach</option>
              </select>
              <div class="mt-2 text-xs text-gray-500">
                <p>Note: The role will be automatically set based on the employee's role and cannot be changed.</p>
              </div>
            </div>
          </div>
        </div>

        <div class="mb-6">
          <h3 class="text-lg font-medium text-gray-700 mb-4 pb-2 border-b border-gray-200">Account Information</h3>
          <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
            <!-- Username -->
            <div class="mb-4">
              <label for="username" class="block text-sm font-medium text-gray-700 mb-1">Username</label>
              <input class="border border-gray-300 rounded-md w-full p-3 bg-gray-50 focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-all" name="username" type="text" placeholder="Username" required />
            </div>

            <!-- Email -->
            <div class="mb-4">
              <label for="email" class="block text-sm font-medium text-gray-700 mb-1">Email</label>
              <input class="border border-gray-300 rounded-md w-full p-3 bg-gray-50 focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-all" name="email" type="email" placeholder="Email" required />
            </div>
          </div>
        </div>

        <div class="mb-6">
          <h3 class="text-lg font-medium text-gray-700 mb-4 pb-2 border-b border-gray-200">Security</h3>
          <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
            <!-- Password -->
            <div class="mb-4">
              <label for="password" class="block text-sm font-medium text-gray-700 mb-1">Password</label>
              <div class="relative">
                <input id="password" class="border border-gray-300 rounded-md w-full p-3 pr-12 bg-gray-50 focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-all" name="password" type="password" placeholder="Password" required />
                <button
                  type="button"
                  class="absolute inset-y-0 right-0 flex items-center px-3 text-gray-600 hover:text-gray-800 focus:outline-none"
                  onclick="togglePassword('password')"
                  aria-label="Toggle password visibility">
                  <i id="password_icon" class="fas fa-eye"></i>
                </button>
              </div>
            </div>

            <!-- Confirm Password -->
            <div class="mb-4">
              <label for="confirm_password" class="block text-sm font-medium text-gray-700 mb-1">Confirm Password</label>
              <div class="relative">
                <input id="confirm_password" class="border border-gray-300 rounded-md w-full p-3 pr-12 bg-gray-50 focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-all" name="confirm_password" type="password" placeholder="Confirm Password" required />
                <button
                  type="button"
                  class="absolute inset-y-0 right-0 flex items-center px-3 text-gray-600 hover:text-gray-800 focus:outline-none"
                  onclick="togglePassword('confirm_password')"
                  aria-label="Toggle confirm password visibility">
                  <i id="confirm_password_icon" class="fas fa-eye"></i>
                </button>
              </div>
            </div>
          </div>
        </div>

        <!-- Submit button -->
        <div class="mt-8">
          <button type="submit" class="bg-blue-700 hover:bg-blue-800 text-white font-medium py-3 px-4 w-full rounded flex items-center justify-center transition-colors text-lg"><i class="fa-solid fa-user-plus mr-2"></i> Create User Account</button>
        </div>
      </form>
    </div>

    <!-- Success message section -->
    <div class="bg-white rounded-lg shadow-md overflow-hidden">
      <div class="p-6 text-center">
        <p class="text-gray-600 mb-4">After creating a system access account, you will be redirected to the User Management page where you can view all users with system access.</p>
        <a href="{% url 'user:index' %}?tab=system-users" class="text-blue-600 hover:text-blue-800">
          <i class="fa-solid fa-arrow-right mr-1"></i>View System Access Users
        </a>
      </div>
    </div>
  </div>
</div>
<!-- JavaScript to auto-select role based on employee selection -->
<script>
  document.addEventListener('DOMContentLoaded', function() {
    const employeeSelect = document.getElementById('employee');
    const roleSelect = document.getElementById('role');

    // Store employee roles in a JavaScript object
    const employeeRoles = {
      {% for employee in employees_without_access %}
        "{{ employee.id }}": "{{ employee.role|default:'' }}",
      {% endfor %}
    };

    // Function to update role based on selected employee
    function updateRole() {
      const selectedEmployeeId = employeeSelect.value;
      const employeeRole = employeeRoles[selectedEmployeeId];

      if (employeeRole) {
        // Remove any existing hidden input and note
        const existingHiddenInput = roleSelect.parentNode.querySelector('input[type="hidden"][name="role"]');
        if (existingHiddenInput) {
          existingHiddenInput.remove();
        }

        const existingNote = roleSelect.parentNode.querySelector('div.text-xs.text-gray-500.mt-1');
        if (existingNote) {
          existingNote.remove();
        }

        // Check if the employee role is one that can have system access
        const systemAccessRoles = ['admin', 'cashier', 'coach'];

        if (systemAccessRoles.includes(employeeRole)) {
          // We don't allow creating admin users through the form
          // For admin employees, default to cashier role
          const roleToSelect = employeeRole === 'admin' ? 'cashier' : employeeRole;

          // Find and select the matching option
          for (let i = 0; i < roleSelect.options.length; i++) {
            if (roleSelect.options[i].value === roleToSelect) {
              roleSelect.selectedIndex = i;
              break;
            }
          }

          // Disable the role dropdown to prevent changes for roles that already have system access
          roleSelect.disabled = true;

          // Add a hidden input to ensure the role is submitted with the form
          const hiddenRoleInput = document.createElement('input');
          hiddenRoleInput.type = 'hidden';
          hiddenRoleInput.name = 'role';
          hiddenRoleInput.value = roleToSelect;
          roleSelect.parentNode.appendChild(hiddenRoleInput);

          // Add a note to explain why the dropdown is disabled
          const noteElement = document.createElement('div');
          noteElement.className = 'text-xs text-gray-500 mt-1';
          noteElement.innerHTML = 'Role is automatically set based on the employee\'s role and cannot be changed.';
          roleSelect.parentNode.appendChild(noteElement);
        } else {
          // For roles that can't have system access (cleaner, security), allow selecting a role
          roleSelect.disabled = false;

          // Reset the role dropdown to the first option (Select Role)
          roleSelect.selectedIndex = 0;

          // Add a note to explain that a role with system access must be selected
          const noteElement = document.createElement('div');
          noteElement.className = 'text-xs text-gray-500 mt-1';
          noteElement.innerHTML = 'Employee has role "' + employeeRole + '" which cannot have system access. Please select a system access role (Cashier or Coach).';
          roleSelect.parentNode.appendChild(noteElement);
        }
      } else {
        // If no employee is selected, enable the role dropdown
        roleSelect.disabled = false;

        // Remove any existing hidden input and note
        const hiddenInput = roleSelect.parentNode.querySelector('input[type="hidden"][name="role"]');
        if (hiddenInput) {
          hiddenInput.remove();
        }

        const note = roleSelect.parentNode.querySelector('div.text-xs.text-gray-500.mt-1');
        if (note) {
          note.remove();
        }
      }
    }

    // Update role when employee selection changes
    employeeSelect.addEventListener('change', updateRole);

    // Initial update
    updateRole();
  });

  // Password toggle functionality
  function togglePassword(fieldId) {
    const passwordInput = document.getElementById(fieldId);
    const passwordIcon = document.getElementById(fieldId + '_icon');
    const toggleButton = passwordIcon.parentElement;

    if (passwordInput && passwordIcon) {
      if (passwordInput.type === 'password') {
        passwordInput.type = 'text';
        passwordIcon.classList.remove('fa-eye');
        passwordIcon.classList.add('fa-eye-slash');
        toggleButton.setAttribute('aria-label', 'Hide password');
      } else {
        passwordInput.type = 'password';
        passwordIcon.classList.remove('fa-eye-slash');
        passwordIcon.classList.add('fa-eye');
        toggleButton.setAttribute('aria-label', 'Show password');
      }
    }
  }
</script>
{% endblock body %}




