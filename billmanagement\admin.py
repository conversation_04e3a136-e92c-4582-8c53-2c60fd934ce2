from django.contrib import admin
from .models import Bill, BillReceiptTemplate

@admin.register(Bill)
class BillAdmin(admin.ModelAdmin):
    list_display = ('bill_id', 'category', 'provider', 'month_year', 'payment_period', 'amount_khr', 'payment_status')
    list_filter = ('payment_status', 'category', 'payment_period', 'month_year', 'is_recurring')
    search_fields = ('bill_id', 'provider', 'description', 'notes')
    date_hierarchy = 'month_year'
    readonly_fields = ('bill_id', 'payment_date', 'created_at', 'updated_at')
    fieldsets = (
        ('Bill Information', {
            'fields': ('bill_id', 'category', 'provider', 'description')
        }),
        ('Payment Details', {
            'fields': ('month_year', 'payment_period', 'amount_khr', 'amount_usd', 'payment_method', 'payment_status', 'payment_date', 'paid_by')
        }),
        ('Additional Information', {
            'fields': ('is_recurring', 'notes', 'created_at', 'updated_at')
        }),
    )

@admin.register(BillReceiptTemplate)
class BillReceiptTemplateAdmin(admin.ModelAdmin):
    list_display = ('name', 'language', 'is_default', 'created_at')
    list_filter = ('is_default', 'language')
    search_fields = ('name', 'header_text', 'subheader_text', 'footer_text')
    readonly_fields = ('created_at', 'updated_at')
    fieldsets = (
        ('Template Information', {
            'fields': ('name', 'is_default', 'language')
        }),
        ('Content', {
            'fields': ('header_text', 'subheader_text', 'footer_text', 'company_logo')
        }),
        ('Styling', {
            'fields': ('background_color', 'text_color', 'accent_color', 'show_company_info', 'show_signatures', 'custom_css')
        }),
        ('Timestamps', {
            'fields': ('created_at', 'updated_at')
        }),
    )
