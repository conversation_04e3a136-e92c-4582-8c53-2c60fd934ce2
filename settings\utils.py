import logging
from django.core.cache import cache
from django.utils import timezone
from django.contrib.sessions.models import Session
from django.contrib.auth import get_user_model
from user.models import ROLE_CHOICES

logger = logging.getLogger(__name__)
User = get_user_model()

def get_settings():
    """
    Get the settings object, creating it if it doesn't exist.
    This is a convenience function that can be imported from anywhere.
    """
    from settings.models import Settings
    return Settings.get_settings()

def get_setting(key, default=None):
    """
    Get a specific setting by key.

    Args:
        key (str): The name of the setting to retrieve
        default: The default value to return if the setting doesn't exist

    Returns:
        The value of the setting, or the default if not found
    """
    settings = get_settings()
    return getattr(settings, key, default)

def set_setting(key, value):
    """
    Set a specific setting by key.

    Args:
        key (str): The name of the setting to set
        value: The value to set

    Returns:
        The updated settings object
    """
    settings = get_settings()

    # Check if the setting exists
    if not hasattr(settings, key):
        raise AttributeError(f"Setting '{key}' does not exist")

    # Set the value
    setattr(settings, key, value)
    settings.save()

    # Invalidate cache
    cache.delete('system_settings')

    return settings

def update_settings(settings_dict):
    """
    Update multiple settings at once.

    Args:
        settings_dict (dict): Dictionary of setting keys and values

    Returns:
        The updated settings object
    """
    settings = get_settings()

    # Update each setting
    for key, value in settings_dict.items():
        if hasattr(settings, key):
            setattr(settings, key, value)

    settings.save()

    # Invalidate cache
    cache.delete('system_settings')

    return settings

def reset_settings():
    """
    Reset all settings to their default values by deleting the current settings object.
    A new one will be created with defaults the next time get_settings() is called.

    Returns:
        The new settings object with default values
    """
    from settings.models import Settings

    # Delete existing settings
    Settings.objects.all().delete()

    # Invalidate cache
    cache.delete('system_settings')

    # Return new settings with defaults
    return get_settings()

def mark_backup_complete():
    """
    Mark that a backup has been completed by updating the last_backup_date.

    Returns:
        The updated settings object
    """
    settings = get_settings()
    settings.last_backup_date = timezone.now()
    settings.save()
    return settings

def mark_data_cleanup_complete():
    """
    Mark that a data cleanup has been completed by updating the last_data_cleanup.

    Returns:
        The updated settings object
    """
    settings = get_settings()
    settings.last_data_cleanup = timezone.now()
    settings.save()
    return settings


# Permission management functions
def get_default_permissions():
    """
    Get the default permissions for each role.

    Returns:
        dict: A dictionary mapping roles to their default module permissions
    """
    return {
        'admin': {
            # Admin has full access to everything by default
            'dashboard': 'full',
            'member': 'full',
            'payment': 'full',
            'payroll': 'full',
            'product': 'full',
            'purchase': 'full',
            'pos': 'full',
            'paypervisit': 'full',
            'bill': 'full',
            'finance': 'full',
            'financialreport': 'full',
            'settings': 'full',
            'user': 'full',     # Explicitly include user management
        },
        'cashier': {
            'dashboard': 'view',
            'member': 'edit',
            'payment': 'edit',
            'payroll': 'view',  # Allow cashiers to view their own salary
            'product': 'edit',
            'purchase': 'edit',
            'pos': 'full',
            'paypervisit': 'full',
            'bill': 'none',
            'finance': 'view',
            'financialreport': 'none',
            'settings': 'none',
            'user': 'none',     # Explicitly set no access to user management
        },
        'coach': {
            'dashboard': 'view',
            'member': 'edit',
            'payment': 'none',
            'payroll': 'view',  # Allow coaches to view their own salary
            'product': 'none',
            'purchase': 'none',
            'pos': 'none',
            'paypervisit': 'none',
            'bill': 'none',
            'finance': 'none',
            'financialreport': 'none',
            'settings': 'none',
            'user': 'none',     # Explicitly set no access to user management
        },
        'cleaner': {
            'dashboard': 'view',
            'member': 'none',
            'payment': 'none',
            'payroll': 'view',  # Allow cleaners to view their own salary
            'product': 'none',
            'purchase': 'none',
            'pos': 'none',
            'paypervisit': 'none',
            'bill': 'none',
            'finance': 'none',
            'financialreport': 'none',
            'settings': 'none',
            'user': 'none',     # Explicitly set no access to user management
        },
        'security': {
            'dashboard': 'view',
            'member': 'none',
            'payment': 'none',
            'payroll': 'view',  # Allow security to view their own salary
            'product': 'none',
            'purchase': 'none',
            'pos': 'none',
            'paypervisit': 'none',
            'bill': 'none',
            'finance': 'none',
            'financialreport': 'none',
            'settings': 'none',
            'user': 'none',     # Explicitly set no access to user management
        },
    }

def reset_role_permissions(role=None):
    """
    Reset permissions for a specific role or all roles to their defaults.

    Args:
        role (str, optional): The role to reset. If None, reset all roles.

    Returns:
        int: The number of permissions that were reset
    """
    from settings.models import RolePermission, MODULE_CHOICES

    default_permissions = get_default_permissions()
    count = 0

    if role:
        # Reset permissions for a specific role
        if role in default_permissions:
            # Delete existing permissions for this role
            RolePermission.objects.filter(role=role).delete()

            # Create default permissions for this role
            for module, level in default_permissions[role].items():
                RolePermission.objects.create(role=role, module=module, permission_level=level)
                count += 1
    else:
        # Reset permissions for all roles
        RolePermission.objects.all().delete()

        for role_code, role_name in ROLE_CHOICES:
            if role_code in default_permissions:
                for module, level in default_permissions[role_code].items():
                    RolePermission.objects.create(role=role_code, module=module, permission_level=level)
                    count += 1

    return count

def get_user_permissions(user):
    """
    Get all permissions for a user based on their role.

    Args:
        user: The user object

    Returns:
        dict: A dictionary mapping module names to permission levels
    """
    from settings.models import RolePermission, MODULE_CHOICES

    # Get permissions for this role (respecting granular permission settings)
    # Note: Removed admin bypass to respect granular permission settings
    permissions = {}
    for module_code, module_name in MODULE_CHOICES:
        permission_level = RolePermission.get_module_permission(user.role, module_code)
        permissions[module_code] = permission_level

    return permissions

def invalidate_user_sessions(user_ids=None, role=None):
    """
    Invalidate sessions for specific users or all users with a specific role

    Args:
        user_ids (list): List of user IDs to invalidate sessions for
        role (str): Role to invalidate sessions for all users with that role
    """
    try:
        if user_ids:
            # Invalidate sessions for specific users
            for user_id in user_ids:
                invalidate_user_session(user_id)

        elif role:
            # Invalidate sessions for all users with the specified role
            users = User.objects.filter(role=role)
            for user in users:
                invalidate_user_session(user.id)

        logger.info(f"Invalidated sessions for users: {user_ids or f'role {role}'}")

    except Exception as e:
        logger.error(f"Error invalidating user sessions: {str(e)}")

def invalidate_user_session(user_id):
    """
    Invalidate all sessions for a specific user

    Args:
        user_id (int): User ID to invalidate sessions for
    """
    try:
        # Get all sessions
        sessions = Session.objects.all()

        for session in sessions:
            session_data = session.get_decoded()
            if session_data.get('_auth_user_id') == str(user_id):
                session.delete()
                logger.debug(f"Deleted session for user {user_id}")

    except Exception as e:
        logger.error(f"Error invalidating session for user {user_id}: {str(e)}")

def get_permission_level_display(level):
    """
    Get display name for permission level

    Args:
        level (str): Permission level code

    Returns:
        str: Display name for the permission level
    """
    level_map = {
        'none': 'No Access',
        'view': 'View Only',
        'edit': 'View and Edit',
        'full': 'Full Access'
    }
    return level_map.get(level, 'Unknown')

def compare_permission_levels(level1, level2):
    """
    Compare two permission levels

    Args:
        level1 (str): First permission level
        level2 (str): Second permission level

    Returns:
        int: -1 if level1 < level2, 0 if equal, 1 if level1 > level2
    """
    level_hierarchy = {
        'none': 0,
        'view': 1,
        'edit': 2,
        'full': 3
    }

    value1 = level_hierarchy.get(level1, 0)
    value2 = level_hierarchy.get(level2, 0)

    if value1 < value2:
        return -1
    elif value1 > value2:
        return 1
    else:
        return 0

def log_permission_change(user, role, module, old_level, new_level, changed_by=None):
    """
    Log permission changes for audit purposes

    Args:
        user: User whose permissions changed (can be None for role-based changes)
        role (str): Role that was modified
        module (str): Module that was modified
        old_level (str): Previous permission level
        new_level (str): New permission level
        changed_by: User who made the change (can be None)
    """
    try:
        change_type = "increased" if compare_permission_levels(new_level, old_level) > 0 else "decreased"

        if user:
            log_message = f"Permission {change_type} for user {user.username} ({role}): {module} from {old_level} to {new_level}"
        else:
            log_message = f"Permission {change_type} for role {role}: {module} from {old_level} to {new_level}"

        if changed_by:
            log_message += f" by {changed_by.username}"

        logger.info(log_message)

    except Exception as e:
        logger.error(f"Error logging permission change: {str(e)}")

def get_users_with_role(role):
    """
    Get all users with a specific role

    Args:
        role (str): Role to search for

    Returns:
        QuerySet: Users with the specified role
    """
    return User.objects.filter(role=role)

def get_active_users_with_role(role):
    """
    Get all active users with a specific role

    Args:
        role (str): Role to search for

    Returns:
        QuerySet: Active users with the specified role
    """
    return User.objects.filter(role=role, is_active=True)
