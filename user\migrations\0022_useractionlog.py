# Generated by Django 5.0.2 on 2025-05-28 15:24

import django.db.models.deletion
from django.conf import settings
from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('user', '0021_alter_user_role'),
    ]

    operations = [
        migrations.CreateModel(
            name='UserActionLog',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('action_type', models.CharField(choices=[('login', 'Login'), ('logout', 'Logout'), ('failed_login', 'Failed Login'), ('create_user', 'Create User'), ('edit_user', 'Edit User'), ('delete_user', 'Delete User'), ('activate_user', 'Activate User'), ('deactivate_user', 'Deactivate User'), ('password_change', 'Password Change'), ('delete_payment', 'Delete Payment'), ('delete_paypervisit', 'Delete Pay-per-visit'), ('delete_sale', 'Delete Sale'), ('delete_finance_transaction', 'Delete Finance Transaction'), ('delete_salary_payment', 'Delete Salary Payment'), ('delete_bill', 'Delete Bill'), ('edit_payment', 'Edit Payment'), ('edit_paypervisit', 'Edit Pay-per-visit'), ('edit_sale', 'Edit Sale'), ('edit_finance_transaction', 'Edit Finance Transaction'), ('edit_salary_payment', 'Edit Salary Payment'), ('edit_bill', 'Edit Bill'), ('edit_member_balance', 'Edit Member Balance'), ('change_permissions', 'Change Permissions'), ('change_role', 'Change Role'), ('export_data', 'Export Data'), ('generate_report', 'Generate Report'), ('settings_change', 'Settings Change'), ('template_change', 'Template Change'), ('bulk_delete', 'Bulk Delete'), ('bulk_edit', 'Bulk Edit'), ('clean_data', 'Clean Data'), ('other', 'Other Action')], max_length=50, verbose_name='Action Type')),
                ('module', models.CharField(choices=[('user', 'User Management'), ('member', 'Member Management'), ('payment', 'Payment Management'), ('paypervisit', 'Pay-per-visit'), ('product', 'Product Management'), ('pos', 'Point of Sale'), ('finance', 'Finance Management'), ('payroll', 'Payroll Management'), ('bill', 'Bill Management'), ('settings', 'Settings'), ('auth', 'Authentication'), ('system', 'System')], max_length=20, verbose_name='Module')),
                ('action_time', models.DateTimeField(auto_now_add=True, verbose_name='Action Time')),
                ('status', models.CharField(choices=[('success', 'Success'), ('failed', 'Failed'), ('partial', 'Partial Success')], default='success', max_length=10, verbose_name='Status')),
                ('target_model', models.CharField(blank=True, max_length=50, verbose_name='Target Model')),
                ('target_id', models.CharField(blank=True, max_length=50, verbose_name='Target ID')),
                ('target_description', models.TextField(blank=True, verbose_name='Target Description')),
                ('before_values', models.JSONField(blank=True, null=True, verbose_name='Before Values')),
                ('after_values', models.JSONField(blank=True, null=True, verbose_name='After Values')),
                ('ip_address', models.GenericIPAddressField(blank=True, null=True, verbose_name='IP Address')),
                ('user_agent', models.TextField(blank=True, verbose_name='User Agent')),
                ('description', models.TextField(blank=True, verbose_name='Description')),
                ('additional_data', models.JSONField(blank=True, null=True, verbose_name='Additional Data')),
                ('financial_impact', models.DecimalField(blank=True, decimal_places=2, help_text='Amount in KHR', max_digits=15, null=True, verbose_name='Financial Impact')),
                ('user', models.ForeignKey(null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='action_logs', to=settings.AUTH_USER_MODEL, verbose_name='User')),
            ],
            options={
                'verbose_name': 'User Action Log',
                'verbose_name_plural': 'User Action Logs',
                'ordering': ['-action_time'],
                'indexes': [models.Index(fields=['user', 'action_time'], name='user_userac_user_id_d64df7_idx'), models.Index(fields=['action_type', 'action_time'], name='user_userac_action__3ccd63_idx'), models.Index(fields=['module', 'action_time'], name='user_userac_module_afa3ec_idx'), models.Index(fields=['ip_address', 'action_time'], name='user_userac_ip_addr_87a7ab_idx')],
            },
        ),
    ]
