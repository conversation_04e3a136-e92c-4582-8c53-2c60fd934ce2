import os
import re
import argparse
from collections import defaultdict

def check_template_for_translation_tags(file_path):
    """Check if a template file has translation tags."""
    with open(file_path, 'r', encoding='utf-8', errors='ignore') as f:
        content = f.read()

    has_load_i18n = re.search(r'{%\s*load\s+i18n\s*%}', content) is not None
    has_trans_tags = re.search(r'{%\s*trans\s+', content) is not None
    has_blocktrans_tags = re.search(r'{%\s*blocktrans', content) is not None

    return {
        'has_load_i18n': has_load_i18n,
        'has_trans_tags': has_trans_tags,
        'has_blocktrans_tags': has_blocktrans_tags,
        'needs_translation': not (has_trans_tags or has_blocktrans_tags)
    }

def find_untranslated_templates(templates_dir):
    """Find all HTML templates that don't have translation tags."""
    untranslated_templates = []
    partial_translated_templates = []
    translated_templates = []

    for root, _, files in os.walk(templates_dir):
        for file in files:
            if file.endswith('.html'):
                file_path = os.path.join(root, file)
                rel_path = os.path.relpath(file_path, os.path.dirname(templates_dir))

                result = check_template_for_translation_tags(file_path)

                if result['needs_translation']:
                    if result['has_load_i18n']:
                        partial_translated_templates.append((rel_path, file_path))
                    else:
                        untranslated_templates.append((rel_path, file_path))
                else:
                    translated_templates.append((rel_path, file_path))

    return {
        'untranslated': untranslated_templates,
        'partial': partial_translated_templates,
        'translated': translated_templates
    }

def add_load_i18n_to_template(file_path):
    """Add {% load i18n %} to a template file if it doesn't have it."""
    with open(file_path, 'r', encoding='utf-8', errors='ignore') as f:
        content = f.read()

    # Check if i18n is already loaded
    if re.search(r'{%\s*load\s+i18n\s*%}', content):
        return False

    # Find the first {% load ... %} tag
    load_match = re.search(r'{%\s*load\s+[^%]+%}', content)

    if load_match:
        # Add {% load i18n %} after the existing load tag
        end_pos = load_match.end()
        new_content = content[:end_pos] + '\n{% load i18n %}' + content[end_pos:]
    else:
        # If no load tag exists, add it after the first line (usually DOCTYPE or html tag)
        lines = content.split('\n', 1)
        if len(lines) > 1:
            new_content = lines[0] + '\n{% load i18n %}\n' + lines[1]
        else:
            new_content = '{% load i18n %}\n' + content

    with open(file_path, 'w', encoding='utf-8') as f:
        f.write(new_content)

    return True

def process_templates(templates_dir, add_load_i18n=False):
    """Process all templates and optionally add {% load i18n %} tags."""
    results = find_untranslated_templates(templates_dir)

    print(f"Templates Analysis:")
    print(f"Total templates: {len(results['untranslated']) + len(results['partial']) + len(results['translated'])}")
    print(f"Fully translated templates: {len(results['translated'])}")
    print(f"Partially translated templates (has load i18n but no trans tags): {len(results['partial'])}")
    print(f"Untranslated templates: {len(results['untranslated'])}")

    if add_load_i18n:
        print("\nAdding {% load i18n %} to templates that need it...")
        templates_updated = 0

        for _, file_path in results['untranslated']:
            if add_load_i18n_to_template(file_path):
                templates_updated += 1

        print(f"Added '{{% load i18n %}}' to {templates_updated} templates.")

    print("\nTemplates that need translation tags:")
    for rel_path, _ in sorted(results['untranslated'] + results['partial']):
        print(f"- {rel_path}")

    print("\nNext steps:")
    print("1. For each template, wrap text strings with '{{% trans \"text\" %}}' tags")
    print("2. For text with variables, use '{{% blocktrans %}}...{{% endblocktrans %}}'")
    print("3. Run 'python manage.py makemessages -l km' to extract strings")
    print("4. Add Khmer translations to locale/km/LC_MESSAGES/django.po")
    print("5. Run 'python manage.py compilemessages -l km' to compile translations")

if __name__ == "__main__":
    parser = argparse.ArgumentParser(description='Process templates for internationalization.')
    parser.add_argument('--templates-dir', default='templates', help='Path to templates directory')
    parser.add_argument('--add-load-i18n', action='store_true', help='Add {% load i18n %} to templates that need it')

    args = parser.parse_args()
    process_templates(args.templates_dir, args.add_load_i18n)
