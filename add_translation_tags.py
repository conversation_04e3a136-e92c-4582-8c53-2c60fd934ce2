import os
import re
import argparse
from bs4 import BeautifulSoup, Comment

def is_translatable_text(text):
    """Check if text should be translated."""
    if not text or text.strip() == '':
        return False

    # Skip if text is just whitespace, numbers, or special characters
    if re.match(r'^[\s\d\W]+$', text):
        return False

    return True

def add_translation_tags_to_template(file_path, dry_run=True):
    """Add translation tags to a template file."""
    try:
        with open(file_path, 'r', encoding='utf-8', errors='ignore') as f:
            content = f.read()

        # Skip if the file doesn't have {% load i18n %}
        if not re.search(r'{%\s*load\s+i18n\s*%}', content):
            print(f"Warning: {file_path} doesn't have '{{% load i18n %}}' tag. Skipping.")
            return False

        # Skip if the file already has translation tags
        if re.search(r'{%\s*trans\s+', content):
            print(f"Info: {file_path} already has translation tags. Skipping.")
            return False

        # We'll use a simple approach to add translation tags to text nodes
        # This won't be perfect but will help with the bulk of the work

        # Replace text in HTML attributes
        content = re.sub(r'(placeholder|title|alt)="([^"]*)"',
                         lambda m: f'{m.group(1)}="{% trans "{m.group(2)}" %}"'
                         if is_translatable_text(m.group(2)) else m.group(0),
                         content)

        # Replace text in button elements
        content = re.sub(r'<button([^>]*)>(.*?)</button>',
                         lambda m: f'<button{m.group(1)}>{% trans "{m.group(2).strip()}" %}</button>'
                         if is_translatable_text(m.group(2)) else m.group(0),
                         content)

        # Replace text in heading elements
        for tag in ['h1', 'h2', 'h3', 'h4', 'h5', 'h6']:
            content = re.sub(f'<{tag}([^>]*)>(.*?)</{tag}>',
                             lambda m: f'<{tag}{m.group(1)}>{% trans "{m.group(2).strip()}" %}</{tag}>'
                             if is_translatable_text(m.group(2)) else m.group(0),
                             content)

        # Replace text in label elements
        content = re.sub(r'<label([^>]*)>(.*?)</label>',
                         lambda m: f'<label{m.group(1)}>{% trans "{m.group(2).strip()}" %}</label>'
                         if is_translatable_text(m.group(2)) else m.group(0),
                         content)

        # Replace text in span elements
        content = re.sub(r'<span([^>]*)>(.*?)</span>',
                         lambda m: f'<span{m.group(1)}>{% trans "{m.group(2).strip()}" %}</span>'
                         if is_translatable_text(m.group(2)) else m.group(0),
                         content)

        # Replace text in p elements
        content = re.sub(r'<p([^>]*)>(.*?)</p>',
                         lambda m: f'<p{m.group(1)}>{% trans "{m.group(2).strip()}" %}</p>'
                         if is_translatable_text(m.group(2)) else m.group(0),
                         content)

        # Replace text in a elements
        content = re.sub(r'<a([^>]*)>(.*?)</a>',
                         lambda m: f'<a{m.group(1)}>{% trans "{m.group(2).strip()}" %}</a>'
                         if is_translatable_text(m.group(2)) else m.group(0),
                         content)

        # Replace text in th elements
        content = re.sub(r'<th([^>]*)>(.*?)</th>',
                         lambda m: f'<th{m.group(1)}>{% trans "{m.group(2).strip()}" %}</th>'
                         if is_translatable_text(m.group(2)) else m.group(0),
                         content)

        # Replace text in td elements
        content = re.sub(r'<td([^>]*)>(.*?)</td>',
                         lambda m: f'<td{m.group(1)}>{% trans "{m.group(2).strip()}" %}</td>'
                         if is_translatable_text(m.group(2)) else m.group(0),
                         content)

        # Replace text in option elements
        content = re.sub(r'<option([^>]*)>(.*?)</option>',
                         lambda m: f'<option{m.group(1)}>{% trans "{m.group(2).strip()}" %}</option>'
                         if is_translatable_text(m.group(2)) else m.group(0),
                         content)

        if not dry_run:
            with open(file_path, 'w', encoding='utf-8') as f:
                f.write(content)
            print(f"Added translation tags to {file_path}")
        else:
            print(f"Would add translation tags to {file_path} (dry run)")

        return True
    except Exception as e:
        print(f"Error processing {file_path}: {str(e)}")
        return False

def process_templates(templates_dir, dry_run=True):
    """Process all templates and add translation tags."""
    success_count = 0
    error_count = 0
    skipped_count = 0

    for root, _, files in os.walk(templates_dir):
        for file in files:
            if file.endswith('.html'):
                file_path = os.path.join(root, file)
                print(f"Processing {file_path}...")

                result = add_translation_tags_to_template(file_path, dry_run)

                if result:
                    success_count += 1
                else:
                    skipped_count += 1

    print(f"\nSummary:")
    print(f"Templates processed: {success_count + error_count + skipped_count}")
    print(f"Templates updated: {success_count}")
    print(f"Templates with errors: {error_count}")
    print(f"Templates skipped: {skipped_count}")

    if dry_run:
        print("\nThis was a dry run. No files were actually modified.")
        print("Run with --apply to apply the changes.")

if __name__ == "__main__":
    parser = argparse.ArgumentParser(description='Add translation tags to templates.')
    parser.add_argument('--templates-dir', default='templates', help='Path to templates directory')
    parser.add_argument('--apply', action='store_true', help='Apply changes (default is dry run)')
    parser.add_argument('--template', help='Process a specific template file')

    args = parser.parse_args()

    if args.template:
        add_translation_tags_to_template(args.template, not args.apply)
    else:
        process_templates(args.templates_dir, not args.apply)
