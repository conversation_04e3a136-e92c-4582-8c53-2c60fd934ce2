{% extends "base.html" %}
{% load custom_filters %}



{% block head %}
<style>
    /* Print styles for financial reports */
    @media print {
        /* Hide everything by default */
        body * {
            visibility: hidden;
        }

        /* Hide sidebar specifically */
        .sidebar {
            display: none !important;
        }

        /* Make the print section visible */
        .print-section, .print-section * {
            visibility: visible !important;
        }

        /* Show elements with print:block class */
        .print\:block {
            display: block !important;
        }

        /* Position the print section at the top left */
        .print-section {
            position: absolute;
            left: 0;
            top: 0;
            width: 100%;
            margin: 0;
            padding: 20px;
        }

        /* Remove margin that accommodates sidebar */
        .conponentSection {
            margin-left: 0 !important;
        }

        /* Hide elements with no-print class */
        .no-print {
            display: none !important;
        }

        /* Reset any background colors for printing */
        .bg-gray-100, .bg-gray-50, .bg-blue-50, .bg-red-50, .bg-green-50, .bg-yellow-50 {
            background-color: white !important;
        }

        /* Ensure tables print well */
        table {
            width: 100%;
            border-collapse: collapse;
        }

        th, td {
            border: 1px solid #ddd;
            padding: 8px;
            text-align: left;
        }

        /* Adjust font sizes for printing */
        h2 {
            font-size: 24px !important;
        }

        h3 {
            font-size: 18px !important;
        }

        p, td, th {
            font-size: 12px !important;
        }

        /* Ensure proper page breaks */
        .page-break {
            page-break-after: always;
        }
    }
</style>
{% endblock %}

{% block body %}
<div class="conponentSection p-5 sm:ml-64 bg-gray-100">
    <div class="componentWrapper max-w-7xl mx-auto">
        <div class="print-section">
            <!-- Print Header - Only visible when printing -->
            <div class="hidden print:block text-center mb-6">
                <h1 class="text-3xl font-bold">LEGEND FITNESS</h1>
                <h2 class="text-xl font-semibold mt-2">{{ title }}</h2>
                <p class="text-sm mt-2">Period: {{ date_range }}</p>
            </div>
        <!-- Header with title and actions -->
        <div class="bg-white p-6 rounded-lg shadow-sm mb-6 border-l-4 border-green-600">
            <div class="flex flex-col md:flex-row justify-between items-start md:items-center mb-6">
                <div>
                    <div class="flex items-center">
                        <a href="{% url 'financialreport:index' %}?filter={{ filter }}&start_date={{ start_date }}&end_date={{ end_date }}" class="mr-3 bg-gray-100 hover:bg-gray-200 text-gray-700 p-2 rounded-full transition-colors">
                            <i class="fa-solid fa-arrow-left"></i>
                        </a>
                        <div>
                            <h2 class="text-3xl font-bold text-gray-800">{{ title }}</h2>
                            <p class="text-gray-600 mt-1">Comprehensive view of income vs expenses and net balance</p>
                        </div>
                    </div>
                </div>
                <div class="flex mt-4 md:mt-0">
                    <div class="relative inline-block">
                        <button id="printReportBtn" class="flex items-center bg-white border border-gray-300 text-gray-700 px-4 py-2 rounded-md hover:bg-gray-50 mr-2 transition-colors no-print"><i class="fa-solid fa-print mr-2 text-green-600"></i> Print Report</button>
                        <div id="printTemplateDropdown" class="hidden absolute z-10 mt-2 w-64 rounded-md shadow-lg bg-white ring-1 ring-black ring-opacity-5">
                            <div class="py-1">
                                <div class="px-4 py-2 text-sm text-gray-700 font-medium border-b">Select Template</div>
                                {% for template in templates %}
                                <div class="flex px-4 py-2 hover:bg-gray-50">
                                    <div class="flex-grow">
                                        <div class="text-sm text-gray-700">{{ template.name }}</div>
                                        {% if template.is_default %}<span class="text-xs text-green-600">(Default)</span>{% endif %}
                                    </div>
                                    <div class="flex space-x-2">
                                        <a href="{% url 'financialreport:preview_report' 'balance' 'print' %}?filter={{ filter }}&start_date={{ start_date }}&end_date={{ end_date }}&template={{ template.id }}"
                                           class="text-blue-600 hover:text-blue-800" title="Preview" target="_blank">
                                            <i class="fa-solid fa-eye"></i>
                                        </a>
                                        <a href="{% url 'financialreport:print_report' 'balance' %}?filter={{ filter }}&start_date={{ start_date }}&end_date={{ end_date }}&template={{ template.id }}"
                                           class="text-blue-600 hover:text-blue-800" title="Print" target="_blank">
                                            <i class="fa-solid fa-print"></i>
                                        </a>
                                    </div>
                                </div>
                                {% empty %}
                                <div class="px-4 py-2 text-sm text-gray-500">No templates available</div>
                                {% endfor %}
                                <div class="border-t border-gray-100 mt-1"></div>
                                <a href="{% url 'financialreport:template_list' %}" class="block px-4 py-2 text-sm text-blue-600 hover:bg-gray-100">
                                    <i class="fa-solid fa-cog mr-1"></i> Manage Templates
                                </a>
                            </div>
                        </div>
                    </div>
                    <div class="relative inline-block">
                        <button id="csvExportBtn" class="flex items-center bg-white border border-gray-300 text-gray-700 px-4 py-2 rounded-md hover:bg-gray-50 mr-2 transition-colors no-print"><i class="fa-solid fa-file-csv mr-2 text-green-600"></i> Export CSV</button>
                        <div id="csvTemplateDropdown" class="hidden absolute z-10 mt-2 w-64 rounded-md shadow-lg bg-white ring-1 ring-black ring-opacity-5">
                            <div class="py-1">
                                <div class="px-4 py-2 text-sm text-gray-700 font-medium border-b">Select Template</div>
                                {% for template in templates %}
                                <div class="flex px-4 py-2 hover:bg-gray-50">
                                    <div class="flex-grow">
                                        <div class="text-sm text-gray-700">{{ template.name }}</div>
                                        {% if template.is_default %}<span class="text-xs text-green-600">(Default)</span>{% endif %}
                                    </div>
                                    <div class="flex space-x-2">
                                        <a href="{% url 'financialreport:preview_report' 'balance' 'csv' %}?filter={{ filter }}&start_date={{ start_date }}&end_date={{ end_date }}&template={{ template.id }}"
                                           class="text-blue-600 hover:text-blue-800" title="Preview" target="_blank">
                                            <i class="fa-solid fa-eye"></i>
                                        </a>
                                        <a href="{% url 'financialreport:export_csv' 'balance' %}?filter={{ filter }}&start_date={{ start_date }}&end_date={{ end_date }}&template={{ template.id }}"
                                           class="text-blue-600 hover:text-blue-800" title="Download">
                                            <i class="fa-solid fa-download"></i>
                                        </a>
                                    </div>
                                </div>
                                {% empty %}
                                <div class="px-4 py-2 text-sm text-gray-500">No templates available</div>
                                {% endfor %}
                                <div class="border-t border-gray-100 mt-1"></div>
                                <a href="{% url 'financialreport:template_list' %}" class="block px-4 py-2 text-sm text-blue-600 hover:bg-gray-100">
                                    <i class="fa-solid fa-cog mr-1"></i> Manage Templates
                                </a>
                            </div>
                        </div>
                    </div>
                    <div class="relative inline-block">
                        <button id="pdfExportBtn" class="flex items-center bg-white border border-gray-300 text-gray-700 px-4 py-2 rounded-md hover:bg-gray-50 transition-colors no-print"><i class="fa-solid fa-file-pdf mr-2 text-red-600"></i> Export PDF</button>
                        <div id="templateDropdown" class="hidden absolute z-10 mt-2 w-64 rounded-md shadow-lg bg-white ring-1 ring-black ring-opacity-5">
                            <div class="py-1">
                                <div class="px-4 py-2 text-sm text-gray-700 font-medium border-b">Select Template</div>
                                {% for template in templates %}
                                <div class="flex px-4 py-2 hover:bg-gray-50">
                                    <div class="flex-grow">
                                        <div class="text-sm text-gray-700">{{ template.name }}</div>
                                        {% if template.is_default %}<span class="text-xs text-green-600">(Default)</span>{% endif %}
                                    </div>
                                    <div class="flex space-x-2">
                                        <a href="{% url 'financialreport:preview_report' 'balance' 'pdf' %}?filter={{ filter }}&start_date={{ start_date }}&end_date={{ end_date }}&template={{ template.id }}"
                                           class="text-blue-600 hover:text-blue-800" title="Preview" target="_blank">
                                            <i class="fa-solid fa-eye"></i>
                                        </a>
                                        <a href="{% url 'financialreport:export_pdf' 'balance' %}?filter={{ filter }}&start_date={{ start_date }}&end_date={{ end_date }}&template={{ template.id }}"
                                           class="text-blue-600 hover:text-blue-800" title="Download">
                                            <i class="fa-solid fa-download"></i>
                                        </a>
                                    </div>
                                </div>
                                {% empty %}
                                <div class="px-4 py-2 text-sm text-gray-500">No templates available</div>
                                {% endfor %}
                                <div class="border-t border-gray-100 mt-1"></div>
                                <a href="{% url 'financialreport:template_list' %}" class="block px-4 py-2 text-sm text-blue-600 hover:bg-gray-100">
                                    <i class="fa-solid fa-cog mr-1"></i> Manage Templates
                                </a>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Date Range Selector -->
            <div class="bg-gray-50 p-4 rounded-lg border border-gray-200 no-print">
                <div class="flex flex-wrap items-center mb-3">
                    <span class="text-sm font-medium text-gray-700 mr-3">Period:</span>
                    <div class="flex flex-wrap gap-2">
                        <a href="?filter=today" class="px-4 py-2 rounded-md text-sm font-medium transition-colors {% if filter == 'today' %}bg-green-600 text-white{% else %}bg-white border border-gray-300 text-gray-700 hover:bg-gray-50{% endif %}">Today</a>
                        <a href="?filter=week" class="px-4 py-2 rounded-md text-sm font-medium transition-colors {% if filter == 'week' %}bg-green-600 text-white{% else %}bg-white border border-gray-300 text-gray-700 hover:bg-gray-50{% endif %}">This Week</a>
                        <a href="?filter=month" class="px-4 py-2 rounded-md text-sm font-medium transition-colors {% if filter == 'month' %}bg-green-600 text-white{% else %}bg-white border border-gray-300 text-gray-700 hover:bg-gray-50{% endif %}">This Month</a>
                        <a href="?filter=year" class="px-4 py-2 rounded-md text-sm font-medium transition-colors {% if filter == 'year' %}bg-green-600 text-white{% else %}bg-white border border-gray-300 text-gray-700 hover:bg-gray-50{% endif %}">This Year</a>
                        <button id="custom-range-btn" class="px-4 py-2 rounded-md text-sm font-medium transition-colors {% if filter == 'custom' %}bg-green-600 text-white{% else %}bg-white border border-gray-300 text-gray-700 hover:bg-gray-50{% endif %}">Custom Range</button>
                    </div>
                </div>

                <!-- Custom Date Range Form -->
                <div id="custom-range-form" class="{% if filter != 'custom' %}hidden{% endif %} mt-3 pt-3 border-t border-gray-200">
                    <form method="get" class="flex flex-wrap gap-4 items-end">
                        <input type="hidden" name="filter" value="custom">
                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-1">Start Date</label>
                            <input type="date" name="start_date" value="{{ start_date }}" class="border border-gray-300 rounded-md p-2 focus:ring-green-500 focus:border-green-500">
                        </div>
                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-1">End Date</label>
                            <input type="date" name="end_date" value="{{ end_date }}" class="border border-gray-300 rounded-md p-2 focus:ring-green-500 focus:border-green-500">
                        </div>

                        <div class="flex gap-2">
                            <button type="submit" class="bg-green-600 text-white px-4 py-2 rounded-md hover:bg-green-700 transition-colors">Apply</button>
                            <a href="{% url 'financialreport:balance_report' %}" class="bg-gray-200 text-gray-700 px-4 py-2 rounded-md hover:bg-gray-300 transition-colors">Reset</a>
                        </div>
                    </form>
                </div>

                <div class="mt-3 text-sm text-gray-600">
                    <div class="flex items-center mb-1">
                        <i class="fa-regular fa-calendar mr-2"></i>
                        <span>Showing balance data for: <strong>{{ date_range }}</strong></span>
                    </div>

                </div>
            </div>
        </div>

        <!-- Summary Cards -->
        <div class="grid grid-cols-1 md:grid-cols-3 gap-6 mb-6">
            <div class="bg-white rounded-lg shadow-sm p-6 border-t-4 border-blue-500 transition-transform hover:translate-y-[-2px]">
                <div class="flex items-center justify-between">
                    <div>
                        <p class="text-sm font-medium text-gray-500">Total Income</p>
                        <p class="text-3xl font-bold text-gray-800 mt-1">{{ income_total|format_khr }}</p>
                    </div>
                    <div class="bg-blue-100 p-3 rounded-full">
                        <i class="fa-solid fa-arrow-trend-up text-blue-600 text-xl"></i>
                    </div>
                </div>
            </div>

            <div class="bg-white rounded-lg shadow-sm p-6 border-t-4 border-red-500 transition-transform hover:translate-y-[-2px]">
                <div class="flex items-center justify-between">
                    <div>
                        <p class="text-sm font-medium text-gray-500">Total Expenses</p>
                        <p class="text-3xl font-bold text-gray-800 mt-1">{{ expense_total|format_khr }}</p>
                    </div>
                    <div class="bg-red-100 p-3 rounded-full">
                        <i class="fa-solid fa-arrow-trend-down text-red-600 text-xl"></i>
                    </div>
                </div>
            </div>

            <div class="bg-white rounded-lg shadow-sm p-6 border-t-4 {% if balance >= 0 %}border-green-500{% else %}border-yellow-500{% endif %} transition-transform hover:translate-y-[-2px]">
                <div class="flex items-center justify-between">
                    <div>
                        <p class="text-sm font-medium text-gray-500">Net Balance</p>
                        <p class="text-3xl font-bold {% if balance >= 0 %}text-green-600{% else %}text-yellow-600{% endif %} mt-1">{{ balance|format_khr }}</p>
                    </div>
                    <div class="{% if balance >= 0 %}bg-green-100{% else %}bg-yellow-100{% endif %} p-3 rounded-full">
                        <i class="fa-solid fa-scale-balanced {% if balance >= 0 %}text-green-600{% else %}text-yellow-600{% endif %} text-xl"></i>
                    </div>
                </div>
            </div>
        </div>

        <!-- Balance Summary Table -->
        <div class="bg-white rounded-lg shadow-sm p-6 mb-6">
            <h3 class="text-xl font-bold text-gray-800 mb-4">Balance Summary</h3>
            <div class="overflow-x-auto bg-white rounded-lg border border-gray-200">
                <table class="w-full text-sm text-left">
                    <thead><tr class="bg-gray-50 text-xs text-gray-700 uppercase">
                            <th scope="col" class="px-6 py-3 font-medium">Category</th>
                            <th scope="col" class="px-6 py-3 font-medium">Amount (KHR)</th>
                            <th scope="col" class="px-6 py-3 font-medium">Percentage</th>
                        </tr>
                    </thead>
                    <tbody class="divide-y divide-gray-200">
                        <!-- Income Section -->
                        <tr class="bg-blue-50">
                            <td colspan="3" class="px-6 py-3 font-semibold text-blue-800">Income Sources</td>
                        </tr>
                        <tr class="hover:bg-gray-50">
                            <td class="px-6 py-4">
                                <div class="flex items-center">
                                    <div class="w-3 h-3 rounded-full bg-blue-500 mr-2"></div>
                                    Membership Fees
                                </div>
                            </td>
                            <td class="px-6 py-4 font-medium">{{ income_sources.membership.total|format_khr }}</td>
                            <td class="px-6 py-4">
                                <div class="flex items-center">
                                    <div class="w-16 bg-gray-200 rounded-full h-2.5">
                                        <div class="bg-blue-500 h-2.5 rounded-full" style="width: {{ income_sources.membership.percentage }}%"></div>
                                    </div>
                                    <span class="ml-2">{{ income_sources.membership.percentage }}%</span>
                                </div>
                            </td>
                        </tr>
                        <tr class="hover:bg-gray-50">
                            <td class="px-6 py-4">
                                <div class="flex items-center">
                                    <div class="w-3 h-3 rounded-full bg-teal-500 mr-2"></div>
                                    Product Sales
                                </div>
                            </td>
                            <td class="px-6 py-4 font-medium">{{ income_sources.product_sales.total|format_khr }}</td>
                            <td class="px-6 py-4">
                                <div class="flex items-center">
                                    <div class="w-16 bg-gray-200 rounded-full h-2.5">
                                        <div class="bg-teal-500 h-2.5 rounded-full" style="width: {{ income_sources.product_sales.percentage }}%"></div>
                                    </div>
                                    <span class="ml-2">{{ income_sources.product_sales.percentage }}%</span>
                                </div>
                            </td>
                        </tr>
                        <tr class="hover:bg-gray-50">
                            <td class="px-6 py-4">
                                <div class="flex items-center">
                                    <div class="w-3 h-3 rounded-full bg-purple-500 mr-2"></div>
                                    Pay-per-visit
                                </div>
                            </td>
                            <td class="px-6 py-4 font-medium">{{ income_sources.paypervisit.total|format_khr }}</td>
                            <td class="px-6 py-4">
                                <div class="flex items-center">
                                    <div class="w-16 bg-gray-200 rounded-full h-2.5">
                                        <div class="bg-purple-500 h-2.5 rounded-full" style="width: {{ income_sources.paypervisit.percentage }}%"></div>
                                    </div>
                                    <span class="ml-2">{{ income_sources.paypervisit.percentage }}%</span>
                                </div>
                            </td>
                        </tr>
                        <tr class="hover:bg-gray-50">
                            <td class="px-6 py-4">
                                <div class="flex items-center">
                                    <div class="w-3 h-3 rounded-full bg-green-500 mr-2"></div>
                                    Deposits
                                </div>
                            </td>
                            <td class="px-6 py-4 font-medium">{{ income_sources.deposits.total|format_khr }}</td>
                            <td class="px-6 py-4">
                                <div class="flex items-center">
                                    <div class="w-16 bg-gray-200 rounded-full h-2.5">
                                        <div class="bg-green-500 h-2.5 rounded-full" style="width: {{ income_sources.deposits.percentage }}%"></div>
                                    </div>
                                    <span class="ml-2">{{ income_sources.deposits.percentage }}%</span>
                                </div>
                            </td>
                        </tr>
                        <tr class="bg-blue-50">
                            <td class="px-6 py-4 font-semibold text-blue-800">Total Income</td>
                            <td class="px-6 py-4 font-semibold text-blue-800">{{ income_total|format_khr }}</td>
                            <td class="px-6 py-4 font-semibold text-blue-800">100%</td>
                        </tr>

                        <!-- Expense Section -->
                        <tr class="bg-red-50">
                            <td colspan="3" class="px-6 py-3 font-semibold text-red-800">Expense Categories</td>
                        </tr>
                        <tr class="hover:bg-gray-50">
                            <td class="px-6 py-4">
                                <div class="flex items-center">
                                    <div class="w-3 h-3 rounded-full bg-red-500 mr-2"></div>
                                    Salaries
                                </div>
                            </td>
                            <td class="px-6 py-4 font-medium">{{ expense_categories.salaries.total|format_khr }}</td>
                            <td class="px-6 py-4">
                                <div class="flex items-center">
                                    <div class="w-16 bg-gray-200 rounded-full h-2.5">
                                        <div class="bg-red-500 h-2.5 rounded-full" style="width: {{ expense_categories.salaries.percentage }}%"></div>
                                    </div>
                                    <span class="ml-2">{{ expense_categories.salaries.percentage }}%</span>
                                </div>
                            </td>
                        </tr>
                        <tr class="hover:bg-gray-50">
                            <td class="px-6 py-4">
                                <div class="flex items-center">
                                    <div class="w-3 h-3 rounded-full bg-orange-500 mr-2"></div>
                                    Bills
                                </div>
                            </td>
                            <td class="px-6 py-4 font-medium">{{ expense_categories.bills.total|format_khr }}</td>
                            <td class="px-6 py-4">
                                <div class="flex items-center">
                                    <div class="w-16 bg-gray-200 rounded-full h-2.5">
                                        <div class="bg-orange-500 h-2.5 rounded-full" style="width: {{ expense_categories.bills.percentage }}%"></div>
                                    </div>
                                    <span class="ml-2">{{ expense_categories.bills.percentage }}%</span>
                                </div>
                            </td>
                        </tr>
                        <tr class="hover:bg-gray-50">
                            <td class="px-6 py-4">
                                <div class="flex items-center">
                                    <div class="w-3 h-3 rounded-full bg-yellow-500 mr-2"></div>
                                    Purchases
                                </div>
                            </td>
                            <td class="px-6 py-4 font-medium">{{ expense_categories.purchases.total|format_khr }}</td>
                            <td class="px-6 py-4">
                                <div class="flex items-center">
                                    <div class="w-16 bg-gray-200 rounded-full h-2.5">
                                        <div class="bg-yellow-500 h-2.5 rounded-full" style="width: {{ expense_categories.purchases.percentage }}%"></div>
                                    </div>
                                    <span class="ml-2">{{ expense_categories.purchases.percentage }}%</span>
                                </div>
                            </td>
                        </tr>
                        <tr class="hover:bg-gray-50">
                            <td class="px-6 py-4">
                                <div class="flex items-center">
                                    <div class="w-3 h-3 rounded-full bg-blue-500 mr-2"></div>
                                    Withdrawals
                                </div>
                            </td>
                            <td class="px-6 py-4 font-medium">{{ expense_categories.withdrawals.total|format_khr }}</td>
                            <td class="px-6 py-4">
                                <div class="flex items-center">
                                    <div class="w-16 bg-gray-200 rounded-full h-2.5">
                                        <div class="bg-blue-500 h-2.5 rounded-full" style="width: {{ expense_categories.withdrawals.percentage }}%"></div>
                                    </div>
                                    <span class="ml-2">{{ expense_categories.withdrawals.percentage }}%</span>
                                </div>
                            </td>
                        </tr>
                        <tr class="bg-red-50">
                            <td class="px-6 py-4 font-semibold text-red-800">Total Expenses</td>
                            <td class="px-6 py-4 font-semibold text-red-800">{{ expense_total|format_khr }}</td>
                            <td class="px-6 py-4 font-semibold text-red-800">100%</td>
                        </tr>

                        <!-- Balance Row -->
                        <tr class="{% if balance >= 0 %}bg-green-50{% else %}bg-yellow-50{% endif %}">
                            <td class="px-6 py-4 font-semibold {% if balance >= 0 %}text-green-800{% else %}text-yellow-800{% endif %}">Net Balance</td>
                            <td class="px-6 py-4 font-semibold {% if balance >= 0 %}text-green-800{% else %}text-yellow-800{% endif %}">{{ balance|format_khr }}</td>
                            <td class="px-6 py-4 font-semibold {% if balance >= 0 %}text-green-800{% else %}text-yellow-800{% endif %}">
                                {% if income_total > 0 %}
                                    {{ balance_percentage }}% of income
                                {% else %}
                                    N/A
                                {% endif %}
                            </td>
                        </tr>
                    </tbody>
                </table>
            </div>
        </div>

        <!-- Monthly Trend Chart -->
        <div class="bg-white rounded-lg shadow-sm p-6 mb-6">
            <h3 class="text-xl font-bold text-gray-800 mb-4">Monthly Financial Trend</h3>
            <div class="h-80">
                <canvas id="monthlyTrendChart"></canvas>
            </div>
        </div>

        <!-- Report Links -->
        <div class="grid grid-cols-1 md:grid-cols-2 gap-6 mb-6">
            <a href="{% url 'financialreport:income_report' %}?filter={{ filter }}&start_date={{ start_date }}&end_date={{ end_date }}" class="bg-white rounded-lg shadow-sm p-6 border border-gray-200 hover:border-blue-500 transition-all hover:shadow-md group">
                <div class="flex items-center justify-between mb-4">
                    <h3 class="text-xl font-bold text-gray-800 group-hover:text-blue-600 transition-colors">Income Report</h3>
                    <div class="bg-blue-100 p-3 rounded-full">
                        <i class="fa-solid fa-money-bill-trend-up text-blue-600 text-xl"></i>
                    </div>
                </div>
                <p class="text-gray-600 mb-4">Detailed breakdown of all income sources and transactions.</p>
                <div class="text-blue-600 font-medium flex items-center">
                    View Report <i class="fa-solid fa-arrow-right ml-2 group-hover:translate-x-1 transition-transform"></i>
                </div>
            </a>

            <a href="{% url 'financialreport:expense_report' %}?filter={{ filter }}&start_date={{ start_date }}&end_date={{ end_date }}" class="bg-white rounded-lg shadow-sm p-6 border border-gray-200 hover:border-red-500 transition-all hover:shadow-md group">
                <div class="flex items-center justify-between mb-4">
                    <h3 class="text-xl font-bold text-gray-800 group-hover:text-red-600 transition-colors">Expense Report</h3>
                    <div class="bg-red-100 p-3 rounded-full">
                        <i class="fa-solid fa-money-bill-transfer text-red-600 text-xl"></i>
                    </div>
                </div>
                <p class="text-gray-600 mb-4">Complete analysis of all expenses by category and date.</p>
                <div class="text-red-600 font-medium flex items-center">
                    View Report <i class="fa-solid fa-arrow-right ml-2 group-hover:translate-x-1 transition-transform"></i>
                </div>
            </a>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block js %}
<script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
<script>
    document.addEventListener('DOMContentLoaded', function() {
        // Toggle custom date range form
        const customRangeBtn = document.getElementById('custom-range-btn');
        const customRangeForm = document.getElementById('custom-range-form');

        if (customRangeBtn) {
            customRangeBtn.addEventListener('click', function() {
                customRangeForm.classList.toggle('hidden');
            });
        }

        // Toggle PDF template dropdown
        const pdfExportBtn = document.getElementById('pdfExportBtn');
        const templateDropdown = document.getElementById('templateDropdown');

        if (pdfExportBtn && templateDropdown) {
            pdfExportBtn.addEventListener('click', function(e) {
                e.preventDefault();
                templateDropdown.classList.toggle('hidden');
                if (printTemplateDropdown) printTemplateDropdown.classList.add('hidden');
                if (csvTemplateDropdown) csvTemplateDropdown.classList.add('hidden');
            });

            // Close dropdown when clicking outside
            document.addEventListener('click', function(e) {
                if (!pdfExportBtn.contains(e.target) && !templateDropdown.contains(e.target)) {
                    templateDropdown.classList.add('hidden');
                }
            });
        }

        // Toggle Print template dropdown
        const printReportBtn = document.getElementById('printReportBtn');
        const printTemplateDropdown = document.getElementById('printTemplateDropdown');

        if (printReportBtn && printTemplateDropdown) {
            printReportBtn.addEventListener('click', function(e) {
                e.preventDefault();
                printTemplateDropdown.classList.toggle('hidden');
                if (templateDropdown) templateDropdown.classList.add('hidden');
                if (csvTemplateDropdown) csvTemplateDropdown.classList.add('hidden');
            });

            // Close dropdown when clicking outside
            document.addEventListener('click', function(e) {
                if (!printReportBtn.contains(e.target) && !printTemplateDropdown.contains(e.target)) {
                    printTemplateDropdown.classList.add('hidden');
                }
            });
        }

        // Toggle CSV template dropdown
        const csvExportBtn = document.getElementById('csvExportBtn');
        const csvTemplateDropdown = document.getElementById('csvTemplateDropdown');

        if (csvExportBtn && csvTemplateDropdown) {
            csvExportBtn.addEventListener('click', function(e) {
                e.preventDefault();
                csvTemplateDropdown.classList.toggle('hidden');
                if (templateDropdown) templateDropdown.classList.add('hidden');
                if (printTemplateDropdown) printTemplateDropdown.classList.add('hidden');
            });

            // Close dropdown when clicking outside
            document.addEventListener('click', function(e) {
                if (!csvExportBtn.contains(e.target) && !csvTemplateDropdown.contains(e.target)) {
                    csvTemplateDropdown.classList.add('hidden');
                }
            });
        }

        // Chart.js Global Configuration
        Chart.defaults.font.family = "'Inter', 'Helvetica', 'Arial', sans-serif";
        Chart.defaults.font.size = 12;
        Chart.defaults.color = '#64748b';

        // Monthly Trend Chart
        fetch('/financialreport/api/monthly-data/')
            .then(response => response.json())
            .then(data => {
                const monthlyTrendCtx = document.getElementById('monthlyTrendChart').getContext('2d');

                // Create gradient for income
                const incomeGradient = monthlyTrendCtx.createLinearGradient(0, 0, 0, 400);
                incomeGradient.addColorStop(0, 'rgba(37, 99, 235, 0.4)');
                incomeGradient.addColorStop(1, 'rgba(37, 99, 235, 0)');

                // Create gradient for expenses
                const expenseGradient = monthlyTrendCtx.createLinearGradient(0, 0, 0, 400);
                expenseGradient.addColorStop(0, 'rgba(239, 68, 68, 0.4)');
                expenseGradient.addColorStop(1, 'rgba(239, 68, 68, 0)');

                // Create gradient for balance
                const balanceGradient = monthlyTrendCtx.createLinearGradient(0, 0, 0, 400);
                balanceGradient.addColorStop(0, 'rgba(34, 197, 94, 0.4)');
                balanceGradient.addColorStop(1, 'rgba(34, 197, 94, 0)');

                new Chart(monthlyTrendCtx, {
                    type: 'line',
                    data: {
                        labels: data.labels,
                        datasets: [
                            {
                                label: 'Income',
                                data: data.income,
                                borderColor: 'rgba(37, 99, 235, 1)',
                                backgroundColor: incomeGradient,
                                borderWidth: 3,
                                tension: 0.3,
                                fill: true,
                                pointBackgroundColor: 'rgba(37, 99, 235, 1)',
                                pointBorderColor: '#fff',
                                pointBorderWidth: 2,
                                pointRadius: 4,
                                pointHoverRadius: 6
                            },
                            {
                                label: 'Expenses',
                                data: data.expense,
                                borderColor: 'rgba(239, 68, 68, 1)',
                                backgroundColor: expenseGradient,
                                borderWidth: 3,
                                tension: 0.3,
                                fill: true,
                                pointBackgroundColor: 'rgba(239, 68, 68, 1)',
                                pointBorderColor: '#fff',
                                pointBorderWidth: 2,
                                pointRadius: 4,
                                pointHoverRadius: 6
                            },
                            {
                                label: 'Balance',
                                data: data.balance,
                                borderColor: 'rgba(34, 197, 94, 1)',
                                backgroundColor: balanceGradient,
                                borderWidth: 3,
                                tension: 0.3,
                                fill: true,
                                pointBackgroundColor: 'rgba(34, 197, 94, 1)',
                                pointBorderColor: '#fff',
                                pointBorderWidth: 2,
                                pointRadius: 4,
                                pointHoverRadius: 6
                            }
                        ]
                    },
                    options: {
                        responsive: true,
                        maintainAspectRatio: false,
                        interaction: {
                            mode: 'index',
                            intersect: false
                        },
                        plugins: {
                            legend: {
                                position: 'top',
                                labels: {
                                    usePointStyle: true,
                                    pointStyle: 'circle',
                                    padding: 20
                                }
                            },
                            tooltip: {
                                backgroundColor: 'rgba(17, 24, 39, 0.9)',
                                padding: 12,
                                titleFont: {
                                    size: 14,
                                    weight: 'bold'
                                },
                                bodyFont: {
                                    size: 13
                                },
                                callbacks: {
                                    label: function(context) {
                                        let label = context.dataset.label || '';
                                        let value = context.parsed.y;
                                        return `${label}: ${value.toLocaleString()} KHR`;
                                    }
                                }
                            }
                        },
                        scales: {
                            x: {
                                grid: {
                                    display: false
                                }
                            },
                            y: {
                                beginAtZero: true,
                                grid: {
                                    color: 'rgba(226, 232, 240, 0.6)'
                                },
                                ticks: {
                                    callback: function(value) {
                                        return value.toLocaleString() + ' KHR';
                                    }
                                }
                            }
                        }
                    }
                });
            })
            .catch(error => {
                console.error('Error fetching monthly data:', error);
            });
    });
</script>
{% endblock %}
