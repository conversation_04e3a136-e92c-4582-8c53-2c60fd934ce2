# Generated by Django 5.0.2 on 2025-05-19 22:15

import django.db.models.deletion
from django.conf import settings
from django.db import migrations, models


class Migration(migrations.Migration):

    initial = True

    dependencies = [
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
    ]

    operations = [
        migrations.CreateModel(
            name='TransactionTemplate',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('name', models.CharField(max_length=100, verbose_name='Template Name')),
                ('is_default', models.BooleanField(default=False, verbose_name='Default Template')),
                ('language', models.CharField(choices=[('en', 'English'), ('km', 'Khmer'), ('both', 'Both (Bilingual)')], default='en', max_length=10, verbose_name='Language')),
                ('header_text', models.Char<PERSON><PERSON>(default='LEGEND FITNESS', max_length=200, verbose_name='Header Text')),
                ('subheader_text', models.CharField(default='Transaction Receipt', max_length=200, verbose_name='Subheader Text')),
                ('footer_text', models.CharField(default='Thank you for your business!', max_length=200, verbose_name='Footer Text')),
                ('company_logo', models.ImageField(blank=True, null=True, upload_to='transaction_templates/', verbose_name='Company Logo')),
                ('background_color', models.CharField(default='#ffffff', max_length=20, verbose_name='Background Color')),
                ('text_color', models.CharField(default='#000000', max_length=20, verbose_name='Text Color')),
                ('accent_color', models.CharField(default='#0c4a6e', max_length=20, verbose_name='Accent Color')),
                ('show_company_info', models.BooleanField(default=True, verbose_name='Show Company Info')),
                ('show_signatures', models.BooleanField(default=True, verbose_name='Show Signature Lines')),
                ('custom_css', models.TextField(blank=True, null=True, verbose_name='Custom CSS')),
                ('created_at', models.DateTimeField(auto_now_add=True, verbose_name='Created At')),
                ('updated_at', models.DateTimeField(auto_now=True, verbose_name='Updated At')),
            ],
            options={
                'verbose_name': 'Transaction Template',
                'verbose_name_plural': 'Transaction Templates',
                'ordering': ['-is_default', 'name'],
            },
        ),
        migrations.CreateModel(
            name='Transaction',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('transaction_id', models.CharField(max_length=30, unique=True, verbose_name='Transaction ID')),
                ('transaction_type', models.CharField(choices=[('deposit', 'Deposit'), ('withdrawal', 'Withdrawal')], max_length=20, verbose_name='Transaction Type')),
                ('amount_khr', models.IntegerField(verbose_name='Amount (KHR)')),
                ('amount_usd', models.DecimalField(blank=True, decimal_places=2, max_digits=10, null=True, verbose_name='Amount (USD)')),
                ('payment_method', models.CharField(choices=[('cash', 'Cash'), ('aba', 'ABA'), ('wing', 'Wing'), ('bank', 'Bank Transfer'), ('other', 'Other')], default='cash', max_length=20, verbose_name='Payment Method')),
                ('source', models.CharField(blank=True, choices=[('membership', 'Membership Sales'), ('product', 'Product Sales'), ('paypervisit', 'Pay-per-visit'), ('other', 'Other')], max_length=20, null=True, verbose_name='Source of Funds')),
                ('purpose', models.CharField(blank=True, choices=[('salary', 'Salary Payment'), ('supplier', 'Supplier Payment'), ('utility', 'Utility Bill'), ('equipment', 'Equipment Purchase'), ('maintenance', 'Maintenance'), ('other', 'Other')], max_length=20, null=True, verbose_name='Purpose')),
                ('notes', models.TextField(blank=True, null=True, verbose_name='Notes')),
                ('status', models.CharField(choices=[('completed', 'Completed'), ('pending', 'Pending'), ('rejected', 'Rejected')], default='completed', max_length=20, verbose_name='Status')),
                ('transaction_date', models.DateTimeField(auto_now_add=True, verbose_name='Transaction Date')),
                ('updated_at', models.DateTimeField(auto_now=True, verbose_name='Updated At')),
                ('approved_by', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='approved_transactions', to=settings.AUTH_USER_MODEL)),
                ('staff', models.ForeignKey(null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='transactions', to=settings.AUTH_USER_MODEL)),
            ],
            options={
                'verbose_name': 'Transaction',
                'verbose_name_plural': 'Transactions',
                'ordering': ['-transaction_date'],
            },
        ),
    ]
