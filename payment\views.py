from django.shortcuts import render, redirect, get_object_or_404
from django.contrib import messages
from django.contrib.auth.decorators import login_required
from datetime import datetime, timedelta
from django.db.models import F
from .models import Payment, PaymentTemplate
from members.models import Member
from user.models import MetaData
from core.utils import generate_unique_transaction_id
from core.decorators import module_permission_required

@login_required
@module_permission_required(module='payment', required_level='view')
def index(request):
    """
    Display list of payments
    """
    try:
        members = Member.objects.filter(status=True)
    except:
        members = Member.objects.all()

    # Get filter parameters
    member_filter = request.GET.get('member', '')
    date_from = request.GET.get('date_from', '')
    date_to = request.GET.get('date_to', '')
    payment_method = request.GET.get('payment_method', '')

    # Start with all payments
    payments = Payment.objects.all().order_by('-payment_date')

    # Apply filters if provided
    if member_filter:
        payments = payments.filter(member__id=member_filter)

    if date_from:
        try:
            date_from = datetime.strptime(date_from, '%Y-%m-%d')
            payments = payments.filter(payment_date__gte=date_from)
        except ValueError:
            pass

    if date_to:
        try:
            date_to = datetime.strptime(date_to, '%Y-%m-%d')
            # Add one day to include the end date
            date_to = date_to + timedelta(days=1)
            payments = payments.filter(payment_date__lt=date_to)
        except ValueError:
            pass

    if payment_method:
        payments = payments.filter(payment_method=payment_method)

    # Calculate totals
    total_khr = sum(payment.amount_khr for payment in payments)
    total_usd = sum(payment.amount_usd or 0 for payment in payments)

    # Limit to 50 for display
    payments = payments[:50]

    context = {
        'members': members,
        'payments': payments,
        'total_khr': total_khr,
        'total_usd': total_usd,
        'member_filter': member_filter,
        'date_from': date_from.strftime('%Y-%m-%d') if isinstance(date_from, datetime) else date_from,
        'date_to': (date_to - timedelta(days=1)).strftime('%Y-%m-%d') if isinstance(date_to, datetime) else date_to,
        'payment_method': payment_method,
        'payment_methods': Payment.PAYMENT_METHOD_CHOICES,
    }
    return render(request, 'payment/index.html', context)

@login_required
@module_permission_required(module='payment', required_level='edit')
def create_payment(request):
    """
    Create a new payment
    """
    try:
        members = Member.objects.filter(status=True)
    except:
        members = Member.objects.all()

    if request.method == "POST":
        try:
            member_id = request.POST.get("member")
            amount_khr = int(request.POST.get("amount_khr"))
            payment_method = request.POST.get("payment_method")
            notes = request.POST.get("notes")

            # Get the selected member
            member = Member.objects.get(id=member_id)

            # Generate unique invoice number
            invoice_no = generate_unique_transaction_id(prefix="INV")

            # Create the payment
            payment = Payment.objects.create(
                invoice_no=invoice_no,
                member=member,
                amount_khr=amount_khr,
                payment_method=payment_method,
                collector=request.user,
                notes=notes
            )

            # Calculate USD amount if provided
            amount_usd = request.POST.get("amount_usd")
            if amount_usd:
                payment.amount_usd = float(amount_usd)
                payment.save()

            # Get the package price
            package_price = member.package.price_khr

            # Calculate the remaining due after this payment
            # First, get the current due amount
            current_due = float(member.due_payment)

            # Check if this is an additional payment (member has already paid in full)
            # We need to distinguish between:
            # 1. A member who has already paid (current_due = 0, payment_status = 'paid')
            # 2. A member who hasn't made any payments yet (current_due = 0 by default)
            if current_due <= 0 and member.payment_status == 'paid':
                # This is an additional payment for a member who has already paid

                # Add a note about this being an additional payment
                if not notes:
                    notes = "Additional payment"
                else:
                    notes = f"Additional payment - {notes}"

                payment.notes = notes
                payment.save()

                # Options for handling additional payments:
                # 1. Extend membership based on payment amount

                # Calculate how many days to extend based on payment amount
                # Formula: (payment / package price) * package duration in days
                package_duration_days = member.package.duration * 30  # Approximate month to days
                extension_ratio = amount_khr / package_price
                days_to_extend = int(extension_ratio * package_duration_days)

                # Extend the membership end date
                current_end_date = member.end_date
                new_end_date = current_end_date + timedelta(days=days_to_extend)
                member.end_date = new_end_date
                member.save()

                # Add a special message
                messages.success(request,
                    f"Additional payment of {amount_khr}៛ recorded for {member.name}. " +
                    f"Membership extended by {days_to_extend} days (from {current_end_date.strftime('%d-%b-%Y')} to {new_end_date.strftime('%d-%b-%Y')}).")

                # Update gym funds
                meta = MetaData.objects.last()
                meta.funds += amount_khr
                meta.save()

                return redirect('payment:index')

            # Normal payment processing for members with dues
            # If current due is 0, use the package price as the total amount due
            total_due = current_due if current_due > 0 else package_price

            # Calculate remaining due after this payment
            remaining_due = max(0, total_due - amount_khr)

            # Update member's payment status based on remaining due
            if remaining_due > 0:
                member.payment_status = 'pending'
                member.due_payment = remaining_due
            else:
                member.payment_status = 'paid'
                member.due_payment = 0

            member.save()

            # Update gym funds
            meta = MetaData.objects.last()
            meta.funds += amount_khr
            meta.save()

            # Create a more informative success message
            if remaining_due > 0:
                messages.success(request, f"Payment of {amount_khr}៛ recorded successfully for {member.name}. Remaining due: {int(remaining_due)}៛")
            else:
                messages.success(request, f"Payment of {amount_khr}៛ recorded successfully for {member.name}. Payment completed!")

            return redirect('payment:index')

        except Exception as e:
            messages.error(request, f"Error recording payment: {str(e)}")

    context = {
        'members': members,
    }
    return render(request, 'payment/payment_form.html', context)

@login_required
@module_permission_required(module='payment', required_level='view')
def print_receipt(request, pk):
    """
    Print payment receipt
    """
    payment = get_object_or_404(Payment, pk=pk)

    # Get the default template or the first available template
    template = PaymentTemplate.objects.filter(is_default=True).first()
    if not template:
        template = PaymentTemplate.objects.first()

    # If a specific template is requested
    template_id = request.GET.get('template')
    if template_id:
        requested_template = PaymentTemplate.objects.filter(id=template_id).first()
        if requested_template:
            template = requested_template

    context = {
        'payment': payment,
        'template': template,
        'all_templates': PaymentTemplate.objects.all(),
    }
    return render(request, 'payment/print_receipt.html', context)

@login_required
@module_permission_required(module='payment', required_level='edit')
def bulk_actions(request):
    """
    Handle bulk actions for payments
    """
    if request.method != "POST":
        return redirect('payment:index')

    action = request.POST.get('bulk_action')
    selected_payments = request.POST.getlist('selected_payments')

    if not action or not selected_payments:
        messages.error(request, "No action or payments selected.")
        return redirect('payment:index')

    if action == 'delete':
        try:
            # Get the selected payments
            payments = Payment.objects.filter(id__in=selected_payments)
            count = payments.count()

            # Delete the payments
            payments.delete()

            messages.success(request, f"{count} payment(s) deleted successfully.")
        except Exception as e:
            messages.error(request, f"Error deleting payments: {str(e)}")

    elif action == 'print':
        try:
            # Get the selected payments
            payments = Payment.objects.filter(id__in=selected_payments)

            # Render the bulk print template
            context = {
                'payments': payments,
            }
            return render(request, 'payment/bulk_print.html', context)
        except Exception as e:
            messages.error(request, f"Error printing receipts: {str(e)}")

    return redirect('payment:index')

@login_required
@module_permission_required(module='payment', required_level='full')
def delete_payment(request, pk):
    """
    Delete a payment record and reverse its financial impact
    """
    payment = get_object_or_404(Payment, pk=pk)

    # Store information for success message
    invoice_no = payment.invoice_no
    amount_khr = payment.amount_khr
    member_name = payment.member.name
    collector = payment.collector.username if payment.collector else "Unknown"
    payment_method = payment.get_payment_method_display()

    try:
        # Reverse the financial impact by subtracting the amount from funds
        meta = MetaData.objects.last()
        if meta:
            meta.funds -= amount_khr
            meta.save()

        # Delete the payment
        payment.delete()

        # Format amount for display
        formatted_amount = f"{amount_khr:,}៛"

        messages.success(
            request,
            f"Payment {invoice_no} deleted successfully! "
            f"Amount {formatted_amount} for {member_name} ({payment_method}) has been deducted from gym funds. "
            f"(Originally processed by: {collector})"
        )

    except Exception as e:
        messages.error(request, f"Error deleting payment {invoice_no}: {str(e)}")

    return redirect('payment:index')

@login_required
@module_permission_required(module='payment', required_level='view')
def template_list(request):
    """
    List all payment receipt templates
    """
    templates = PaymentTemplate.objects.all().order_by('-is_default', 'name')

    context = {
        'templates': templates,
    }
    return render(request, 'payment/template_list.html', context)

@login_required
@module_permission_required(module='payment', required_level='edit')
def create_template(request):
    """
    Create a new payment receipt template
    """
    if request.method == "POST":
        try:
            name = request.POST.get("name")
            language = request.POST.get("language")
            header_text = request.POST.get("header_text")
            subheader_text = request.POST.get("subheader_text")
            footer_text = request.POST.get("footer_text")
            background_color = request.POST.get("background_color")
            text_color = request.POST.get("text_color")
            accent_color = request.POST.get("accent_color")
            is_default = request.POST.get("is_default") == "on"
            show_company_info = request.POST.get("show_company_info") == "on"
            show_signatures = request.POST.get("show_signatures") == "on"
            custom_css = request.POST.get("custom_css")

            template = PaymentTemplate.objects.create(
                name=name,
                language=language,
                header_text=header_text,
                subheader_text=subheader_text,
                footer_text=footer_text,
                background_color=background_color,
                text_color=text_color,
                accent_color=accent_color,
                is_default=is_default,
                show_company_info=show_company_info,
                show_signatures=show_signatures,
                custom_css=custom_css
            )

            # Handle logo upload
            if 'company_logo' in request.FILES:
                template.company_logo = request.FILES['company_logo']
                template.save()

            messages.success(request, f"Template '{name}' created successfully.")
            return redirect('payment:template_list')

        except Exception as e:
            messages.error(request, f"Error creating template: {str(e)}")

    context = {
        'languages': PaymentTemplate.LANGUAGE_CHOICES,
    }
    return render(request, 'payment/create_template.html', context)

@login_required
@module_permission_required(module='payment', required_level='edit')
def edit_template(request, pk):
    """
    Edit a payment receipt template
    """
    template = get_object_or_404(PaymentTemplate, pk=pk)

    if request.method == "POST":
        try:
            template.name = request.POST.get("name")
            template.language = request.POST.get("language")
            template.header_text = request.POST.get("header_text")
            template.subheader_text = request.POST.get("subheader_text")
            template.footer_text = request.POST.get("footer_text")
            template.background_color = request.POST.get("background_color")
            template.text_color = request.POST.get("text_color")
            template.accent_color = request.POST.get("accent_color")
            template.is_default = request.POST.get("is_default") == "on"
            template.show_company_info = request.POST.get("show_company_info") == "on"
            template.show_signatures = request.POST.get("show_signatures") == "on"
            template.custom_css = request.POST.get("custom_css")

            # Handle logo upload
            if 'company_logo' in request.FILES:
                template.company_logo = request.FILES['company_logo']

            template.save()

            messages.success(request, f"Template '{template.name}' updated successfully.")
            return redirect('payment:template_list')

        except Exception as e:
            messages.error(request, f"Error updating template: {str(e)}")

    context = {
        'template': template,
        'languages': PaymentTemplate.LANGUAGE_CHOICES,
    }
    return render(request, 'payment/edit_template.html', context)

@login_required
@module_permission_required(module='payment', required_level='full')
def delete_template(request, pk):
    """
    Delete a payment receipt template
    """
    template = get_object_or_404(PaymentTemplate, pk=pk)

    # Don't delete the last template
    if PaymentTemplate.objects.count() <= 1:
        messages.error(request, "Cannot delete the last template.")
        return redirect('payment:template_list')

    name = template.name
    template.delete()

    messages.success(request, f"Template '{name}' deleted successfully.")
    return redirect('payment:template_list')

@login_required
@module_permission_required(module='payment', required_level='view')
def preview_template(request, pk):
    """
    Preview a payment receipt template
    """
    template = get_object_or_404(PaymentTemplate, pk=pk)

    # Get a sample payment for preview
    sample_payment = Payment.objects.first()
    if not sample_payment:
        # Create a dummy payment for preview if no real payments exist
        sample_payment = {
            'invoice_no': 'SAMPLE-001',
            'member': {
                'name': 'John Doe',
                'member_id': 'MEM-001',
            },
            'amount_khr': 100000,
            'amount_usd': 25.00,
            'payment_date': datetime.now(),
            'payment_method': 'cash',
            'collector': {
                'username': 'admin'
            },
            'notes': 'This is a sample payment for template preview.',
            'get_payment_method_display': lambda: 'Cash',
        }

    context = {
        'template': template,
        'payment': sample_payment,
        'is_preview': True
    }
    return render(request, 'payment/preview_template.html', context)