# Redis Setup Guide for Windows

## Option 1: Manual Redis Installation (Recommended)

### Step 1: Download Redis
1. Go to: https://github.com/microsoftarchive/redis/releases
2. Download: `Redis-x64-3.0.504.zip`
3. Extract to: `C:\Redis\`

### Step 2: Start Redis Server
1. Open Command Prompt as Administrator
2. Navigate to Redis directory:
   ```cmd
   cd C:\Redis
   ```
3. Start Redis server:
   ```cmd
   redis-server.exe
   ```

### Step 3: Test Redis Connection
Open another Command Prompt and run:
```cmd
cd C:\Redis
redis-cli.exe ping
```
Expected response: `PONG`

## Option 2: Using WSL (Windows Subsystem for Linux)

### Step 1: Install WSL
```powershell
wsl --install
```

### Step 2: Install Redis in WSL
```bash
sudo apt update
sudo apt install redis-server
sudo service redis-server start
```

### Step 3: Test Redis
```bash
redis-cli ping
```

## Option 3: Docker (If Available)

### Step 1: Install Docker Desktop
Download from: https://www.docker.com/products/docker-desktop

### Step 2: Run Redis Container
```cmd
docker run -d -p 6379:6379 --name redis redis:alpine
```

### Step 3: Test Redis
```cmd
docker exec -it redis redis-cli ping
```

## Verification Commands

### Test Redis Connection
```cmd
# Windows
C:\Redis\redis-cli.exe ping

# WSL/Linux
redis-cli ping

# Expected output: PONG
```

### Test Basic Operations
```cmd
# Set a value
redis-cli set test "Hello Redis"

# Get the value
redis-cli get test

# Delete the value
redis-cli del test
```

## Redis Configuration for Legend Fitness Club

Create `redis.conf` file:
```conf
# Basic Redis Configuration
port 6379
bind 127.0.0.1
timeout 0
tcp-keepalive 60
loglevel notice
databases 16
save 900 1
save 300 10
save 60 10000
maxmemory 256mb
maxmemory-policy allkeys-lru
```

## Troubleshooting

### Redis Won't Start
1. Check if port 6379 is available:
   ```cmd
   netstat -an | findstr :6379
   ```
2. Kill existing Redis processes:
   ```cmd
   taskkill /f /im redis-server.exe
   ```

### Connection Refused
1. Verify Redis is running:
   ```cmd
   redis-cli ping
   ```
2. Check firewall settings
3. Ensure Redis is bound to 127.0.0.1

### Performance Issues
1. Increase maxmemory in redis.conf
2. Use appropriate eviction policy
3. Monitor Redis with:
   ```cmd
   redis-cli info memory
   ```
