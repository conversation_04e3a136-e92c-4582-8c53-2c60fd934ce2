import os
import re
import sys

def remove_load_i18n(file_path):
    """Remove {% load i18n %} tag from a template file."""
    with open(file_path, 'r', encoding='utf-8', errors='ignore') as f:
        content = f.read()

    # Keep track of original content to check if changes were made
    original_content = content

    # Remove {% load i18n %} tag
    content = re.sub(r'{%\s*load\s+i18n\s*%}\n?', '', content)

    # Check if any changes were made
    if content == original_content:
        print(f"No load i18n tag found in {file_path}")
        return False

    # Write the modified content back to the file
    with open(file_path, 'w', encoding='utf-8') as f:
        f.write(content)

    print(f"Removed load i18n tag from {file_path}")
    return True

def find_html_files(directory):
    """Find all HTML files in the given directory and its subdirectories."""
    html_files = []
    for root, dirs, files in os.walk(directory):
        for file in files:
            if file.endswith('.html'):
                html_files.append(os.path.join(root, file))
    return html_files

def main():
    if len(sys.argv) < 2:
        print("Usage: python remove_load_i18n.py <template_path_or_directory>")
        print("  - Provide a specific template file path to process a single file")
        print("  - Provide a directory path to process all HTML files in that directory and subdirectories")
        sys.exit(1)

    path = sys.argv[1]

    if os.path.isfile(path):
        # Process a single file
        if path.endswith('.html'):
            remove_load_i18n(path)
        else:
            print(f"Error: {path} is not an HTML file")
    elif os.path.isdir(path):
        # Process all HTML files in the directory and subdirectories
        html_files = find_html_files(path)
        if not html_files:
            print(f"No HTML files found in {path}")
            sys.exit(1)

        processed_count = 0
        for html_file in html_files:
            if remove_load_i18n(html_file):
                processed_count += 1

        print(f"Processed {len(html_files)} HTML files. Removed load i18n tag from {processed_count} files.")
    else:
        print(f"Error: {path} does not exist")
        sys.exit(1)

if __name__ == "__main__":
    main()
