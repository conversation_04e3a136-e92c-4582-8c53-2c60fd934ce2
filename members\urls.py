from django.urls import path
from . import views

app_name = "member"

urlpatterns = [
    path('', views.index, name='index'),
    path('create/', views.create_member, name='create_member'),  # New URL for member creation
    path('edit/<int:pk>/', views.edit, name='edit'),
    path('print_card/<int:pk>/', views.print_card, name='print_card'),
    path('deactivate/<int:pk>/', views.deactivate, name='deactivate'),
    path('activate/<int:pk>/', views.activate, name='activate'),
    path('delete/<int:pk>/', views.delete, name='delete'),
    path('bill/', views.bill, name='bill'),  # Add URL for bill view
    path('clean-data/', views.clean_data, name='clean_data'),  # Add URL for data cleaning

    # Package management URLs
    path('package/', views.package_list, name='package_list'),
    path('package/create/', views.package_create, name='package_create'),
    path('package/edit/<int:pk>/', views.package_edit, name='package_edit'),
    path('package/delete/<int:pk>/', views.package_delete, name='package_delete'),
]
