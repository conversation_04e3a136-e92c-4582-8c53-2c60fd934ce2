from django.shortcuts import render, redirect, get_object_or_404
from django.contrib import messages
from django.contrib.auth.decorators import login_required
from django.http import JsonResponse, HttpResponse
from django.utils import timezone
from django.db.models import Sum
from django.template.loader import get_template
from datetime import datetime, timedelta
from io import BytesIO
from xhtml2pdf import pisa

from user.models import MetaData, User
from core.decorators import module_permission_required
from .models import Transaction, TransactionTemplate

@login_required
@module_permission_required(module='finance', required_level='view')
def index(request):
    """
    Main dashboard view for the finance module
    """
    # Get current balance
    metadata = MetaData.objects.first()
    balance = metadata.funds if metadata else 0

    # Get recent transactions (last 10)
    recent_transactions = Transaction.objects.all().order_by('-transaction_date')[:10]

    # Get summary statistics
    today = timezone.now().date()
    start_of_month = today.replace(day=1)

    # This month's deposits
    month_deposits = Transaction.objects.filter(
        transaction_type='deposit',
        status='completed',
        transaction_date__date__gte=start_of_month,
        transaction_date__date__lte=today
    ).aggregate(total=Sum('amount_khr'))['total'] or 0

    # This month's withdrawals
    month_withdrawals = Transaction.objects.filter(
        transaction_type='withdrawal',
        status='completed',
        transaction_date__date__gte=start_of_month,
        transaction_date__date__lte=today
    ).aggregate(total=Sum('amount_khr'))['total'] or 0

    # Pending withdrawals that need approval
    pending_withdrawals = Transaction.objects.filter(
        transaction_type='withdrawal',
        status='pending'
    ).count()

    context = {
        'balance': balance,
        'recent_transactions': recent_transactions,
        'month_deposits': month_deposits,
        'month_withdrawals': month_withdrawals,
        'pending_withdrawals': pending_withdrawals,
    }

    return render(request, 'finance/index.html', context)

@login_required
@module_permission_required(module='finance', required_level='edit')
def deposit(request):
    """
    View for creating a new deposit
    """
    if request.method == 'POST':
        # Get form data
        amount_khr = request.POST.get('amount_khr')
        payment_method = request.POST.get('payment_method')
        notes = request.POST.get('notes')

        try:
            # Create the deposit transaction
            transaction = Transaction.objects.create(
                transaction_type='deposit',
                amount_khr=int(amount_khr),
                payment_method=payment_method,
                notes=notes,
                staff=request.user,
                status='completed'  # Deposits are automatically completed
            )

            messages.success(request, f"Deposit of {amount_khr} ៛ recorded successfully.")
            return redirect('finance:print_receipt', pk=transaction.id)

        except Exception as e:
            messages.error(request, f"Error creating deposit: {str(e)}")

    context = {
        'payment_methods': Transaction.PAYMENT_METHOD_CHOICES,
    }

    return render(request, 'finance/deposit.html', context)

@login_required
@module_permission_required(module='finance', required_level='edit')
def withdraw(request):
    """
    View for creating a new withdrawal
    """
    # Get current balance
    metadata = MetaData.objects.first()
    balance = metadata.funds if metadata else 0

    if request.method == 'POST':
        # Get form data
        amount_khr = int(request.POST.get('amount_khr', 0))
        payment_method = request.POST.get('payment_method')
        notes = request.POST.get('notes')

        # Check if there are sufficient funds
        if amount_khr > balance:
            messages.error(request, f"Insufficient funds. Available balance: {balance} ៛")
            return redirect('finance:withdraw')

        try:
            # Determine status based on user role
            status = 'completed' if request.user.role == 'admin' else 'pending'
            approved_by = request.user if status == 'completed' else None

            # Create the withdrawal transaction
            transaction = Transaction.objects.create(
                transaction_type='withdrawal',
                amount_khr=amount_khr,
                payment_method=payment_method,
                notes=notes,
                staff=request.user,
                status=status,
                approved_by=approved_by
            )

            if status == 'completed':
                messages.success(request, f"Withdrawal of {amount_khr} ៛ completed successfully.")
                return redirect('finance:print_receipt', pk=transaction.id)
            else:
                messages.success(request, f"Withdrawal request of {amount_khr} ៛ submitted for approval.")
                return redirect('finance:index')

        except Exception as e:
            messages.error(request, f"Error creating withdrawal: {str(e)}")

    context = {
        'balance': balance,
        'payment_methods': Transaction.PAYMENT_METHOD_CHOICES,
    }

    return render(request, 'finance/withdraw.html', context)

@login_required
@module_permission_required(module='finance', required_level='view')
def transaction_history(request):
    """
    View for transaction history
    """
    # Get filter parameters
    transaction_type = request.GET.get('type', '')
    status = request.GET.get('status', '')
    start_date = request.GET.get('start_date', '')
    end_date = request.GET.get('end_date', '')

    # Base queryset
    transactions = Transaction.objects.all()

    # Apply filters
    if transaction_type:
        transactions = transactions.filter(transaction_type=transaction_type)

    if status:
        transactions = transactions.filter(status=status)

    if start_date:
        try:
            start_date = datetime.strptime(start_date, '%Y-%m-%d').date()
            transactions = transactions.filter(transaction_date__date__gte=start_date)
        except ValueError:
            pass

    if end_date:
        try:
            end_date = datetime.strptime(end_date, '%Y-%m-%d').date()
            transactions = transactions.filter(transaction_date__date__lte=end_date)
        except ValueError:
            pass

    # Order by transaction date (newest first)
    transactions = transactions.order_by('-transaction_date')

    context = {
        'transactions': transactions,
        'transaction_types': Transaction.TRANSACTION_TYPE_CHOICES,
        'statuses': Transaction.STATUS_CHOICES,
        'selected_type': transaction_type,
        'selected_status': status,
        'start_date': start_date,
        'end_date': end_date,
    }

    return render(request, 'finance/history.html', context)

@login_required
@module_permission_required(module='finance', required_level='full')
def delete_transaction(request, pk):
    """
    Delete a finance transaction and reverse its financial impact
    """
    transaction = get_object_or_404(Transaction, pk=pk)

    # Store information for success message
    transaction_id = transaction.transaction_id
    amount_khr = transaction.amount_khr
    transaction_type = transaction.get_transaction_type_display()
    processed_by = transaction.staff.name if transaction.staff else "Unknown"
    transaction_date = transaction.transaction_date.strftime("%d %b %Y %H:%M")

    try:
        # Only reverse financial impact if the transaction was completed
        if transaction.status == 'completed':
            # Reverse the financial impact based on transaction type
            meta = MetaData.objects.last()
            if meta:
                if transaction.transaction_type == 'deposit':
                    # For deposits: subtract the amount from funds (opposite of addition during creation)
                    meta.funds -= amount_khr
                elif transaction.transaction_type == 'withdrawal':
                    # For withdrawals: add the amount back to funds (opposite of subtraction during creation)
                    meta.funds += amount_khr
                meta.save()

        # Delete the transaction
        transaction.delete()

        # Format amount for display
        formatted_amount = f"{amount_khr:,}៛"

        # Create success message based on transaction type and status
        if transaction.status == 'completed':
            if transaction.transaction_type == 'deposit':
                success_message = (
                    f"Deposit transaction {transaction_id} deleted successfully! "
                    f"Amount {formatted_amount} has been subtracted from gym funds. "
                    f"(Date: {transaction_date}, Processed by: {processed_by})"
                )
            else:  # withdrawal
                success_message = (
                    f"Withdrawal transaction {transaction_id} deleted successfully! "
                    f"Amount {formatted_amount} has been added back to gym funds. "
                    f"(Date: {transaction_date}, Processed by: {processed_by})"
                )
        else:
            success_message = (
                f"{transaction_type} transaction {transaction_id} deleted successfully! "
                f"Pending transaction of {formatted_amount} has been removed. "
                f"(Date: {transaction_date}, Processed by: {processed_by})"
            )

        messages.success(request, success_message)

    except Exception as e:
        messages.error(request, f"Error deleting transaction {transaction_id}: {str(e)}")

    return redirect('finance:history')


@login_required
@module_permission_required(module='finance', required_level='full')
def approve_transaction(request, pk):
    """
    API endpoint to approve a pending transaction
    """
    if request.method != 'POST':
        return JsonResponse({'status': 'error', 'message': 'Invalid request method'})

    transaction = get_object_or_404(Transaction, pk=pk)

    if transaction.status != 'pending':
        return JsonResponse({'status': 'error', 'message': 'Transaction is not pending'})

    # Check if there are sufficient funds for withdrawals
    if transaction.transaction_type == 'withdrawal':
        metadata = MetaData.objects.first()
        balance = metadata.funds if metadata else 0

        if transaction.amount_khr > balance:
            return JsonResponse({'status': 'error', 'message': f'Insufficient funds. Available balance: {balance} ៛'})

    # Update transaction status
    transaction.status = 'completed'
    transaction.approved_by = request.user
    transaction.save()

    return JsonResponse({'status': 'success', 'message': 'Transaction approved successfully'})

@login_required
@module_permission_required(module='finance', required_level='full')
def reject_transaction(request, pk):
    """
    API endpoint to reject a pending transaction
    """
    if request.method != 'POST':
        return JsonResponse({'status': 'error', 'message': 'Invalid request method'})

    transaction = get_object_or_404(Transaction, pk=pk)

    if transaction.status != 'pending':
        return JsonResponse({'status': 'error', 'message': 'Transaction is not pending'})

    # Update transaction status
    transaction.status = 'rejected'
    transaction.approved_by = request.user
    transaction.save()

    return JsonResponse({'status': 'success', 'message': 'Transaction rejected successfully'})

@login_required
@module_permission_required(module='finance', required_level='view')
def print_receipt(request, pk):
    """
    View for printing a transaction receipt
    """
    transaction = get_object_or_404(Transaction, pk=pk)

    # Get template
    template_id = request.GET.get('template')
    if template_id:
        template = get_object_or_404(TransactionTemplate, pk=template_id)
    else:
        # Get default template or first available
        template = TransactionTemplate.objects.filter(is_default=True).first()
        if not template:
            template = TransactionTemplate.objects.first()
            if not template:
                # Create a default template if none exists
                template = TransactionTemplate.objects.create(
                    name="Default Template",
                    is_default=True
                )

    # Get all templates for the selector
    all_templates = TransactionTemplate.objects.all()

    context = {
        'transaction': transaction,
        'template': template,
        'all_templates': all_templates,
    }

    return render(request, 'finance/print_receipt.html', context)

# Template management views
@login_required
@module_permission_required(module='finance', required_level='view')
def template_list(request):
    """
    View for listing receipt templates
    """
    templates = TransactionTemplate.objects.all()

    context = {
        'templates': templates,
    }

    return render(request, 'finance/template_list.html', context)

@login_required
@module_permission_required(module='finance', required_level='edit')
def create_template(request):
    """
    View for creating a new receipt template
    """
    if request.method == 'POST':
        # Get form data
        name = request.POST.get('name')
        language = request.POST.get('language')
        header_text = request.POST.get('header_text')
        subheader_text = request.POST.get('subheader_text')
        footer_text = request.POST.get('footer_text')
        background_color = request.POST.get('background_color')
        text_color = request.POST.get('text_color')
        accent_color = request.POST.get('accent_color')
        is_default = request.POST.get('is_default') == 'on'
        show_company_info = request.POST.get('show_company_info') == 'on'
        show_signatures = request.POST.get('show_signatures') == 'on'
        custom_css = request.POST.get('custom_css')

        try:
            template = TransactionTemplate.objects.create(
                name=name,
                language=language,
                header_text=header_text,
                subheader_text=subheader_text,
                footer_text=footer_text,
                background_color=background_color,
                text_color=text_color,
                accent_color=accent_color,
                is_default=is_default,
                show_company_info=show_company_info,
                show_signatures=show_signatures,
                custom_css=custom_css
            )

            # Handle logo upload
            if 'company_logo' in request.FILES:
                template.company_logo = request.FILES['company_logo']
                template.save()

            messages.success(request, f"Template '{name}' created successfully.")
            return redirect('finance:template_list')

        except Exception as e:
            messages.error(request, f"Error creating template: {str(e)}")

    context = {
        'languages': TransactionTemplate.LANGUAGE_CHOICES,
    }

    return render(request, 'finance/create_template.html', context)

@login_required
@module_permission_required(module='finance', required_level='edit')
def edit_template(request, pk):
    """
    View for editing a receipt template
    """
    template = get_object_or_404(TransactionTemplate, pk=pk)

    if request.method == 'POST':
        # Get form data
        template.name = request.POST.get('name')
        template.language = request.POST.get('language')
        template.header_text = request.POST.get('header_text')
        template.subheader_text = request.POST.get('subheader_text')
        template.footer_text = request.POST.get('footer_text')
        template.background_color = request.POST.get('background_color')
        template.text_color = request.POST.get('text_color')
        template.accent_color = request.POST.get('accent_color')
        template.is_default = request.POST.get('is_default') == 'on'
        template.show_company_info = request.POST.get('show_company_info') == 'on'
        template.show_signatures = request.POST.get('show_signatures') == 'on'
        template.custom_css = request.POST.get('custom_css')

        try:
            # Handle logo upload
            if 'company_logo' in request.FILES:
                template.company_logo = request.FILES['company_logo']

            template.save()

            messages.success(request, f"Template '{template.name}' updated successfully.")
            return redirect('finance:template_list')

        except Exception as e:
            messages.error(request, f"Error updating template: {str(e)}")

    context = {
        'template': template,
        'languages': TransactionTemplate.LANGUAGE_CHOICES,
    }

    return render(request, 'finance/edit_template.html', context)

@login_required
@module_permission_required(module='finance', required_level='view')
def preview_template(request, pk):
    """
    View for previewing a receipt template
    """
    template = get_object_or_404(TransactionTemplate, pk=pk)

    # Create a sample transaction for preview
    sample_transaction = {
        'transaction_id': 'DEP-20250101-SAMPLE',
        'transaction_type': 'deposit',
        'amount_khr': 500000,
        'payment_method': 'cash',
        'notes': 'Sample transaction for template preview',
        'staff': request.user,
        'transaction_date': timezone.now(),
    }

    context = {
        'template': template,
        'transaction': sample_transaction,
        'is_preview': True,
    }

    return render(request, 'finance/preview_template.html', context)

@login_required
@module_permission_required(module='finance', required_level='full')
def delete_template(request, pk):
    """
    View for deleting a receipt template
    """
    template = get_object_or_404(TransactionTemplate, pk=pk)

    if request.method == 'POST':
        try:
            template_name = template.name
            template.delete()
            messages.success(request, f"Template '{template_name}' deleted successfully.")
        except Exception as e:
            messages.error(request, f"Error deleting template: {str(e)}")

    return redirect('finance:template_list')
