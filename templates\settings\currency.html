{% extends 'base.html' %}
{% load static %}
{% load currency_filters %}


{% block body %}
<div class="conponentSection p-5 bg-gray-200">
    <div class="componentWrapper">
        <!-- Header with Back Button -->
        <div class="flex items-center mb-4">
            <a href="{% url 'settings:dashboard' %}" class="bg-blue-900 text-white p-2 rounded-full hover:bg-blue-800 mr-3">
                <i class="fas fa-arrow-left"></i>
            </a>
            <h2 class="text-2xl font-bold">Currency Settings</h2>
        </div>

        <!-- Settings Card -->
        <div class="bg-white rounded-lg shadow-md overflow-hidden">
            <!-- Card Header -->
            <div class="bg-blue-900 text-white p-4">
                <div class="flex items-center">
                    <i class="fas fa-money-bill-wave text-2xl mr-3"></i>
                    <h3 class="text-xl font-bold">Exchange Rate Configuration</h3>
                </div>
            </div>

            <!-- Card Body -->
            <div class="p-6">
                <form method="post">
                    {% csrf_token %}

                    <div class="mb-6">
                        <h4 class="text-lg font-semibold mb-3">Current Exchange Rate</h4>

                        <div class="p-4 bg-blue-50 rounded-lg mb-4">
                            <div class="text-center">
                                <span class="text-2xl font-bold">$1.00 = {{ settings.exchange_rate_usd_to_khr|format_number_with_commas }}៛</span>
                            </div>
                        </div>

                        <div class="mb-4">
                            <label class="block text-sm font-medium mb-2">Exchange Rate (USD to KHR)*</label>
                            <div class="relative flex items-center">
                            
                                <!-- Input field with proper padding to accommodate prefix and suffix -->
                                <input class="border w-full p-4 pl-16 pr-10 leading-tight bg-slate-100 rounded"
                                       id="exchange_rate_usd_to_khr"
                                       name="exchange_rate_usd_to_khr"
                                       type="text"
                                       placeholder="Enter exchange rate"
                                       value="{{ settings.exchange_rate_usd_to_khr }}"
                                       required />
                                <!-- Suffix container with fixed position -->
                                <div class="absolute inset-y-0 right-0 flex items-center pr-4 pointer-events-none">
                                    <span class="text-gray-500">៛</span>
                                </div>
                            </div>
                            <p class="text-sm text-gray-500 mt-2">The exchange rate for converting between USD and KHR (e.g., 4000 means 1 USD = 4,000 KHR)</p>
                        </div>

                        <!-- Info Box -->
                        <div class="mt-3 p-3 bg-white rounded border border-blue-100">
                            <p class="text-sm text-gray-700"><i class="fas fa-info-circle text-blue-600 mr-1"></i> This exchange rate will be used throughout the system for currency conversions. Make sure to update it regularly to reflect the current market rate.</p>
                        </div>
                    </div>

                    <div class="flex items-center justify-between">
                        <div>
                            <a href="{% url 'settings:dashboard' %}" class="bg-gray-300 hover:bg-gray-400 text-gray-800 font-bold py-3 px-6 rounded mr-2">Cancel</a>
                            <button type="submit" name="reset_to_default" class="bg-yellow-500 hover:bg-yellow-600 text-white font-bold py-3 px-6 rounded"><i class="fas fa-undo mr-2"></i> Reset to Default</button>
                        </div>
                        <button class="bg-blue-900 hover:bg-blue-800 text-white font-bold py-3 px-6 rounded"
                                type="submit"><i class="fas fa-save mr-2"></i> Save Changes</button>
                    </div>
                </form>
            </div>

            <!-- Last Updated Info -->
            <div class="bg-gray-100 p-4 border-t">
                <p class="text-sm text-gray-600">
                    <i class="fas fa-clock mr-1"></i> Last updated: {{ settings.last_updated|date:"F j, Y H:i" }}
                </p>
            </div>
        </div>
    </div>
</div>

<script src="{% static 'js/currency-formatter.js' %}"></script>
<script>
    document.addEventListener('DOMContentLoaded', function() {
        // Set up formatted input for exchange rate
        const exchangeRateInput = document.getElementById('exchange_rate_usd_to_khr');

        // Format the input value with thousand separators
        function formatExchangeRate() {
            let value = exchangeRateInput.value.replace(/,/g, '');
            if (value) {
                exchangeRateInput.value = formatNumber(value);
            }
        }

        // Format on page load
        formatExchangeRate();

        // Format as user types
        exchangeRateInput.addEventListener('input', function() {
            // Store cursor position
            const start = this.selectionStart;
            const end = this.selectionEnd;
            const valueLength = this.value.length;

            // Format the value
            formatExchangeRate();

            // Adjust cursor position if value length changed
            const newLength = this.value.length;
            const diff = newLength - valueLength;

            // Set cursor position
            this.setSelectionRange(start + diff, end + diff);
        });

        // Remove formatting before form submission
        exchangeRateInput.closest('form').addEventListener('submit', function() {
            exchangeRateInput.value = exchangeRateInput.value.replace(/,/g, '');
        });
    });
</script>
{% endblock %}
