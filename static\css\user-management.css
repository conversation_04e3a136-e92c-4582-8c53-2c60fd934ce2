/* User Management Styles */

/* But<PERSON> styles */
.btn-primary {
  @apply bg-blue-700 hover:bg-blue-800 text-white font-medium py-2 px-4 rounded flex items-center justify-center transition-colors;
}

.btn-secondary {
  @apply bg-gray-600 hover:bg-gray-700 text-white font-medium py-2 px-4 rounded flex items-center justify-center transition-colors;
}

.btn-success {
  @apply bg-green-600 hover:bg-green-700 text-white font-medium py-2 px-4 rounded flex items-center justify-center transition-colors;
}

.btn-warning {
  @apply bg-yellow-600 hover:bg-yellow-700 text-white font-medium py-2 px-4 rounded flex items-center justify-center transition-colors;
}

.btn-danger {
  @apply bg-red-600 hover:bg-red-700 text-white font-medium py-2 px-4 rounded flex items-center justify-center transition-colors;
}

/* Tab styling */
.tab-button {
  @apply px-6 py-3 text-sm font-medium border-b-2 border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300 whitespace-nowrap flex items-center;
}

.tab-button.active {
  @apply border-blue-500 text-blue-600;
}

.tab-content {
  @apply hidden;
}

.tab-content.active {
  @apply block;
}

/* Form styling */
.form-group {
  @apply mb-4;
}

.form-label {
  @apply block text-sm font-medium text-gray-700 mb-1;
}

.form-input {
  @apply border border-gray-300 rounded-md w-full p-3 bg-gray-50 focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-all;
}

.form-select {
  @apply border border-gray-300 rounded-md w-full p-3 bg-gray-50 focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-all;
}

.form-textarea {
  @apply border border-gray-300 rounded-md w-full p-3 bg-gray-50 focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-all;
}

/* Card styling */
.card {
  @apply bg-white rounded-lg shadow-md overflow-hidden;
}

.card-header {
  @apply border-b border-gray-200 px-6 py-4;
}

.card-title {
  @apply text-xl font-semibold text-gray-800;
}

.card-subtitle {
  @apply text-gray-600 text-sm mt-1;
}

.card-body {
  @apply p-6;
}

.card-footer {
  @apply border-t border-gray-200 px-6 py-4 bg-gray-50;
}

/* Section styling */
.section-title {
  @apply text-lg font-medium text-gray-700 mb-4 pb-2 border-b border-gray-200;
}

/* Page header */
.page-header {
  @apply mb-6;
}

.page-title {
  @apply text-3xl font-bold text-gray-800;
}

/* Breadcrumbs */
.breadcrumbs {
  @apply flex items-center text-sm mt-2;
}

.breadcrumb-item {
  @apply text-gray-500 hover:text-blue-600 transition-colors;
}

.breadcrumb-separator {
  @apply mx-2 text-gray-400;
}

.breadcrumb-active {
  @apply text-blue-600 font-medium;
}

/* Status badges */
.badge {
  @apply text-xs font-medium px-2.5 py-0.5 rounded;
}

.badge-success {
  @apply bg-green-100 text-green-800;
}

.badge-danger {
  @apply bg-red-100 text-red-800;
}

.badge-warning {
  @apply bg-yellow-100 text-yellow-800;
}

.badge-info {
  @apply bg-blue-100 text-blue-800;
}

.badge-secondary {
  @apply bg-gray-100 text-gray-800;
}

/* Action buttons */
.action-buttons {
  @apply flex justify-center space-x-2;
}

.action-btn {
  @apply hover:text-opacity-80 transition-colors;
}

.action-btn-edit {
  @apply text-blue-600 hover:text-blue-800;
}

.action-btn-delete {
  @apply text-red-600 hover:text-red-800;
}

.action-btn-activate {
  @apply text-green-600 hover:text-green-800;
}

.action-btn-deactivate {
  @apply text-yellow-600 hover:text-yellow-800;
}

/* Search and filter section */
.filter-section {
  @apply flex flex-wrap gap-4 mb-4;
}

.search-box {
  @apply flex-grow max-w-md;
}

.search-input {
  @apply bg-white border border-gray-300 text-gray-900 text-sm rounded-lg focus:ring-blue-500 focus:border-blue-500 block w-full pl-10 p-2.5;
}

.filter-dropdown {
  @apply w-48;
}

.filter-select {
  @apply bg-white border border-gray-300 text-gray-900 text-sm rounded-lg focus:ring-blue-500 focus:border-blue-500 block w-full p-2.5;
}

/* View toggle */
.view-toggle {
  @apply inline-flex rounded-md shadow-sm;
}

.view-toggle-btn {
  @apply px-4 py-2 text-sm font-medium border;
}

.view-toggle-btn-active {
  @apply text-white bg-blue-900 border-blue-900;
}

.view-toggle-btn-inactive {
  @apply text-blue-900 bg-white border-blue-900;
}
