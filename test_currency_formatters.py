"""
Simple test script for currency formatters
"""

import sys
import os

# Add the project directory to the Python path
sys.path.insert(0, os.path.abspath('.'))

# Set up Django environment
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'core.settings')
import django
django.setup()

# Import the currency formatters
from core.templatetags.currency_formatters import format_khr, format_usd, format_number

def test_format_khr():
    """Test KHR formatting"""
    print("\nTesting format_khr:")
    test_cases = [
        (1000, "1,000៛"),
        (10000, "10,000៛"),
        (1000000, "1,000,000៛"),
        (1234567, "1,234,567៛"),
        (1000.50, "1,000៛"),
        (1000.99, "1,000៛"),
        ("1000", "1,000៛"),
        ("1,000", "1,000៛"),
        (None, "0៛"),
        ("invalid", "0៛"),
    ]

    for value, expected in test_cases:
        result = format_khr(value)
        print(f"format_khr({value}) = {result} {'✓' if result == expected else '✗'}")
        assert result == expected, f"Expected {expected}, got {result}"

def test_format_usd():
    """Test USD formatting"""
    print("\nTesting format_usd:")
    test_cases = [
        (1000, "$1,000.00"),
        (10000, "$10,000.00"),
        (1000000, "$1,000,000.00"),
        (1000.50, "$1,000.50"),
        (1000.99, "$1,000.99"),
        (1000.999, "$1,001.00"),
        ("1000", "$1,000.00"),
        ("1,000", "$1,000.00"),
        ("1000.50", "$1,000.50"),
        (None, "$0.00"),
        ("invalid", "$0.00"),
    ]

    for value, expected in test_cases:
        result = format_usd(value)
        print(f"format_usd({value}) = {result} {'✓' if result == expected else '✗'}")
        assert result == expected, f"Expected {expected}, got {result}"

def test_format_number():
    """Test number formatting"""
    print("\nTesting format_number:")
    test_cases = [
        (1000, "1,000"),
        (10000, "10,000"),
        (1000000, "1,000,000"),
        (1000.00, "1,000"),
        (1000.50, "1,000"),
        ("1000", "1,000"),
        ("1,000", "1,000"),
        (None, "0"),
        ("invalid", "0"),
    ]

    for value, expected in test_cases:
        result = format_number(value)
        print(f"format_number({value}) = {result} {'✓' if result == expected else '✗'}")
        assert result == expected, f"Expected {expected}, got {result}"

    # Test with decimal places
    print("\nTesting format_number with decimal places:")
    decimal_test_cases = [
        ((1000, 2), "1,000.00"),
        ((1000.50, 2), "1,000.50"),
        ((1000.99, 2), "1,000.99"),
        ((1000.999, 2), "1,001.00"),
    ]

    for (value, decimals), expected in decimal_test_cases:
        result = format_number(value, decimals)
        print(f"format_number({value}, {decimals}) = {result} {'✓' if result == expected else '✗'}")
        assert result == expected, f"Expected {expected}, got {result}"

if __name__ == "__main__":
    print("Running currency formatter tests...")
    try:
        test_format_khr()
        test_format_usd()
        test_format_number()
        print("\nAll tests passed! ✓")
    except AssertionError as e:
        print(f"\nTest failed: {e}")
