{% extends "../base.html" %}
{% load permission_tags %}
{% load custom_filters %}
{% load currency_filters %}



{% block body %}
<div class="conponentSection p-5 bg-gray-200">
    <div class="componentWrapper">
        <!-- Header with Add Button -->
        <div class="flex justify-between items-center mb-4">
            <h3 class="text-2xl font-bold">Product Management</h3>
            {% has_permission request.user 'product' 'edit' as can_edit_products %}
            {% if can_edit_products %}
            <a href="{% url 'product:create_product' %}" class="bg-blue-900 text-white font-bold py-2 px-4 rounded">
                <i class="fas fa-plus mr-2"></i>Add New Product
            </a>
            {% endif %}
        </div>



        <!-- Advanced Search and Filter Section -->
        <div class="bg-white p-4 rounded shadow-md mb-4">
            <!-- Search Form -->
            <form method="get" action="{% url 'product:index' %}" id="product-filter-form">
                <!-- Live Search Bar -->
                <div class="mb-4">
                    <div class="relative">
                        <div class="absolute inset-y-0 left-0 flex items-center pl-3 pointer-events-none">
                            <i class="fas fa-search text-gray-400"></i>
                        </div>
                        <input type="text" id="product-search" name="search" class="bg-gray-50 border border-gray-300 text-gray-900 text-sm rounded-lg focus:ring-blue-500 focus:border-blue-500 block w-full pl-10 p-2.5" placeholder="Search by name, SKU, category..." value="{{ search_query|default:'' }}">
                    </div>
                </div>

                <!-- Filter and Sort Options -->
                <div class="grid grid-cols-1 md:grid-cols-3 gap-4">
                    <!-- Category Filter -->
                    <div>
                        <label for="category-filter" class="block mb-2 text-sm font-medium text-gray-900">Category</label>
                        <select id="category-filter" name="category" class="bg-gray-50 border border-gray-300 text-gray-900 text-sm rounded-lg focus:ring-blue-500 focus:border-blue-500 block w-full p-2.5">
                            <option value="">All Categories</option>
                            {% for category in categories %}
                            <option value="{{ category.id }}" {% if selected_category == category.id|stringformat:"s" %}selected{% endif %}>{{ category.name }}</option>
                            {% endfor %}
                        </select>
                    </div>

                    <!-- Stock Status Filter -->
                    <div>
                        <label for="stock-filter" class="block mb-2 text-sm font-medium text-gray-900">Stock Status</label>
                        <select id="stock-filter" name="stock_status" class="bg-gray-50 border border-gray-300 text-gray-900 text-sm rounded-lg focus:ring-blue-500 focus:border-blue-500 block w-full p-2.5">
                            <option value="">All Stock Status</option>
                            <option value="in-stock" {% if selected_stock_status == 'in-stock' %}selected{% endif %}>In Stock</option>
                            <option value="out-of-stock" {% if selected_stock_status == 'out-of-stock' %}selected{% endif %}>Out of Stock</option>
                        </select>
                    </div>

                    <!-- Sort Options -->
                    <div>
                        <label for="sort-options" class="block mb-2 text-sm font-medium text-gray-900">Sort By</label>
                        <select id="sort-options" name="sort" class="bg-gray-50 border border-gray-300 text-gray-900 text-sm rounded-lg focus:ring-blue-500 focus:border-blue-500 block w-full p-2.5">
                            <option value="name-asc" {% if selected_sort == 'name-asc' %}selected{% endif %}>Name (A-Z)</option>
                            <option value="name-desc" {% if selected_sort == 'name-desc' %}selected{% endif %}>Name (Z-A)</option>
                            <option value="price-asc" {% if selected_sort == 'price-asc' %}selected{% endif %}>Price (Low to High)</option>
                            <option value="price-desc" {% if selected_sort == 'price-desc' %}selected{% endif %}>Price (High to Low)</option>
                            <option value="newest" {% if selected_sort == 'newest' or not selected_sort %}selected{% endif %}>Most Recently Added</option>
                        </select>
                    </div>
                </div>

                <!-- Advanced Filters (collapsible) -->
                <div class="mt-4">
                    <button type="button" id="advanced-filters-toggle" class="text-blue-900 hover:underline flex items-center"><i class="fas fa-caret-right mr-1 transform transition-transform" id="advanced-filters-icon"></i> Advanced Filters</button>
                    <div id="advanced-filters" class="hidden mt-4">
                        <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                            <!-- Price Range Filter -->
                            <div>
                                <label class="block mb-2 text-sm font-medium text-gray-900">Price Range (៛)</label>
                                <div class="flex items-center space-x-2">
                                    <input type="number" name="min_price" placeholder="Min" class="bg-gray-50 border border-gray-300 text-gray-900 text-sm rounded-lg focus:ring-blue-500 focus:border-blue-500 w-full p-2.5" value="{{ min_price|default:'' }}">
                                    <span>to</span>
                                    <input type="number" name="max_price" placeholder="Max" class="bg-gray-50 border border-gray-300 text-gray-900 text-sm rounded-lg focus:ring-blue-500 focus:border-blue-500 w-full p-2.5" value="{{ max_price|default:'' }}">
                                </div>
                            </div>

                            <!-- Date Added Filter -->
                            <div>
                                <label for="date-added" class="block mb-2 text-sm font-medium text-gray-900">Date Added</label>
                                <select id="date-added" name="date_added" class="bg-gray-50 border border-gray-300 text-gray-900 text-sm rounded-lg focus:ring-blue-500 focus:border-blue-500 block w-full p-2.5">
                                    <option value="">All Time</option>
                                    <option value="today" {% if date_added == 'today' %}selected{% endif %}>Today</option>
                                    <option value="this_week" {% if date_added == 'this_week' %}selected{% endif %}>This Week</option>
                                    <option value="this_month" {% if date_added == 'this_month' %}selected{% endif %}>This Month</option>
                                    <option value="this_year" {% if date_added == 'this_year' %}selected{% endif %}>This Year</option>
                                </select>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Filter Controls and Data Management -->
                <div class="mt-4 flex flex-col md:flex-row md:items-center md:justify-between gap-4">
                    <!-- Filter Buttons -->
                    <div class="flex justify-start">
                        <a href="{% url 'product:index' %}" class="bg-gray-500 text-white px-4 py-2 rounded mr-2">
                            <i class="fas fa-undo mr-1"></i> Reset Filters
                        </a>
                        <button type="submit" class="bg-blue-900 text-white px-4 py-2 rounded">
                            <i class="fas fa-filter mr-1"></i> Apply Filters
                        </button>
                    </div>

                    <!-- Import/Export Controls -->
                    <div class="flex flex-wrap gap-2">
                        <!-- Export Button -->
                        <a href="{% url 'product:export_csv' %}{% if request.GET %}?{{ request.GET.urlencode }}{% endif %}" class="bg-green-600 text-white px-4 py-2 rounded">
                            <i class="fas fa-file-export mr-1"></i> Export to CSV
                        </a>

                        <!-- Import Button (opens modal) - Only show if user has edit permission -->
                        {% has_permission request.user 'product' 'edit' as can_edit_products %}
                        {% if can_edit_products %}
                        <button id="import-btn" class="bg-blue-600 text-white px-4 py-2 rounded">
                            <i class="fas fa-file-import mr-1"></i> Import from CSV
                        </button>
                        {% endif %}
                    </div>
                </div>
            </form>
        </div>

        <!-- Import Modal -->
        <div id="import-modal" class="fixed inset-0 bg-gray-600 bg-opacity-50 flex items-center justify-center z-50 hidden">
            <div class="bg-white p-6 rounded-lg shadow-lg w-full max-w-md">
                <div class="flex justify-between items-center mb-4">
                    <h3 class="text-xl font-semibold">Import Products</h3>
                    <button id="close-modal" class="text-gray-500 hover:text-gray-700"><i class="fas fa-times"></i></button>
                </div>
                <form action="{% url 'product:import_csv' %}" method="post" enctype="multipart/form-data">
                    {% csrf_token %}
                    <div class="mb-4">
                        <label for="csv-file" class="block text-sm font-medium text-gray-700 mb-2">Select CSV File</label>
                        <input type="file" id="csv-file" name="csv_file" accept=".csv" class="block w-full text-sm text-gray-900 border border-gray-300 rounded-lg cursor-pointer bg-gray-50 focus:outline-none" required>
                    </div>
                    <div class="mb-4 text-sm text-gray-600">
                        <p class="mb-2">CSV should have the following columns:</p>
                        <p>SKU, Name, Category, Description, Cost Price, Retail Price, Quantity, Box Quantity, Box Cost, Active</p>
                    </div>
                    <div class="flex justify-end">
                        <button type="button" id="cancel-import" class="bg-gray-300 text-gray-800 px-4 py-2 rounded mr-2">Cancel</button>
                        <button type="submit" class="bg-blue-600 text-white px-4 py-2 rounded">Import</button>
                    </div>
                </form>
            </div>
        </div>

        <!-- View Toggle and Bulk Actions -->
        <div class="flex justify-between items-center mb-4">
            <div class="flex items-center space-x-2">
                <button id="view-table" class="bg-blue-900 text-white px-3 py-1 rounded text-sm"><i class="fas fa-list mr-1"></i> Table View</button>
                <button id="view-grid" class="bg-gray-200 text-gray-800 px-3 py-1 rounded text-sm"><i class="fas fa-th-large mr-1"></i> Grid View</button>
            </div>
            {% has_permission request.user 'product' 'edit' as can_edit_products %}
            {% has_permission request.user 'product' 'full' as can_delete_products %}
            {% if can_edit_products %}
            <div class="flex items-center space-x-2">
                <select id="bulk-action" class="bg-gray-50 border border-gray-300 text-gray-900 text-sm rounded-lg focus:ring-blue-500 focus:border-blue-500 p-2">
                    <option value="">Bulk Actions</option>
                    {% if can_delete_products %}
                    <option value="delete">Delete Selected</option>
                    {% endif %}
                    <option value="active">Set Active</option>
                    <option value="inactive">Set Inactive</option>
                    <option value="reactivate_stocked">Reactivate Products with Stock</option>
                    <option value="export">Export Selected</option>
                </select>
                <button id="apply-bulk-action" class="bg-blue-900 text-white px-3 py-1 rounded text-sm">Apply</button>
            </div>
            {% endif %}
        </div>

        <!-- Products List (Table View) -->
        <div id="table-view" class="bg-white p-4 rounded shadow-md">
            <h3 class="text-2xl font-bold mb-4">Products</h3>
            <div class="relative overflow-x-auto">
                <table class="w-full text-sm text-left">
                    <thead class="text-sm uppercase bg-blue-900 text-gray-50"><tr>
                            <th scope="col" class="px-6 py-3">
                                <input type="checkbox" id="select-all" class="w-4 h-4 text-blue-600 bg-gray-100 border-gray-300 rounded focus:ring-blue-500"></th>
                            <th scope="col" class="px-6 py-3">Image</th>
                            <th scope="col" class="px-6 py-3">Name</th>
                            <th scope="col" class="px-6 py-3">SKU</th>
                            <th scope="col" class="px-6 py-3">Category</th>
                            <th scope="col" class="px-6 py-3">Quantity<br/>(Boxes & Units)</th>
                            <th scope="col" class="px-6 py-3">Retail Price</th>
                            <th scope="col" class="px-6 py-3">Stock Status</th>
                            <th scope="col" class="px-6 py-3">Active</th>
                            <th scope="col" class="px-6 py-3">Actions</th>
                        </tr>
                    </thead>
                    <tbody id="product-table-body">
                        {% for product in products %}
                        <tr class="bg-white border" data-product-id="{{ product.id }}" data-product-name="{{ product.name }}" data-product-sku="{{ product.sku }}" data-product-category="{{ product.category.name|default:'' }}" data-product-price="{{ product.retail_price }}">
                            <td class="px-6 py-4">
                                <input type="checkbox" class="product-select w-4 h-4 text-blue-600 bg-gray-100 border-gray-300 rounded focus:ring-blue-500" data-product-id="{{ product.id }}">
                            </td>
                            <td class="px-6 py-4">
                                {% if product.image %}
                                <img src="{{ product.image.url }}" alt="{{ product.name }}" class="w-12 h-12 object-cover" />
                                {% else %}
                                <div class="w-12 h-12 bg-gray-200 flex items-center justify-center">
                                    <span class="text-gray-500">No image</span>
                                </div>
                                {% endif %}
                            </td>
                            <td class="px-6 py-4">{{ product.name }}</td>
                            <td class="px-6 py-4">{{ product.sku }}</td>
                            <td class="px-6 py-4">{{ product.category.name|default:"--" }}</td>
                            <td class="px-6 py-4">
                                <div class="grid grid-cols-1 gap-1">
                                    {% if product.box_quantity > 1 %}
                                        <div class="font-medium">Boxes: {{ product.boxes_count }}</div>
                                        <div>Units: {{ product.units_count }}</div>
                                        <div class="text-xs text-gray-500 mt-1">Total: {{ product.quantity }} units</div>
                                        <div class="text-xs text-gray-400">({{ product.box_quantity }} units per box)</div>
                                    {% else %}
                                        <div>Units: {{ product.quantity }}</div>
                                    {% endif %}
                                </div>
                            </td>
                            <td class="px-6 py-4">{{ product.retail_price|format_khr }}</td>
                            <td class="px-6 py-4">
                                {% if product.quantity <= 0 %}
                                <span class="bg-red-100 text-red-800 text-xs font-medium px-2.5 py-0.5 rounded">Out of Stock</span>
                                {% else %}
                                <span class="bg-green-100 text-green-800 text-xs font-medium px-2.5 py-0.5 rounded">In Stock</span>
                                {% endif %}
                            </td>
                            <td class="px-6 py-4">
                                {% if product.is_active %}
                                <span class="bg-green-100 text-green-800 text-xs font-medium px-2.5 py-0.5 rounded">Active</span>
                                {% else %}
                                <span class="bg-gray-100 text-gray-800 text-xs font-medium px-2.5 py-0.5 rounded">Inactive</span>
                                {% if product.quantity <= 0 %}
                                <div class="text-xs text-red-600 mt-1">Auto-deactivated (Out of Stock)</div>
                                {% endif %}
                                {% endif %}
                            </td>
                            <td class="px-6 py-4">
                                {% has_permission request.user 'product' 'edit' as can_edit_products %}
                                {% if can_edit_products %}
                                <a href="{% url 'product:edit_product' product.id %}" class="text-blue-600 hover:underline mr-2">
                                    <i class="fas fa-edit"></i> Edit
                                </a>
                                {% endif %}

                                {% has_permission request.user 'product' 'full' as can_delete_products %}
                                {% if can_delete_products %}
                                <a href="{% url 'product:delete_product' product.id %}" class="text-red-600 hover:underline" onclick="return confirm('Are you sure you want to delete this product?')">
                                    <i class="fas fa-trash"></i> Delete
                                </a>
                                {% endif %}
                            </td>
                        </tr>
                        {% empty %}
                        <tr class="bg-white border">
                            <td colspan="10" class="px-6 py-4 text-center">No products found.</td>
                        </tr>
                        {% endfor %}
                    </tbody>
                </table>
            </div>
        </div>

        <!-- Products Grid (Grid View) -->
        <div id="grid-view" class="bg-white p-4 rounded shadow-md hidden">
            <h3 class="text-2xl font-bold mb-4">Products</h3>
            <div class="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-4" id="product-grid">
                {% for product in products %}
                <div class="product-card border rounded-lg overflow-hidden shadow-md flex flex-col" data-product-id="{{ product.id }}" data-product-name="{{ product.name }}" data-product-sku="{{ product.sku }}" data-product-category="{{ product.category.name|default:'' }}" data-product-price="{{ product.retail_price }}">
                    <div class="relative">
                        <div class="absolute top-0 left-0 m-2">
                            <input type="checkbox" class="product-select w-4 h-4 text-blue-600 bg-gray-100 border-gray-300 rounded focus:ring-blue-500" data-product-id="{{ product.id }}">
                        </div>
                        {% if product.image %}
                        <img src="{{ product.image.url }}" alt="{{ product.name }}" class="w-full h-48 object-cover" />
                        {% else %}
                        <div class="w-full h-48 bg-gray-200 flex items-center justify-center">
                            <span class="text-gray-500">No image</span>
                        </div>
                        {% endif %}
                        <div class="absolute top-0 right-0 m-2 flex flex-col gap-1">
                            {% if product.quantity <= 0 %}
                            <span class="bg-red-100 text-red-800 text-xs font-medium px-2.5 py-0.5 rounded">Out of Stock</span>
                            {% else %}
                            <span class="bg-green-100 text-green-800 text-xs font-medium px-2.5 py-0.5 rounded">In Stock</span>
                            {% endif %}

                            {% if product.is_active %}
                            <span class="bg-green-100 text-green-800 text-xs font-medium px-2.5 py-0.5 rounded">Active</span>
                            {% else %}
                            <span class="bg-gray-100 text-gray-800 text-xs font-medium px-2.5 py-0.5 rounded">Inactive</span>
                            {% if product.quantity <= 0 %}
                            <span class="block text-xs text-red-600 mt-1">Auto-deactivated</span>
                            {% endif %}
                            {% endif %}
                        </div>
                    </div>
                    <div class="p-4 flex-grow">
                        <h5 class="text-lg font-semibold mb-1">{{ product.name }}</h5>
                        <p class="text-sm text-gray-600 mb-2">SKU: {{ product.sku }}</p>
                        <p class="text-sm text-gray-600 mb-2">Category: {{ product.category.name|default:"--" }}</p>
                        <div class="mb-2">
                            {% if product.box_quantity > 1 %}
                                <div class="text-sm">Boxes: {{ product.boxes_count }}</div>
                                <div class="text-sm">Units: {{ product.units_count }}</div>
                                <div class="text-xs text-gray-500">Total: {{ product.quantity }} units</div>
                                <div class="text-xs text-gray-400">({{ product.box_quantity }} units per box)</div>
                            {% else %}
                                <div class="text-sm">Units: {{ product.quantity }}</div>
                            {% endif %}
                        </div>
                        <p class="text-lg font-bold text-blue-900">{{ product.retail_price|format_khr }}</p>
                    </div>
                    <div class="p-4 bg-gray-50 border-t">
                        <div class="flex justify-between">
                            {% has_permission request.user 'product' 'edit' as can_edit_products %}
                            {% if can_edit_products %}
                            <a href="{% url 'product:edit_product' product.id %}" class="text-blue-600 hover:underline">
                                <i class="fas fa-edit"></i> Edit
                            </a>
                            {% endif %}

                            {% has_permission request.user 'product' 'full' as can_delete_products %}
                            {% if can_delete_products %}
                            <a href="{% url 'product:delete_product' product.id %}" class="text-red-600 hover:underline" onclick="return confirm('Are you sure you want to delete this product?')">
                                <i class="fas fa-trash"></i> Delete
                            </a>
                            {% endif %}
                        </div>
                    </div>
                </div>
                {% empty %}
                <div class="col-span-full text-center py-8 text-gray-500">
                    No products found.
                </div>
                {% endfor %}
            </div>
        </div>

        <!-- Pagination -->
        <div class="flex flex-col md:flex-row md:items-center md:justify-between mt-4 bg-white p-4 rounded shadow-md">
            <div class="flex items-center mb-4 md:mb-0">
                <span class="text-sm text-gray-700">
                    Showing <span class="font-medium">{{ products.start_index }}</span> to <span class="font-medium">{{ products.end_index }}</span> of <span class="font-medium">{{ products.paginator.count }}</span> entries
                </span>
                <div class="ml-4">
                    <label for="items-per-page" class="text-sm text-gray-700 mr-2">Items per page:</label>
                    <select id="items-per-page" name="items_per_page" class="bg-gray-50 border border-gray-300 text-gray-900 text-sm rounded-lg p-1" form="product-filter-form">
                        <option value="10" {% if items_per_page == 10 %}selected{% endif %}>10</option>
                        <option value="25" {% if items_per_page == 25 %}selected{% endif %}>25</option>
                        <option value="50" {% if items_per_page == 50 %}selected{% endif %}>50</option>
                        <option value="100" {% if items_per_page == 100 %}selected{% endif %}>100</option>
                    </select>
                </div>
            </div>
            <div class="flex items-center space-x-2">
                <nav class="isolate inline-flex -space-x-px rounded-md shadow-sm" aria-label="Pagination">
                    <!-- First Page -->
                    {% if products.has_previous %}
                    <a href="?{% if request.GET %}{% for key, value in request.GET.items %}{% if key != 'page' %}{{ key }}={{ value }}&{% endif %}{% endfor %}{% endif %}page=1" class="relative inline-flex items-center rounded-l-md px-2 py-2 text-gray-600 ring-1 ring-inset ring-gray-300 hover:bg-gray-50 focus:z-20 focus:outline-offset-0">
                        <span class="sr-only">First</span>
                        <i class="fas fa-angle-double-left h-5 w-5"></i>
                    </a>
                    {% else %}
                    <span class="relative inline-flex items-center rounded-l-md px-2 py-2 text-gray-300 ring-1 ring-inset ring-gray-300 cursor-not-allowed"><span class="sr-only">First</span>
                        <i class="fas fa-angle-double-left h-5 w-5"></i>
                    </span>
                    {% endif %}

                    <!-- Previous Page -->
                    {% if products.has_previous %}
                    <a href="?{% if request.GET %}{% for key, value in request.GET.items %}{% if key != 'page' %}{{ key }}={{ value }}&{% endif %}{% endfor %}{% endif %}page={{ products.previous_page_number }}" class="relative inline-flex items-center px-2 py-2 text-gray-600 ring-1 ring-inset ring-gray-300 hover:bg-gray-50 focus:z-20 focus:outline-offset-0">
                        <span class="sr-only">Previous</span>
                        <i class="fas fa-chevron-left h-5 w-5"></i>
                    </a>
                    {% else %}
                    <span class="relative inline-flex items-center px-2 py-2 text-gray-300 ring-1 ring-inset ring-gray-300 cursor-not-allowed"><span class="sr-only">Previous</span>
                        <i class="fas fa-chevron-left h-5 w-5"></i>
                    </span>
                    {% endif %}

                    {% for i in products.paginator.page_range %}
                        {% if products.number == i %}
                            <span aria-current="page" class="relative z-10 inline-flex items-center bg-blue-900 px-4 py-2 text-sm font-semibold text-white focus:z-20 focus-visible:outline focus-visible:outline-2 focus-visible:outline-offset-2 focus-visible:outline-blue-900">{{ i }}</span>
                        {% elif i > products.number|add:"-3" and i < products.number|add:"3" %}
                            <a href="?{% if request.GET %}{% for key, value in request.GET.items %}{% if key != 'page' %}{{ key }}={{ value }}&{% endif %}{% endfor %}{% endif %}page={{ i }}" class="relative inline-flex items-center px-4 py-2 text-sm font-semibold text-gray-900 ring-1 ring-inset ring-gray-300 hover:bg-gray-50 focus:z-20 focus:outline-offset-0">{{ i }}</a>
                        {% endif %}
                    {% endfor %}

                    <!-- Next Page -->
                    {% if products.has_next %}
                    <a href="?{% if request.GET %}{% for key, value in request.GET.items %}{% if key != 'page' %}{{ key }}={{ value }}&{% endif %}{% endfor %}{% endif %}page={{ products.next_page_number }}" class="relative inline-flex items-center px-2 py-2 text-gray-600 ring-1 ring-inset ring-gray-300 hover:bg-gray-50 focus:z-20 focus:outline-offset-0">
                        <span class="sr-only">Next</span>
                        <i class="fas fa-chevron-right h-5 w-5"></i>
                    </a>
                    {% else %}
                    <span class="relative inline-flex items-center px-2 py-2 text-gray-300 ring-1 ring-inset ring-gray-300 cursor-not-allowed"><span class="sr-only">Next</span>
                        <i class="fas fa-chevron-right h-5 w-5"></i>
                    </span>
                    {% endif %}

                    <!-- Last Page -->
                    {% if products.has_next %}
                    <a href="?{% if request.GET %}{% for key, value in request.GET.items %}{% if key != 'page' %}{{ key }}={{ value }}&{% endif %}{% endfor %}{% endif %}page={{ products.paginator.num_pages }}" class="relative inline-flex items-center rounded-r-md px-2 py-2 text-gray-600 ring-1 ring-inset ring-gray-300 hover:bg-gray-50 focus:z-20 focus:outline-offset-0">
                        <span class="sr-only">Last</span>
                        <i class="fas fa-angle-double-right h-5 w-5"></i>
                    </a>
                    {% else %}
                    <span class="relative inline-flex items-center rounded-r-md px-2 py-2 text-gray-300 ring-1 ring-inset ring-gray-300 cursor-not-allowed"><span class="sr-only">Last</span>
                        <i class="fas fa-angle-double-right h-5 w-5"></i>
                    </span>
                    {% endif %}
                </nav>

                <!-- Jump to Page -->
                <div class="flex items-center ml-4">
                    <span class="text-sm text-gray-700 mr-2">Go to page:</span>
                    <input type="number" id="jump-to-page" min="1" max="{{ products.paginator.num_pages }}" value="{{ products.number }}" class="w-16 bg-gray-50 border border-gray-300 text-gray-900 text-sm rounded-lg p-1">
                    <button id="jump-page-btn" class="ml-2 bg-blue-900 text-white px-2 py-1 rounded text-sm">Go</button>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block js %}
<script>
    document.addEventListener('DOMContentLoaded', function() {
        // View Toggle Functionality
        const tableViewBtn = document.getElementById('view-table');
        const gridViewBtn = document.getElementById('view-grid');
        const tableView = document.getElementById('table-view');
        const gridView = document.getElementById('grid-view');

        tableViewBtn.addEventListener('click', function() {
            tableView.classList.remove('hidden');
            gridView.classList.add('hidden');
            tableViewBtn.classList.remove('bg-gray-200', 'text-gray-800');
            tableViewBtn.classList.add('bg-blue-900', 'text-white');
            gridViewBtn.classList.remove('bg-blue-900', 'text-white');
            gridViewBtn.classList.add('bg-gray-200', 'text-gray-800');

            // Store view preference in localStorage
            localStorage.setItem('productViewPreference', 'table');
        });

        gridViewBtn.addEventListener('click', function() {
            gridView.classList.remove('hidden');
            tableView.classList.add('hidden');
            gridViewBtn.classList.remove('bg-gray-200', 'text-gray-800');
            gridViewBtn.classList.add('bg-blue-900', 'text-white');
            tableViewBtn.classList.remove('bg-blue-900', 'text-white');
            tableViewBtn.classList.add('bg-gray-200', 'text-gray-800');

            // Store view preference in localStorage
            localStorage.setItem('productViewPreference', 'grid');
        });

        // Load view preference from localStorage
        const viewPreference = localStorage.getItem('productViewPreference');
        if (viewPreference === 'grid') {
            gridViewBtn.click();
        }

        // Select All Checkbox Functionality
        const selectAllCheckbox = document.getElementById('select-all');
        const productCheckboxes = document.querySelectorAll('.product-select');

        selectAllCheckbox.addEventListener('change', function() {
            productCheckboxes.forEach(checkbox => {
                checkbox.checked = selectAllCheckbox.checked;
            });
        });

        // Client-side search for quick filtering without page reload
        const searchInput = document.getElementById('product-search');
        const tableRows = document.querySelectorAll('#product-table-body tr');
        const gridCards = document.querySelectorAll('.product-card');

        // Form elements
        const filterForm = document.getElementById('product-filter-form');
        const categoryFilter = document.getElementById('category-filter');
        const stockFilter = document.getElementById('stock-filter');
        const sortOptions = document.getElementById('sort-options');
        const itemsPerPage = document.getElementById('items-per-page');

        // Live search functionality (client-side only)
        let searchTimeout;
        searchInput.addEventListener('input', function() {
            clearTimeout(searchTimeout);

            // Add a small delay to avoid searching on every keystroke
            searchTimeout = setTimeout(function() {
                const searchTerm = searchInput.value.toLowerCase();

                if (searchTerm.length >= 2) {
                    // Filter table rows
                    tableRows.forEach(row => {
                        const name = row.getAttribute('data-product-name').toLowerCase();
                        const sku = row.getAttribute('data-product-sku').toLowerCase();
                        const category = row.getAttribute('data-product-category').toLowerCase();

                        if (name.includes(searchTerm) || sku.includes(searchTerm) || category.includes(searchTerm)) {
                            row.classList.remove('hidden');
                        } else {
                            row.classList.add('hidden');
                        }
                    });

                    // Filter grid cards
                    gridCards.forEach(card => {
                        const name = card.getAttribute('data-product-name').toLowerCase();
                        const sku = card.getAttribute('data-product-sku').toLowerCase();
                        const category = card.getAttribute('data-product-category').toLowerCase();

                        if (name.includes(searchTerm) || sku.includes(searchTerm) || category.includes(searchTerm)) {
                            card.classList.remove('hidden');
                        } else {
                            card.classList.add('hidden');
                        }
                    });
                } else if (searchTerm.length === 0) {
                    // Show all products if search is cleared
                    tableRows.forEach(row => row.classList.remove('hidden'));
                    gridCards.forEach(card => card.classList.remove('hidden'));
                }
            }, 300);
        });

        // Advanced Filters Toggle
        const advancedFiltersToggle = document.getElementById('advanced-filters-toggle');
        const advancedFilters = document.getElementById('advanced-filters');
        const advancedFiltersIcon = document.getElementById('advanced-filters-icon');

        advancedFiltersToggle.addEventListener('click', function() {
            advancedFilters.classList.toggle('hidden');
            advancedFiltersIcon.classList.toggle('rotate-90');
        });

        // Auto-submit form when filters change (for server-side filtering)
        categoryFilter.addEventListener('change', function() {
            filterForm.submit();
        });

        stockFilter.addEventListener('change', function() {
            filterForm.submit();
        });

        sortOptions.addEventListener('change', function() {
            filterForm.submit();
        });

        itemsPerPage.addEventListener('change', function() {
            filterForm.submit();
        });

        // Import/Export Functionality
        const importBtn = document.getElementById('import-btn');
        const importModal = document.getElementById('import-modal');
        const closeModal = document.getElementById('close-modal');
        const cancelImport = document.getElementById('cancel-import');

        importBtn.addEventListener('click', function(e) {
            e.preventDefault();
            importModal.classList.remove('hidden');
        });

        closeModal.addEventListener('click', function() {
            importModal.classList.add('hidden');
        });

        cancelImport.addEventListener('click', function() {
            importModal.classList.add('hidden');
        });

        // Close modal when clicking outside
        importModal.addEventListener('click', function(e) {
            if (e.target === importModal) {
                importModal.classList.add('hidden');
            }
        });

        // Bulk Actions Functionality
        const bulkActionSelect = document.getElementById('bulk-action');
        const applyBulkActionBtn = document.getElementById('apply-bulk-action');

        applyBulkActionBtn.addEventListener('click', function() {
            const selectedAction = bulkActionSelect.value;

            if (!selectedAction) {
                alert('Please select an action to perform.');
                return;
            }

            // Get all checked checkboxes
            const checkedCheckboxes = document.querySelectorAll('.product-select:checked');

            if (checkedCheckboxes.length === 0) {
                alert('Please select at least one product.');
                return;
            }

            // Get product IDs of selected products
            const selectedProductIds = Array.from(checkedCheckboxes).map(checkbox => checkbox.getAttribute('data-product-id'));

            // Perform action based on selection
            if (selectedAction === 'delete') {
                if (confirm(`Are you sure you want to delete ${selectedProductIds.length} selected products?`)) {
                    // Create a form to submit the bulk action
                    const form = document.createElement('form');
                    form.method = 'POST';
                    form.action = '{% url "product:bulk_action" %}';

                    // Add CSRF token
                    const csrfToken = document.createElement('input');
                    csrfToken.type = 'hidden';
                    csrfToken.name = 'csrfmiddlewaretoken';
                    csrfToken.value = '{{ csrf_token }}';
                    form.appendChild(csrfToken);

                    // Add action
                    const actionInput = document.createElement('input');
                    actionInput.type = 'hidden';
                    actionInput.name = 'action';
                    actionInput.value = selectedAction;
                    form.appendChild(actionInput);

                    // Add product IDs
                    selectedProductIds.forEach(id => {
                        const productIdInput = document.createElement('input');
                        productIdInput.type = 'hidden';
                        productIdInput.name = 'product_ids[]';
                        productIdInput.value = id;
                        form.appendChild(productIdInput);
                    });

                    // Append form to body and submit
                    document.body.appendChild(form);
                    form.submit();
                }
            } else if (selectedAction === 'active' || selectedAction === 'inactive') {
                if (confirm(`Are you sure you want to set ${selectedProductIds.length} selected products to ${selectedAction}?`)) {
                    // Create a form to submit the bulk action
                    const form = document.createElement('form');
                    form.method = 'POST';
                    form.action = '{% url "product:bulk_action" %}';

                    // Add CSRF token
                    const csrfToken = document.createElement('input');
                    csrfToken.type = 'hidden';
                    csrfToken.name = 'csrfmiddlewaretoken';
                    csrfToken.value = '{{ csrf_token }}';
                    form.appendChild(csrfToken);

                    // Add action
                    const actionInput = document.createElement('input');
                    actionInput.type = 'hidden';
                    actionInput.name = 'action';
                    actionInput.value = selectedAction;
                    form.appendChild(actionInput);

                    // Add product IDs
                    selectedProductIds.forEach(id => {
                        const productIdInput = document.createElement('input');
                        productIdInput.type = 'hidden';
                        productIdInput.name = 'product_ids[]';
                        productIdInput.value = id;
                        form.appendChild(productIdInput);
                    });

                    // Append form to body and submit
                    document.body.appendChild(form);
                    form.submit();
                }
            } else if (selectedAction === 'export') {
                // Create a form to submit the bulk action
                const form = document.createElement('form');
                form.method = 'POST';
                form.action = '{% url "product:bulk_action" %}';

                // Add CSRF token
                const csrfToken = document.createElement('input');
                csrfToken.type = 'hidden';
                csrfToken.name = 'csrfmiddlewaretoken';
                csrfToken.value = '{{ csrf_token }}';
                form.appendChild(csrfToken);

                // Add action
                const actionInput = document.createElement('input');
                actionInput.type = 'hidden';
                actionInput.name = 'action';
                actionInput.value = selectedAction;
                form.appendChild(actionInput);

                // Add product IDs
                selectedProductIds.forEach(id => {
                    const productIdInput = document.createElement('input');
                    productIdInput.type = 'hidden';
                    productIdInput.name = 'product_ids[]';
                    productIdInput.value = id;
                    form.appendChild(productIdInput);
                });

                // Append form to body and submit
                document.body.appendChild(form);
                form.submit();
            }
        });

        // Box quantity calculation functionality for product form
        const boxQuantityInput = document.getElementById('box_quantity');
        const boxCostInput = document.getElementById('box_cost');
        const costPriceInput = document.getElementById('cost_price');
        const costPriceInfo = document.getElementById('cost_price_info');

        // Function to calculate cost price from box cost and box quantity
        function calculateCostPrice() {
            const boxQuantity = parseFloat(boxQuantityInput.value) || 1;
            const boxCost = parseFloat(boxCostInput.value) || 0;

            if (boxQuantity > 0 && boxCost > 0) {
                // Calculate cost price per unit
                const costPrice = boxCost / boxQuantity;

                // Update cost price input
                costPriceInput.value = costPrice.toFixed(2);

                // Show info about calculation
                costPriceInfo.textContent = `${boxCost}៛ ÷ ${boxQuantity} = ${Math.round(costPrice)}៛/unit`;
            } else {
                costPriceInfo.textContent = '';
            }
        }

        // Function to calculate box cost from cost price and box quantity
        function calculateBoxCost() {
            const boxQuantity = parseFloat(boxQuantityInput.value) || 1;
            const costPrice = parseFloat(costPriceInput.value) || 0;

            if (boxQuantity > 0 && costPrice > 0) {
                // Calculate box cost
                const boxCost = costPrice * boxQuantity;

                // Update box cost input
                boxCostInput.value = boxCost.toFixed(2);

                // Show info about calculation
                costPriceInfo.textContent = `${costPrice}៛ × ${boxQuantity} = ${Math.round(boxCost)}៛/box`;
            }
        }

        // Add event listeners if we're on the product form page
        if (boxQuantityInput && boxCostInput && costPriceInput) {
            boxQuantityInput.addEventListener('input', function() {
                if (boxCostInput.value) {
                    calculateCostPrice();
                } else if (costPriceInput.value) {
                    calculateBoxCost();
                }
            });

            boxCostInput.addEventListener('input', calculateCostPrice);

            costPriceInput.addEventListener('input', calculateBoxCost);
        }

        // Jump to page functionality
        const jumpToPageInput = document.getElementById('jump-to-page');
        const jumpPageBtn = document.getElementById('jump-page-btn');

        if (jumpToPageInput && jumpPageBtn) {
            jumpPageBtn.addEventListener('click', function() {
                const pageNum = parseInt(jumpToPageInput.value);
                const maxPage = parseInt(jumpToPageInput.getAttribute('max'));

                if (pageNum && pageNum > 0 && pageNum <= maxPage) {
                    // Build the URL with the current query parameters
                    let url = new URL(window.location.href);
                    let params = new URLSearchParams(url.search);

                    // Update or add the page parameter
                    params.set('page', pageNum);

                    // Redirect to the new URL
                    window.location.href = `${url.pathname}?${params.toString()}`;
                } else {
                    alert(`Please enter a valid page number between 1 and ${maxPage}`);
                    jumpToPageInput.value = "{{ products.number }}";
                }
            });

            // Allow pressing Enter in the jump to page input
            jumpToPageInput.addEventListener('keypress', function(e) {
                if (e.key === 'Enter') {
                    e.preventDefault();
                    jumpPageBtn.click();
                }
            });
        }
    });
</script>
{% endblock %}
