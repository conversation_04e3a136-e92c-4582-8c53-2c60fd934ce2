{% extends 'base.html' %}
{% load static %}



{% block body %}
<div class="conponentSection p-5 bg-gray-200">
    <div class="componentWrapper">
        <!-- Header with Back Button -->
        <div class="flex items-center mb-4">
            <a href="{% url 'settings:dashboard' %}" class="bg-blue-900 text-white p-2 rounded-full hover:bg-blue-800 mr-3">
                <i class="fas fa-arrow-left"></i>
            </a>
            <h2 class="text-2xl font-bold">Product Settings</h2>
        </div>

        <!-- Settings Card -->
        <div class="bg-white rounded-lg shadow-md overflow-hidden">
            <!-- Card Header -->
            <div class="bg-blue-900 text-white p-4">
                <div class="flex items-center">
                    <i class="fas fa-box text-2xl mr-3"></i>
                    <h3 class="text-xl font-bold">Product Configuration</h3>
                </div>
            </div>

            <!-- Card Body -->
            <div class="p-6">
                <form method="post">
                    {% csrf_token %}

                    <div class="mb-6">
                        <h4 class="text-lg font-semibold mb-3">Inventory Management</h4>
                        
                        <div class="flex items-center mb-4">
                            <input type="checkbox" id="auto_deactivate_out_of_stock" name="auto_deactivate_out_of_stock" class="w-5 h-5 text-blue-600 rounded" 
                                {% if settings.auto_deactivate_out_of_stock %}checked{% endif %}>
                            <label for="auto_deactivate_out_of_stock" class="ml-2 text-gray-700">Auto-deactivate out-of-stock products</label>
                        </div>
                        <p class="text-sm text-gray-500 mb-4 ml-7">When enabled, products will be automatically deactivated when they go out of stock</p>
                        
                        <div class="flex items-center mb-4">
                            <input type="checkbox" id="auto_reactivate_in_stock" name="auto_reactivate_in_stock" class="w-5 h-5 text-blue-600 rounded" 
                                {% if settings.auto_reactivate_in_stock %}checked{% endif %}>
                            <label for="auto_reactivate_in_stock" class="ml-2 text-gray-700">Auto-reactivate in-stock products</label>
                        </div>
                        <p class="text-sm text-gray-500 mb-4 ml-7">When enabled, products will be automatically reactivated when they come back in stock</p>
                    </div>

                    <div class="mb-6">
                        <h4 class="text-lg font-semibold mb-3">Display Settings</h4>
                        
                        <label class="block text-sm font-medium mb-2">Items Per Page</label>
                        <input class="border w-full p-4 leading-tight bg-slate-100 rounded"
                               id="default_items_per_page"
                               name="default_items_per_page"
                               type="number"
                               min="5"
                               max="100"
                               step="5"
                               placeholder="Enter number of items per page"
                               value="{{ settings.default_items_per_page }}"
                               required />
                        <p class="text-sm text-gray-500 mt-2">Default number of items to show per page in product lists</p>
                    </div>

                    <!-- Info Box -->
                    <div class="mt-3 p-3 bg-white rounded border border-blue-100 mb-6">
                        <p class="text-sm text-gray-700"><i class="fas fa-info-circle text-blue-600 mr-1"></i> These settings control how products behave in the system, including inventory management and display options.</p>
                    </div>

                    <div class="flex items-center justify-between">
                        <a href="{% url 'settings:dashboard' %}" class="bg-gray-300 hover:bg-gray-400 text-gray-800 font-bold py-3 px-6 rounded">Cancel</a>
                        <button class="bg-blue-900 hover:bg-blue-800 text-white font-bold py-3 px-6 rounded"
                                type="submit"><i class="fas fa-save mr-2"></i> Save Changes</button>
                    </div>
                </form>
            </div>

            <!-- Last Updated Info -->
            <div class="bg-gray-100 p-4 border-t">
                <p class="text-sm text-gray-600">
                    <i class="fas fa-clock mr-1"></i> Last updated: {{ settings.last_updated|date:"F j, Y H:i" }}
                </p>
            </div>
        </div>
    </div>
</div>
{% endblock %}
