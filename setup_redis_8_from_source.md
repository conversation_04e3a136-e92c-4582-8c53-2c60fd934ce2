# Redis 8.0.1 Setup Guide for Windows

## Current Situation
You have Redis 8.0.1 source code, but Redis 8.0.1 for Windows needs to be either:
1. Compiled from source (complex on Windows)
2. Use a pre-built Windows version
3. Use Docker

## Recommended Solutions

### Option 1: Use Pre-built Redis 7.x (Recommended for Windows)
Since Redis 8.0.1 is very new and may not have stable Windows builds:

1. **Download stable Redis for Windows**:
   - Go to: https://github.com/tporadowski/redis/releases
   - Download: `Redis-x64-7.2.4.zip` (latest stable)
   - Extract to: `C:\redis-8.0.1\` (keep your preferred directory name)

### Option 2: Use Docker Redis 8.0.1 (If Docker available)
```cmd
docker run -d -p 6379:6379 --name redis8 redis:8.0.1-alpine
```

### Option 3: Manual Setup from Your Files
If you have Redis 8.0.1 binaries:

1. **Create directory structure**:
   ```cmd
   mkdir C:\redis-8.0.1
   ```

2. **Copy Redis files** to `C:\redis-8.0.1\`:
   - `redis-server.exe`
   - `redis-cli.exe`
   - `redis-check-aof.exe`
   - `redis-check-rdb.exe`
   - `redis-benchmark.exe`

3. **Create configuration file** `C:\redis-8.0.1\redis.windows-service.conf`

## Quick Setup Script

I've created scripts that will work with any Redis version you place in `C:\redis-8.0.1\`:

### Files Created:
1. **`stop_redis_3_start_redis_8.bat`** - Complete upgrade script
2. **`upgrade_to_redis_8.ps1`** - PowerShell version
3. **`verify_redis_8_integration.bat`** - Verification script

### Usage:
1. **Ensure Redis 8.0.1 files are in** `C:\redis-8.0.1\`
2. **Right-click** `upgrade_to_redis_8.ps1` → **Run with PowerShell**
3. **Or run** `stop_redis_3_start_redis_8.bat` as Administrator

## What the Scripts Will Do:

1. **Stop Redis 3.0.504** safely
2. **Create optimized Redis 8.0.1 configuration**
3. **Install Redis 8.0.1 as Windows service**
4. **Start Redis 8.0.1 server**
5. **Test connectivity and basic operations**
6. **Update system PATH**

## Expected Results:

### Before Upgrade:
```
⚠ Redis 3.0.504 detected - using in-memory channel layer
✓ Cache Backend: django_redis.cache.RedisCache
⚠ Channel Backend: channels.layers.InMemoryChannelLayer
```

### After Redis 8.0.1 Upgrade:
```
✓ Redis 8.0.1 detected - using Redis channel layer
✓ Cache Backend: django_redis.cache.RedisCache
✓ Channel Backend: channels_redis.core.RedisChannelLayer
```

## Verification Steps:

1. **Test Redis 8.0.1**:
   ```cmd
   C:\redis-8.0.1\redis-cli.exe ping
   ```

2. **Test Django integration**:
   ```cmd
   python manage.py test_redis --detailed
   ```

3. **Verify WebSocket functionality**:
   ```cmd
   verify_redis_8_integration.bat
   ```

## If You Need Help:

1. **Check if Redis files exist**:
   ```cmd
   dir C:\redis-8.0.1
   ```

2. **If directory is empty**, you need to:
   - Extract Redis 8.0.1 files to this location
   - Or download pre-built Redis for Windows
   - Or use Docker Redis 8.0.1

3. **Run the upgrade script** once files are in place
