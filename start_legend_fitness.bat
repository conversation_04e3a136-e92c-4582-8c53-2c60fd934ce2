@echo off
echo ==========================================
echo   Legend Fitness Club - Startup Script
echo ==========================================
echo.

echo Step 1: Starting Redis 7.4.3...
echo.

REM Check if <PERSON><PERSON> is already running
tasklist /FI "IMAGENAME eq redis-server.exe" 2>NUL | find /I /N "redis-server.exe">NUL
if "%ERRORLEVEL%"=="0" (
    echo ✓ Redis is already running
) else (
    echo Starting Redis server...
    start "Redis 7.4.3" /MIN powershell -Command "cd 'C:\Redis-7.4.3'; .\redis-server.exe redis.conf"
    echo Waiting for Redis to initialize...
    timeout /t 3 /nobreak >nul
)

echo.
echo Step 2: Testing Redis connection...
python manage.py test_redis

echo.
echo Step 3: Starting Django server...
echo.
echo ✓ Redis is running
echo ✓ Starting Django with WebSocket support
echo ✓ Access your gym system at: http://127.0.0.1:8000
echo.
echo Press Ctrl+C to stop the Django server
echo (<PERSON>is will continue running in background)
echo.

python manage.py runserver 8000
