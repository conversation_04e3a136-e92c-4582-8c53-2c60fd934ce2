import os
import re
import sys

def check_for_i18n_tags(file_path):
    """Check if a template file contains any internationalization tags."""
    with open(file_path, 'r', encoding='utf-8', errors='ignore') as f:
        content = f.read()

    # Check for {% load i18n %} tag
    load_i18n = re.search(r'{%\s*load\s+i18n\s*%}', content)

    # Check for {% trans %} tags
    trans_tags = re.findall(r'{%\s*trans\s+.*?%}', content)

    # Check for {% blocktrans %} tags
    blocktrans_tags = re.findall(r'{%\s*blocktrans.*?%}', content)

    # Check for {% endblocktrans %} tags
    endblocktrans_tags = re.findall(r'{%\s*endblocktrans\s*%}', content)

    # Check for trans tags in attributes
    attr_trans_tags = re.findall(r'="{%\s*trans\s+.*?%}"', content)

    # Combine all findings
    all_tags = []
    if load_i18n:
        all_tags.append("{% load i18n %} tag found")

    if trans_tags:
        all_tags.append(f"{len(trans_tags)} trans tags found")

    if blocktrans_tags:
        all_tags.append(f"{len(blocktrans_tags)} blocktrans tags found")

    if endblocktrans_tags:
        all_tags.append(f"{len(endblocktrans_tags)} endblocktrans tags found")

    if attr_trans_tags:
        all_tags.append(f"{len(attr_trans_tags)} attribute trans tags found")

    return all_tags

def find_html_files(directory):
    """Find all HTML files in the given directory and its subdirectories."""
    html_files = []
    for root, dirs, files in os.walk(directory):
        for file in files:
            if file.endswith('.html'):
                html_files.append(os.path.join(root, file))
    return html_files

def main():
    if len(sys.argv) < 2:
        print("Usage: python verify_i18n_removal.py <template_path_or_directory>")
        print("  - Provide a specific template file path to check a single file")
        print("  - Provide a directory path to check all HTML files in that directory and subdirectories")
        sys.exit(1)

    path = sys.argv[1]

    if os.path.isfile(path):
        # Check a single file
        if path.endswith('.html'):
            tags = check_for_i18n_tags(path)
            if tags:
                print(f"WARNING: {path} still contains internationalization tags:")
                for tag in tags:
                    print(f"  - {tag}")
            else:
                print(f"SUCCESS: {path} does not contain any internationalization tags")
        else:
            print(f"Error: {path} is not an HTML file")
    elif os.path.isdir(path):
        # Check all HTML files in the directory and subdirectories
        html_files = find_html_files(path)
        if not html_files:
            print(f"No HTML files found in {path}")
            sys.exit(1)

        files_with_tags = 0
        for html_file in html_files:
            tags = check_for_i18n_tags(html_file)
            if tags:
                files_with_tags += 1
                print(f"WARNING: {html_file} still contains internationalization tags:")
                for tag in tags:
                    print(f"  - {tag}")

        if files_with_tags == 0:
            print(f"SUCCESS: All {len(html_files)} HTML files are free of internationalization tags")
        else:
            print(f"WARNING: {files_with_tags} out of {len(html_files)} HTML files still contain internationalization tags")
    else:
        print(f"Error: {path} does not exist")
        sys.exit(1)

if __name__ == "__main__":
    main()
