{% extends 'base.html' %}
{% load custom_filters %}



{% block body %}
<div class="conponentSection p-5 bg-gray-200">
    <div class="componentWrapper">
        <!-- Entry Form starts  -->
        <div class="formSection bg-white p-4 rounded shadow-md mb-4">
            <h3 class="text-2xl font-bold mb-4">Salary Payment Form</h3>
            <form method="post">
                {% csrf_token %}
                <div class="grid grid-cols-3 gap-4">
                    <!-- Employee Selection -->
                    <div>
                        <label class="block text-sm font-medium mb-1">Employee*</label>
                        <select name="employee" id="employee-select" class="border w-full p-4 leading-tight bg-slate-100" required>
                            <option value="">Select Employee</option>
                            {% for employee in employees %}
                            <option value="{{ employee.id }}" data-salary="{{ employee.salary }}">{{ employee.name }} ({{ employee.role|title }})</option>
                            {% endfor %}
                        </select>
                    </div>

                    <!-- Month & Year -->
                    <div>
                        <label class="block text-sm font-medium mb-1">Month & Year*</label>
                        <input type="month" name="month" class="border w-full p-4 leading-tight bg-slate-100"
                               value="{{ current_month }}" required>
                    </div>

                    <!-- Employment Type -->
                    <div>
                        <label class="block text-sm font-medium mb-1">Employment Type*</label>
                        <select name="employment_type" class="border w-full p-4 leading-tight bg-slate-100" required>
                            <option value="full_time">Full-time</option>
                            <option value="part_time">Part-time</option>
                        </select>
                    </div>

                    <!-- Base Salary -->
                    <div>
                        <label class="block text-sm font-medium mb-1">Base Salary (៛)*</label>
                        <input type="number" name="base_salary" id="base-salary" class="border w-full p-4 leading-tight bg-slate-100" required>
                    </div>

                    <!-- Bonus -->
                    <div>
                        <label class="block text-sm font-medium mb-1">Bonus (៛)</label>
                        <input type="number" name="bonus" class="border w-full p-4 leading-tight bg-slate-100" value="0">
                    </div>

                    <!-- Deduction -->
                    <div>
                        <label class="block text-sm font-medium mb-1">Deduction (៛)</label>
                        <input type="number" name="deduction" class="border w-full p-4 leading-tight bg-slate-100" value="0">
                    </div>

                    <!-- Overtime Hours -->
                    <div>
                        <label class="block text-sm font-medium mb-1">Overtime Hours</label>
                        <input type="number" name="overtime_hours" id="overtime-hours" class="border w-full p-4 leading-tight bg-slate-100" value="0">
                    </div>

                    <!-- Payment Method -->
                    <div>
                        <label class="block text-sm font-medium mb-1">Payment Method*</label>
                        <select name="payment_method" class="border w-full p-4 leading-tight bg-slate-100" required>
                            <option value="cash">Cash</option>
                            <option value="aba">ABA</option>
                            <option value="wing">Wing</option>
                            <option value="bank">Bank Transfer</option>
                            <option value="other">Other</option>
                        </select>
                    </div>

                    <!-- Notes -->
                    <div class="col-span-3">
                        <label class="block text-sm font-medium mb-1">Notes</label>
                        <textarea class="border w-full p-4 leading-tight bg-slate-100"
                                  name="notes"
                                  rows="2"
                                  placeholder="Additional notes"></textarea>
                    </div>

                    <!-- Submit Button -->
                    <div class="col-span-3">
                        <button class="bg-blue-900 text-white font-bold py-2 px-4 w-full" type="submit">Create Salary Record</button>
                    </div>
                </div>
            </form>
        </div>

        <!-- Salary Payments List -->
        <div class="listSection bg-white p-4 rounded shadow-md">
            <div class="flex justify-between items-center mb-4">
                <h3 class="text-2xl font-bold">Salary Payments</h3>
                <div class="text-right">
                    <p class="font-bold">Total Paid: {{ total_paid|format_khr }}</p>
                </div>
            </div>

            <!-- Filter Form -->
            <form method="get" class="mb-4 grid grid-cols-3 gap-4">
                <div>
                    <label class="block text-sm font-medium mb-1">Employee</label>
                    <select name="employee" class="border w-full p-2 leading-tight bg-slate-100">
                        <option value="">All Employees</option>
                        {% for employee in employees %}
                        <option value="{{ employee.id }}" {% if employee_filter == employee.id|stringformat:"s" %}selected{% endif %}>{{ employee.name }}</option>
                        {% endfor %}
                    </select>
                </div>
                <div>
                    <label class="block text-sm font-medium mb-1">Month</label>
                    <input type="month" name="month" class="border w-full p-2 leading-tight bg-slate-100"
                           value="{{ month_filter }}">
                </div>
                <div>
                    <label class="block text-sm font-medium mb-1">Status</label>
                    <select name="status" class="border w-full p-2 leading-tight bg-slate-100">
                        <option value="">All Statuses</option>
                        <option value="pending" {% if status_filter == 'pending' %}selected{% endif %}>Pending</option>
                        <option value="paid" {% if status_filter == 'paid' %}selected{% endif %}>Paid</option>
                    </select>
                </div>
                <div class="col-span-3">
                    <button type="submit" class="bg-blue-900 text-white font-bold py-2 px-4">Filter</button>
                    <a href="{% url 'salary:index' %}" class="bg-gray-500 text-white font-bold py-2 px-4 ml-2">Reset</a>
                </div>
            </form>

            <div class="relative overflow-x-auto">
                <table class="w-full text-sm text-left">
                    <thead class="text-sm uppercase bg-blue-900 text-gray-50"><tr>
                            <th scope="col" class="px-6 py-3">Payroll ID</th>
                            <th scope="col" class="px-6 py-3">Employee</th>
                            <th scope="col" class="px-6 py-3">Month</th>
                            <th scope="col" class="px-6 py-3">Base Salary</th>
                            <th scope="col" class="px-6 py-3">Bonus</th>
                            <th scope="col" class="px-6 py-3">Deduction</th>
                            <th scope="col" class="px-6 py-3">Final Pay</th>
                            <th scope="col" class="px-6 py-3">Status</th>
                            <th scope="col" class="px-6 py-3">Actions</th>
                        </tr>
                    </thead>
                    <tbody>
                        {% for payment in payments %}
                        <tr class="bg-white border">
                            <td class="px-6 py-4">{{ payment.payroll_id }}</td>
                            <td class="px-6 py-4">{{ payment.employee.name }}</td>
                            <td class="px-6 py-4">{{ payment.month|date:"M Y" }}</td>
                            <td class="px-6 py-4">{{ payment.base_salary|format_khr }}</td>
                            <td class="px-6 py-4">{{ payment.bonus|format_khr }}</td>
                            <td class="px-6 py-4">{{ payment.deduction|format_khr }}</td>
                            <td class="px-6 py-4">{{ payment.final_pay|format_khr }}</td>
                            <td class="px-6 py-4">
                                {% if payment.payment_status == 'paid' %}
                                <span class="bg-green-100 text-green-800 text-xs font-medium px-2.5 py-0.5 rounded">Paid</span>
                                {% else %}
                                <span class="bg-yellow-100 text-yellow-800 text-xs font-medium px-2.5 py-0.5 rounded">Pending</span>
                                {% endif %}
                            </td>
                            <td class="px-6 py-4">
                                <div class="flex space-x-2">
                                    <a href="{% url 'salary:view' payment.id %}" class="text-blue-600 hover:underline">View</a>
                                    {% if payment.payment_status == 'pending' %}
                                    <a href="{% url 'salary:edit' payment.id %}" class="text-green-600 hover:underline">Edit</a>
                                    <a href="{% url 'salary:process' payment.id %}" class="text-purple-600 hover:underline"
                                       onclick="return confirm('Are you sure you want to process this payment?')">Process</a>
                                    <a href="{% url 'salary:delete' payment.id %}" class="text-red-600 hover:underline"
                                       onclick="return confirm('Are you sure you want to delete this payment record?')">Delete</a>
                                    {% else %}
                                    <a href="{% url 'salary:print' payment.id %}" class="text-purple-600 hover:underline">Print</a>
                                    {% endif %}
                                </div>
                            </td>
                        </tr>
                        {% empty %}
                        <tr class="bg-white border">
                            <td colspan="9" class="px-6 py-4 text-center">No salary payments found matching the filter criteria.</td>
                        </tr>
                        {% endfor %}
                    </tbody>
                </table>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block js %}
<script>
    document.addEventListener('DOMContentLoaded', function() {
        const employeeSelect = document.getElementById('employee-select');
        const baseSalaryInput = document.getElementById('base-salary');

        employeeSelect.addEventListener('change', function() {
            const selectedOption = employeeSelect.options[employeeSelect.selectedIndex];
            const salary = selectedOption.getAttribute('data-salary');

            if (salary) {
                baseSalaryInput.value = salary;
            } else {
                baseSalaryInput.value = '';
            }
        });
    });
</script>
{% endblock %}
