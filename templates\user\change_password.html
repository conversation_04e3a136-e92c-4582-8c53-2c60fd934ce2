{% extends "../base.html" %}



{% block body %}
<!-- component sections starts  -->
<div class="conponentSection p-5 bg-gray-200">
  <div class="componentWrapper">
    <!-- Header with navigation -->
    <div class="flex justify-between items-center mb-4">
      <h3 class="text-2xl font-bold">Change Password</h3>
      <div class="flex space-x-2">
        <a href="{% url 'user:profile' %}" class="bg-blue-900 text-white px-4 py-2 rounded">
          <i class="fa-solid fa-user mr-2"></i>Profile
        </a>
        <a href="{% url 'user:change_password' %}" class="bg-green-900 text-white px-4 py-2 rounded">
          <i class="fa-solid fa-key mr-2"></i>Change Password
        </a>
      </div>
    </div>

    <!-- Change Password Form -->
    <div class="max-w-md mx-auto bg-white p-6 rounded-lg shadow-md">
      <h3 class="text-lg font-semibold mb-4 border-b pb-2">Change Your Password</h3>

      <form method="post" class="space-y-4">
        {% csrf_token %}

        <div>
          <label for="current_password" class="block text-sm font-medium text-gray-700">Current Password</label>
          <div class="relative mt-1">
            <input type="password" id="current_password" name="current_password" required class="block w-full pr-10 rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500">
            <button
              type="button"
              class="absolute inset-y-0 right-0 flex items-center px-3 text-gray-600 hover:text-gray-800 focus:outline-none"
              onclick="togglePassword('current_password')"
              aria-label="Toggle current password visibility">
              <i id="current_password_icon" class="fas fa-eye"></i>
            </button>
          </div>
        </div>

        <div>
          <label for="new_password" class="block text-sm font-medium text-gray-700">New Password</label>
          <div class="relative mt-1">
            <input type="password" id="new_password" name="new_password" required class="block w-full pr-10 rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500">
            <button
              type="button"
              class="absolute inset-y-0 right-0 flex items-center px-3 text-gray-600 hover:text-gray-800 focus:outline-none"
              onclick="togglePassword('new_password')"
              aria-label="Toggle new password visibility">
              <i id="new_password_icon" class="fas fa-eye"></i>
            </button>
          </div>
          <p class="mt-1 text-sm text-gray-500">Password must be at least 8 characters long and include a mix of letters, numbers, and special characters.</p>
        </div>

        <div>
          <label for="confirm_password" class="block text-sm font-medium text-gray-700">Confirm New Password</label>
          <div class="relative mt-1">
            <input type="password" id="confirm_password" name="confirm_password" required class="block w-full pr-10 rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500">
            <button
              type="button"
              class="absolute inset-y-0 right-0 flex items-center px-3 text-gray-600 hover:text-gray-800 focus:outline-none"
              onclick="togglePassword('confirm_password')"
              aria-label="Toggle confirm password visibility">
              <i id="confirm_password_icon" class="fas fa-eye"></i>
            </button>
          </div>
        </div>

        <div class="pt-4">
          <button type="submit" class="w-full flex justify-center py-2 px-4 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-blue-900 hover:bg-blue-800 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500">Change Password</button>
        </div>
      </form>

      <div class="mt-6 border-t border-gray-200 pt-4">
        <h4 class="text-sm font-medium text-gray-700 mb-2">Password Security Tips:</h4>
        <ul class="list-disc pl-5 text-sm text-gray-600 space-y-1">
          <li>Use a mix of uppercase and lowercase letters</li>
          <li>Include numbers and special characters</li>
          <li>Avoid using personal information</li>
          <li>Don't reuse passwords from other sites</li>
          <li>Consider using a password manager</li>
        </ul>
      </div>
    </div>
  </div>
</div>
{% endblock body %}

{% block js %}
<script>
  function togglePassword(fieldId) {
    const passwordInput = document.getElementById(fieldId);
    const passwordIcon = document.getElementById(fieldId + '_icon');
    const toggleButton = passwordIcon.parentElement;

    if (passwordInput && passwordIcon) {
      if (passwordInput.type === 'password') {
        passwordInput.type = 'text';
        passwordIcon.classList.remove('fa-eye');
        passwordIcon.classList.add('fa-eye-slash');
        toggleButton.setAttribute('aria-label', 'Hide password');
      } else {
        passwordInput.type = 'password';
        passwordIcon.classList.remove('fa-eye-slash');
        passwordIcon.classList.add('fa-eye');
        toggleButton.setAttribute('aria-label', 'Show password');
      }
    }
  }
</script>
{% endblock js %}
