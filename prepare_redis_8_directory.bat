@echo off
echo Redis 8.0.1 Directory Preparation for Legend Fitness Club
echo ========================================================
echo.

REM Check if running as administrator
net session >nul 2>&1
if %errorLevel% neq 0 (
    echo This script should be run as Administrator for best results
    echo Right-click on this file and select "Run as administrator"
    echo.
    echo Continuing anyway...
)

echo Step 1: Creating Redis 8.0.1 directory structure...

REM Create Redis 8.0.1 directory
if not exist "C:\redis-8.0.1" (
    mkdir "C:\redis-8.0.1"
    echo ✓ Created directory: C:\redis-8.0.1
) else (
    echo ✓ Directory already exists: C:\redis-8.0.1
)

echo.
echo Step 2: Checking for Redis 8.0.1 files...

REM Check for essential Redis files
set "redis_dir=C:\redis-8.0.1"
set "files_found=0"

if exist "%redis_dir%\redis-server.exe" (
    echo ✓ Found: redis-server.exe
    set /a files_found+=1
) else (
    echo ✗ Missing: redis-server.exe
)

if exist "%redis_dir%\redis-cli.exe" (
    echo ✓ Found: redis-cli.exe
    set /a files_found+=1
) else (
    echo ✗ Missing: redis-cli.exe
)

echo.
if %files_found% geq 2 (
    echo ✓ Redis 8.0.1 files found! Ready to proceed with upgrade.
    echo.
    echo Next steps:
    echo 1. Run: upgrade_to_redis_8.ps1 ^(PowerShell^)
    echo 2. Or run: stop_redis_3_start_redis_8.bat ^(as Administrator^)
    echo.
    
    choice /C YN /M "Do you want to proceed with the Redis 8.0.1 upgrade now"
    if errorlevel 2 goto :manual_instructions
    if errorlevel 1 goto :run_upgrade
    
) else (
    echo ✗ Redis 8.0.1 files not found in C:\redis-8.0.1
    echo.
    goto :setup_instructions
)

:run_upgrade
echo.
echo Running Redis 8.0.1 upgrade...
call stop_redis_3_start_redis_8.bat
goto :end

:setup_instructions
echo Redis 8.0.1 Setup Instructions:
echo ===============================
echo.
echo Option 1: If you have Redis 8.0.1 source/binaries
echo 1. Copy these files to C:\redis-8.0.1\:
echo    - redis-server.exe
echo    - redis-cli.exe
echo    - redis-check-aof.exe
echo    - redis-check-rdb.exe
echo    - redis-benchmark.exe
echo.
echo Option 2: Download pre-built Redis for Windows
echo 1. Go to: https://github.com/tporadowski/redis/releases
echo 2. Download: Redis-x64-7.2.4.zip ^(latest stable^)
echo 3. Extract to: C:\redis-8.0.1\
echo.
echo Option 3: Use Docker ^(if available^)
echo 1. Run: docker run -d -p 6379:6379 --name redis8 redis:8.0.1-alpine
echo.
echo After setting up files, run this script again.
echo.
goto :end

:manual_instructions
echo.
echo Manual upgrade instructions:
echo 1. Right-click "upgrade_to_redis_8.ps1" and select "Run with PowerShell"
echo 2. Or run "stop_redis_3_start_redis_8.bat" as Administrator
echo 3. Then run "verify_redis_8_integration.bat" to test
echo.

:end
echo.
echo Current Redis status:
echo ====================

REM Check current Redis status
echo Checking current Redis 3.0.504...
sc query redis >nul 2>&1
if %errorlevel% equ 0 (
    echo ✓ Redis 3.0.504 service is installed
    sc query redis | findstr STATE
) else (
    echo ⚠ Redis 3.0.504 service not found
)

echo.
echo Checking Redis 8.0.1 setup...
if exist "C:\redis-8.0.1\redis-server.exe" (
    echo ✓ Redis 8.0.1 files are ready
) else (
    echo ⚠ Redis 8.0.1 files need to be installed
)

echo.
echo Files created for Redis 8.0.1 upgrade:
echo - stop_redis_3_start_redis_8.bat
echo - upgrade_to_redis_8.ps1
echo - verify_redis_8_integration.bat
echo - setup_redis_8_from_source.md
echo.

pause
