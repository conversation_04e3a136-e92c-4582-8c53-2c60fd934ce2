{% extends 'base.html' %}
{% load custom_filters %}



{% block extra_css %}
<style>
    .receipt-container {
        font-family: Arial, sans-serif;
        max-width: 800px;
        margin: 0 auto;
        padding: 20px;
        border: 1px solid #ddd;
    }
    
    .receipt-header {
        text-align: center;
        margin-bottom: 20px;
    }
    
    .receipt-header h1 {
        margin: 5px 0;
        color: {{ template.text_color }};
    }
    
    .receipt-header h2 {
        margin: 5px 0;
        color: {{ template.accent_color }};
    }
    
    .receipt-body {
        margin-bottom: 20px;
    }
    
    .receipt-table {
        width: 100%;
        border-collapse: collapse;
        margin-bottom: 20px;
    }
    
    .receipt-table th, .receipt-table td {
        padding: 8px;
        text-align: left;
        border-bottom: 1px solid #ddd;
    }
    
    .receipt-table th {
        background-color: {{ template.accent_color }};
        color: white;
    }
    
    .receipt-footer {
        text-align: center;
        margin-top: 30px;
        color: {{ template.text_color }};
    }
    
    .signature-section {
        display: flex;
        justify-content: space-between;
        margin-top: 50px;
    }
    
    .signature-box {
        text-align: center;
        width: 40%;
    }
    
    .signature-line {
        border-top: 1px solid #000;
        margin-top: 50px;
        margin-bottom: 10px;
    }
    
    {% if template.custom_css %}
    {{ template.custom_css|safe }}
    {% endif %}
</style>
{% endblock %}

{% block body %}
<div class="conponentSection p-5 bg-gray-200">
    <div class="componentWrapper">
        <!-- Header with navigation -->
        <div class="flex justify-between items-center mb-4">
            <h3 class="text-2xl font-bold">Template Preview</h3>
            <div class="flex space-x-2">
                <a href="{% url 'billmanagement:template_list' %}" class="bg-blue-900 text-white px-4 py-2 rounded">
                    <i class="fa-solid fa-arrow-left mr-2"></i>Back to Templates
                </a>
                <a href="{% url 'billmanagement:edit_template' template.id %}" class="bg-green-600 text-white px-4 py-2 rounded">
                    <i class="fa-solid fa-edit mr-2"></i>Edit Template
                </a>
            </div>
        </div>

        <!-- Template Information -->
        <div class="bg-white p-4 rounded shadow-md mb-4">
            <h4 class="text-lg font-semibold mb-2">Template Details</h4>
            <div class="grid grid-cols-1 md:grid-cols-3 gap-4">
                <div>
                    <p class="text-gray-600 text-sm">Name:</p>
                    <p class="font-medium">{{ template.name }}</p>
                </div>
                <div>
                    <p class="text-gray-600 text-sm">Language:</p>
                    <p class="font-medium">
                        {% if template.language == 'en' %}English
                        {% elif template.language == 'km' %}Khmer
                        {% else %}Bilingual
                        {% endif %}
                    </p>
                </div>
                <div>
                    <p class="text-gray-600 text-sm">Default:</p>
                    <p class="font-medium">
                        {% if template.is_default %}
                        <span class="bg-green-100 text-green-800 text-xs font-medium px-2.5 py-0.5 rounded">Yes</span>
                        {% else %}
                        <span class="bg-gray-100 text-gray-800 text-xs font-medium px-2.5 py-0.5 rounded">No</span>
                        {% endif %}
                    </p>
                </div>
            </div>
        </div>

        <!-- Receipt Preview -->
        <div class="bg-white p-4 rounded shadow-md">
            <h4 class="text-lg font-semibold mb-4">Receipt Preview</h4>
            
            <div class="receipt-container" style="background-color: {{ template.background_color }};">
                <div class="receipt-header">
                    {% if template.company_logo %}
                    <img src="{{ template.company_logo.url }}" alt="Company Logo" style="max-height: 80px; margin-bottom: 10px;">
                    {% endif %}
                    <h1>{{ template.header_text }}</h1>
                    <h2>{{ template.subheader_text }}</h2>
                </div>
                
                <div class="receipt-body">
                    <div class="grid grid-cols-2 gap-4 mb-4">
                        <div>
                            <p><strong>Receipt No:</strong> {{ bill.bill_id }}</p>
                            <p><strong>Date:</strong> {{ bill.payment_date|date:"d-M-Y H:i" }}</p>
                            <p><strong>Payment Method:</strong> {{ bill.get_payment_method_display }}</p>
                        </div>
                        <div>
                            <p><strong>Provider/Vendor:</strong> {{ bill.provider }}</p>
                            <p><strong>Category:</strong> {{ bill.get_category_display }}</p>
                            <p><strong>Period:</strong> {{ bill.month_year|date:"F Y" }} ({{ bill.get_payment_period_display }})</p>
                        </div>
                    </div>
                    
                    <table class="receipt-table">
                        <thead><tr>
                                <th>Description</th>
                                <th>Period</th>
                                <th>Amount (KHR)</th>
                                {% if bill.amount_usd %}
                                <th>Amount (USD)</th>
                                {% endif %}
                            </tr>
                        </thead>
                        <tbody>
                            <tr>
                                <td>{{ bill.get_category_display }} - {{ bill.provider }}</td>
                                <td>{{ bill.get_payment_period_display }}</td>
                                <td>{{ bill.amount_khr|format_khr }}</td>
                                {% if bill.amount_usd %}
                                <td>${{ bill.amount_usd }}</td>
                                {% endif %}
                            </tr>
                            {% if bill.description %}
                            <tr>
                                <td colspan="{% if bill.amount_usd %}4{% else %}3{% endif %}">
                                    <strong>Description:</strong> {{ bill.description }}
                                </td>
                            </tr>
                            {% endif %}
                        </tbody>
                        <tfoot>
                            <tr>
                                <td colspan="2"><strong>Total</strong></td>
                                <td><strong>{{ bill.amount_khr|format_khr }}</strong></td>
                                {% if bill.amount_usd %}
                                <td><strong>${{ bill.amount_usd }}</strong></td>
                                {% endif %}
                            </tr>
                        </tfoot>
                    </table>
                    
                    {% if bill.notes %}
                    <div class="mb-4">
                        <p><strong>Notes:</strong></p>
                        <p>{{ bill.notes }}</p>
                    </div>
                    {% endif %}
                    
                    {% if template.show_company_info %}
                    <div class="mb-4 p-3 bg-gray-100 rounded">
                        <p><strong>Legend Fitness</strong></p>
                        <p>Address: Phnom Penh, Cambodia</p>
                        <p>Phone: +855 XX XXX XXX</p>
                        <p>Email: <EMAIL></p>
                    </div>
                    {% endif %}
                </div>
                
                {% if template.show_signatures %}
                <div class="signature-section">
                    <div class="signature-box">
                        <div class="signature-line"></div>
                        <p>Authorized Signature</p>
                    </div>
                    <div class="signature-box">
                        <div class="signature-line"></div>
                        <p>Received By</p>
                    </div>
                </div>
                {% endif %}
                
                <div class="receipt-footer">
                    <p>{{ template.footer_text }}</p>
                    <p>Printed on: {{ now|date:"d-M-Y H:i" }}</p>
                </div>
            </div>
            
            <div class="mt-6 flex justify-center">
                <a href="{% url 'billmanagement:edit_template' template.id %}" class="bg-green-600 text-white px-6 py-2 rounded">Edit Template</a>
            </div>
        </div>
    </div>
</div>
{% endblock %}
