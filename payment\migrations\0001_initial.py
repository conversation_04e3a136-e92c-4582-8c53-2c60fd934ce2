# Generated by Django 5.1.6 on 2025-04-26 03:49

from django.db import migrations, models


class Migration(migrations.Migration):

    initial = True

    dependencies = [
    ]

    operations = [
        migrations.CreateModel(
            name='Payment',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('invoice_no', models.CharField(max_length=20, unique=True, verbose_name='Invoice No.')),
                ('amount_khr', models.IntegerField(verbose_name='Amount (KHR)')),
                ('amount_usd', models.DecimalField(blank=True, decimal_places=2, max_digits=10, null=True, verbose_name='Amount (USD)')),
                ('payment_method', models.CharField(choices=[('cash', 'Cash'), ('aba', 'ABA'), ('wing', 'Wing'), ('other', 'Other')], default='cash', max_length=20, verbose_name='Payment Method')),
                ('payment_date', models.DateTimeField(auto_now_add=True, verbose_name='Payment Date')),
                ('notes', models.TextField(blank=True, null=True, verbose_name='Notes')),
            ],
            options={
                'ordering': ['-payment_date'],
            },
        ),
    ]
