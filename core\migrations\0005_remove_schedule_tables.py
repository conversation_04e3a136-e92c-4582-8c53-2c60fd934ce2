# Generated manually

from django.db import migrations


class Migration(migrations.Migration):

    dependencies = [
        ('core', '0004_remove_report_tables'),
    ]

    operations = [
        migrations.RunSQL(
            sql="DROP TABLE IF EXISTS user_user_schedules;",
            reverse_sql="",  # No reverse operation
        ),
        migrations.RunSQL(
            sql="DROP TABLE IF EXISTS schedule_schedule;",
            reverse_sql="",  # No reverse operation
        ),
        migrations.RunSQL(
            sql="DELETE FROM django_migrations WHERE app = 'schedule';",
            reverse_sql="",  # No reverse operation
        ),
    ]
