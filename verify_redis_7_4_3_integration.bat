@echo off
echo Redis 7.4.3 Integration Verification for Legend Fitness Club
echo ==========================================================
echo.

cd /d "c:\Final Project\legend_fitness_club-gym-ms"

echo Step 1: Testing Redis 7.4.3 connectivity...
echo.

REM Test Redis connection
"C:\Redis-7.4.3\redis-cli.exe" ping >nul 2>&1
if %errorlevel% equ 0 (
    echo ✓ Redis 7.4.3 is responding to ping
    
    REM Get Redis version
    echo ✓ Redis version information:
    "C:\Redis-7.4.3\redis-cli.exe" info server | findstr redis_version
    
    REM Get memory info
    echo ✓ Memory usage:
    "C:\Redis-7.4.3\redis-cli.exe" info memory | findstr used_memory_human
    
    REM Test keyspace notifications
    echo ✓ Keyspace notifications:
    "C:\Redis-7.4.3\redis-cli.exe" config get notify-keyspace-events
    
    echo.
) else (
    echo ✗ Redis 7.4.3 is not responding
    echo Please ensure Redis 7.4.3 is running on port 6379
    pause
    exit /b 1
)

echo Step 2: Testing Django Redis integration...
echo.

REM Test Django Redis integration
python manage.py test_redis --detailed

echo.
echo Step 3: Testing Redis 7.4.3 advanced features...
echo.

REM Test Redis 7.4.3 specific features
echo Testing Redis 7.4.3 lazy freeing...
"C:\Redis-7.4.3\redis-cli.exe" config get lazyfree-lazy-eviction

echo Testing Redis 7.4.3 memory policy...
"C:\Redis-7.4.3\redis-cli.exe" config get maxmemory-policy

echo Testing Redis 7.4.3 performance...
"C:\Redis-7.4.3\redis-cli.exe" --latency-history -i 1 | head -5

echo.
echo Step 4: Starting Django server to verify Redis 7.4.3 detection...
echo.

echo Starting Django server...
echo Look for these messages:
echo "✓ Redis 7.4.3 detected - using Redis channel layer"
echo "✓ Redis detected - using Redis cache backend"
echo "✓ Channel Backend: channels_redis.core.RedisChannelLayer"
echo.

echo Press Ctrl+C to stop the server after verification
echo.

python manage.py runserver 8000

pause
