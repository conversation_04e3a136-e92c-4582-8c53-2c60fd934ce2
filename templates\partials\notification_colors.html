{% load static %}


{% comment %}
This template includes dynamic CSS for notification colors based on settings.
It should be included in the base template to apply custom notification colors.
{% endcomment %}

<style>
    /* Custom notification colors from settings */
    .notification.success {
        background-color: {{ settings.notification_success_color }} !important;
    }

    .notification.error {
        background-color: {{ settings.notification_error_color }} !important;
    }

    .notification.warning {
        background-color: {{ settings.notification_warning_color }} !important;
    }

    .notification.info {
        background-color: {{ settings.notification_info_color }} !important;
    }

    /* Override default notification styles */
    .notification {
        color: {{ settings.notification_text_color }} !important;
        background-color: {{ settings.notification_info_color }} !important;
    }

    .notification-title,
    .notification-message,
    .notification-icon {
        color: {{ settings.notification_text_color }} !important;
    }

    .notification-close {
        color: {{ settings.notification_text_color }} !important;
        opacity: 0.7;
    }

    .notification-close:hover {
        color: {{ settings.notification_text_color }} !important;
        opacity: 1;
    }

    /* Override any inline styles */
    .notification-progress {
        background-color: rgba(255, 255, 255, 0.3) !important;
    }
</style>
