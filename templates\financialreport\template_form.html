{% extends "base.html" %}
{% load custom_filters %}



{% block body %}
<div class="conponentSection p-5 sm:ml-64 bg-gray-100">
    <div class="componentWrapper max-w-7xl mx-auto">
        <!-- Header with title and actions -->
        <div class="bg-white p-6 rounded-lg shadow-sm mb-6 border-l-4 border-blue-600">
            <div class="flex flex-col md:flex-row justify-between items-start md:items-center mb-6">
                <div>
                    <div class="flex items-center">
                        <a href="{% url 'financialreport:template_list' %}" class="mr-3 bg-gray-100 hover:bg-gray-200 text-gray-700 p-2 rounded-full transition-colors">
                            <i class="fa-solid fa-arrow-left"></i>
                        </a>
                        <div>
                            <h2 class="text-3xl font-bold text-gray-800">{{ title }}</h2>
                            <p class="text-gray-600 mt-1">{% if template %}Edit existing template{% else %}Create a new report template{% endif %}</p>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Template Form -->
        <div class="bg-white rounded-lg shadow-sm p-6 mb-6">
            <form method="post" class="space-y-6">
                {% csrf_token %}

                <!-- Basic Information -->
                <div class="border-b border-gray-200 pb-6">
                    <h3 class="text-lg font-medium text-gray-900 mb-4">Basic Information</h3>
                    <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                        <div>
                            <label for="name" class="block text-sm font-medium text-gray-700 mb-1">Template Name</label>
                            <input type="text" name="name" id="name" value="{{ template.name|default:'' }}" required
                                class="w-full border border-gray-300 rounded-md p-2 focus:ring-blue-500 focus:border-blue-500">
                        </div>
                        <div>
                            <label for="template_type" class="block text-sm font-medium text-gray-700 mb-1">Template Type</label>
                            <select name="template_type" id="template_type" required
                                class="w-full border border-gray-300 rounded-md p-2 focus:ring-blue-500 focus:border-blue-500">
                                <option value="">Select a type</option>
                                <option value="income" {% if template.template_type == 'income' or initial_type == 'income' and not template %}selected{% endif %}>Income Report</option>
                                <option value="expense" {% if template.template_type == 'expense' or initial_type == 'expense' and not template %}selected{% endif %}>Expense Report</option>
                                <option value="balance" {% if template.template_type == 'balance' or initial_type == 'balance' and not template %}selected{% endif %}>Balance Report</option>
                            </select>
                        </div>
                        <div class="flex items-center">
                            <input type="checkbox" name="is_default" id="is_default" {% if template.is_default %}checked{% endif %}
                                class="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded">
                            <label for="is_default" class="ml-2 block text-sm text-gray-700">Set as default template</label>
                        </div>
                    </div>
                </div>

                <!-- Styling Options -->
                <div class="border-b border-gray-200 pb-6">
                    <h3 class="text-lg font-medium text-gray-900 mb-4">Styling Options</h3>
                    <div class="grid grid-cols-1 md:grid-cols-3 gap-6">
                        <div>
                            <label for="header_color" class="block text-sm font-medium text-gray-700 mb-1">Header Color</label>
                            <div class="flex">
                                <input type="color" name="header_color" id="header_color" value="{{ template.header_color|default:default_colors.header_color|default:'#2563eb' }}"
                                    class="h-10 w-10 border border-gray-300 rounded-md">
                                <input type="text" value="{{ template.header_color|default:default_colors.header_color|default:'#2563eb' }}" id="header_color_text"
                                    class="ml-2 w-full border border-gray-300 rounded-md p-2 focus:ring-blue-500 focus:border-blue-500">
                            </div>
                        </div>
                        <div>
                            <label for="text_color" class="block text-sm font-medium text-gray-700 mb-1">Text Color</label>
                            <div class="flex">
                                <input type="color" name="text_color" id="text_color" value="{{ template.text_color|default:'#333333' }}"
                                    class="h-10 w-10 border border-gray-300 rounded-md">
                                <input type="text" value="{{ template.text_color|default:'#333333' }}" id="text_color_text"
                                    class="ml-2 w-full border border-gray-300 rounded-md p-2 focus:ring-blue-500 focus:border-blue-500">
                            </div>
                        </div>
                        <div>
                            <label for="accent_color" class="block text-sm font-medium text-gray-700 mb-1">Accent Color</label>
                            <div class="flex">
                                <input type="color" name="accent_color" id="accent_color" value="{{ template.accent_color|default:default_colors.accent_color|default:'#2563eb' }}"
                                    class="h-10 w-10 border border-gray-300 rounded-md">
                                <input type="text" value="{{ template.accent_color|default:default_colors.accent_color|default:'#2563eb' }}" id="accent_color_text"
                                    class="ml-2 w-full border border-gray-300 rounded-md p-2 focus:ring-blue-500 focus:border-blue-500">
                            </div>
                        </div>
                        <div>
                            <label for="background_color" class="block text-sm font-medium text-gray-700 mb-1">Background Color</label>
                            <div class="flex">
                                <input type="color" name="background_color" id="background_color" value="{{ template.background_color|default:'#ffffff' }}"
                                    class="h-10 w-10 border border-gray-300 rounded-md">
                                <input type="text" value="{{ template.background_color|default:'#ffffff' }}" id="background_color_text"
                                    class="ml-2 w-full border border-gray-300 rounded-md p-2 focus:ring-blue-500 focus:border-blue-500">
                            </div>
                        </div>
                        <div>
                            <label for="table_header_color" class="block text-sm font-medium text-gray-700 mb-1">Table Header Color</label>
                            <div class="flex">
                                <input type="color" name="table_header_color" id="table_header_color" value="{{ template.table_header_color|default:'#f8f9fa' }}"
                                    class="h-10 w-10 border border-gray-300 rounded-md">
                                <input type="text" value="{{ template.table_header_color|default:'#f8f9fa' }}" id="table_header_color_text"
                                    class="ml-2 w-full border border-gray-300 rounded-md p-2 focus:ring-blue-500 focus:border-blue-500">
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Content Options -->
                <div>
                    <h3 class="text-lg font-medium text-gray-900 mb-4">Content Options</h3>
                    <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                        <div class="flex items-center">
                            <input type="checkbox" name="show_logo" id="show_logo" {% if template.show_logo or template.show_logo is None %}checked{% endif %}
                                class="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded">
                            <label for="show_logo" class="ml-2 block text-sm text-gray-700">Show Logo</label>
                        </div>
                        <div class="flex items-center">
                            <input type="checkbox" name="show_footer" id="show_footer" {% if template.show_footer or template.show_footer is None %}checked{% endif %}
                                class="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded">
                            <label for="show_footer" class="ml-2 block text-sm text-gray-700">Show Footer</label>
                        </div>
                        <div class="md:col-span-2">
                            <label for="footer_text" class="block text-sm font-medium text-gray-700 mb-1">Footer Text</label>
                            <input type="text" name="footer_text" id="footer_text" value="{{ template.footer_text|default:'Legend Fitness Club' }}"
                                class="w-full border border-gray-300 rounded-md p-2 focus:ring-blue-500 focus:border-blue-500">
                        </div>
                    </div>
                </div>

                <!-- Form Actions -->
                <div class="flex justify-end space-x-3 pt-4">
                    <a href="{% url 'financialreport:template_list' %}" class="bg-gray-200 text-gray-700 px-4 py-2 rounded-md hover:bg-gray-300 transition-colors">Cancel</a>
                    <button type="submit" class="bg-blue-600 text-white px-4 py-2 rounded-md hover:bg-blue-700 transition-colors">
                        {% if template %}Update{% else %}Create{% endif %} Template
                    </button>
                </div>
            </form>
        </div>
    </div>
</div>
{% endblock %}

{% block js %}
<script>
    document.addEventListener('DOMContentLoaded', function() {
        // Sync color inputs with text inputs
        const colorInputs = [
            { color: 'header_color', text: 'header_color_text' },
            { color: 'text_color', text: 'text_color_text' },
            { color: 'accent_color', text: 'accent_color_text' },
            { color: 'background_color', text: 'background_color_text' },
            { color: 'table_header_color', text: 'table_header_color_text' }
        ];

        colorInputs.forEach(function(item) {
            const colorInput = document.getElementById(item.color);
            const textInput = document.getElementById(item.text);

            colorInput.addEventListener('input', function() {
                textInput.value = colorInput.value;
            });

            textInput.addEventListener('input', function() {
                if (/^#[0-9A-F]{6}$/i.test(textInput.value)) {
                    colorInput.value = textInput.value;
                }
            });
        });
    });
</script>
{% endblock %}
