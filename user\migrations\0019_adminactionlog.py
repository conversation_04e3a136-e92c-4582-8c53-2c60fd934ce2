# Generated by Django 5.0.2 on 2025-05-21 01:11

import django.db.models.deletion
from django.conf import settings
from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('user', '0018_alter_user_role'),
    ]

    operations = [
        migrations.CreateModel(
            name='AdminActionLog',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('action_type', models.CharField(choices=[('create_user', 'Create User'), ('delete_user', 'Delete User'), ('deactivate_user', 'Deactivate User'), ('activate_user', 'Activate User'), ('edit_user', 'Edit User'), ('login', 'Login'), ('logout', 'Logout'), ('password_change', 'Password Change'), ('settings_change', 'Settings Change'), ('other', 'Other Action')], max_length=50, verbose_name='Action Type')),
                ('action_time', models.DateTimeField(auto_now_add=True, verbose_name='Action Time')),
                ('ip_address', models.GenericIPAddressField(blank=True, null=True, verbose_name='IP Address')),
                ('description', models.TextField(blank=True, verbose_name='Description')),
                ('target_user', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='target_actions', to=settings.AUTH_USER_MODEL, verbose_name='Target User')),
                ('user', models.ForeignKey(null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='admin_actions', to=settings.AUTH_USER_MODEL, verbose_name='Admin User')),
            ],
            options={
                'verbose_name': 'Admin Action Log',
                'verbose_name_plural': 'Admin Action Logs',
                'ordering': ['-action_time'],
            },
        ),
    ]
