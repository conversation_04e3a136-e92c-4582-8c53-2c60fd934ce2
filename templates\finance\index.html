{% extends 'base.html' %}
{% load custom_filters %}



{% block body %}
<div class="conponentSection p-5 bg-gray-200">
    <div class="componentWrapper">
        <!-- Header with navigation -->
        <div class="flex justify-between items-center mb-4">
            <h3 class="text-2xl font-bold">Finance Management</h3>
            <div class="flex space-x-2">
                <a href="{% url 'finance:deposit' %}" class="bg-green-600 text-white px-4 py-2 rounded">
                    <i class="fa-solid fa-arrow-circle-down mr-2"></i>Make Deposit
                </a>
                <a href="{% url 'finance:withdraw' %}" class="bg-blue-900 text-white px-4 py-2 rounded">
                    <i class="fa-solid fa-arrow-circle-up mr-2"></i>Make Withdrawal
                </a>
                <a href="{% url 'finance:history' %}" class="bg-purple-600 text-white px-4 py-2 rounded">
                    <i class="fa-solid fa-history mr-2"></i>Transaction History
                </a>
            </div>
        </div>

        <!-- Balance Overview -->
        <div class="grid grid-cols-1 md:grid-cols-3 gap-4 mb-6">
            <!-- Current Balance -->
            <div class="bg-white rounded-lg shadow-sm p-6 border-t-4 {% if balance >= 0 %}border-green-500{% else %}border-red-500{% endif %} transition-transform hover:translate-y-[-2px]">
                <div class="flex items-center justify-between">
                    <div>
                        <p class="text-sm font-medium text-gray-500">Current Balance</p>
                        <p class="text-3xl font-bold {% if balance >= 0 %}text-green-600{% else %}text-red-600{% endif %} mt-1">{{ balance|format_khr }}</p>
                    </div>
                    <div class="{% if balance >= 0 %}bg-green-100{% else %}bg-red-100{% endif %} p-3 rounded-full">
                        <i class="fa-solid fa-wallet {% if balance >= 0 %}text-green-600{% else %}text-red-600{% endif %} text-xl"></i>
                    </div>
                </div>
            </div>

            <!-- Month Deposits -->
            <div class="bg-white rounded-lg shadow-sm p-6 border-t-4 border-green-500 transition-transform hover:translate-y-[-2px]">
                <div class="flex items-center justify-between">
                    <div>
                        <p class="text-sm font-medium text-gray-500">This Month Deposits</p>
                        <p class="text-3xl font-bold text-green-600 mt-1">{{ month_deposits|format_khr }}</p>
                    </div>
                    <div class="bg-green-100 p-3 rounded-full">
                        <i class="fa-solid fa-arrow-circle-down text-green-600 text-xl"></i>
                    </div>
                </div>
            </div>

            <!-- Month Withdrawals -->
            <div class="bg-white rounded-lg shadow-sm p-6 border-t-4 border-blue-500 transition-transform hover:translate-y-[-2px]">
                <div class="flex items-center justify-between">
                    <div>
                        <p class="text-sm font-medium text-gray-500">This Month Withdrawals</p>
                        <p class="text-3xl font-bold text-blue-600 mt-1">{{ month_withdrawals|format_khr }}</p>
                    </div>
                    <div class="bg-blue-100 p-3 rounded-full">
                        <i class="fa-solid fa-arrow-circle-up text-blue-600 text-xl"></i>
                    </div>
                </div>
            </div>
        </div>

        <!-- Pending Approvals Section (only visible to admins/managers) -->
        {% if request.user.role == 'admin' or request.user.is_manager %}
            {% if pending_withdrawals > 0 %}
            <div class="bg-yellow-50 border-l-4 border-yellow-400 p-4 mb-6">
                <div class="flex items-center">
                    <div class="flex-shrink-0">
                        <i class="fas fa-exclamation-triangle text-yellow-400"></i>
                    </div>
                    <div class="ml-3">
                        <p class="text-sm text-yellow-700">
                            
                            There are {{ count }} pending withdrawal requests that need your approval.
                            
                        </p>
                        <a href="{% url 'finance:history' %}?status=pending" class="font-medium underline text-yellow-700 hover:text-yellow-600">
                            View pending requests
                        </a>
                    </div>
                </div>
            </div>
            {% endif %}
        {% endif %}

        <!-- Recent Transactions -->
        <div class="bg-white rounded-lg shadow-md overflow-hidden">
            <div class="p-4 bg-blue-900 text-white">
                <h4 class="text-lg font-semibold">Recent Transactions</h4>
            </div>
            <div class="overflow-x-auto">
                <table class="min-w-full divide-y divide-gray-200">
                    <thead class="bg-gray-50">
                        <tr>
                            <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                Transaction ID
                            </th>
                            <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                Type
                            </th>
                            <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                Amount
                            </th>
                            <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                Date
                            </th>
                            <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                Status
                            </th>
                            <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                Actions
                            </th>
                        </tr>
                    </thead>
                    <tbody class="bg-white divide-y divide-gray-200">
                        {% for transaction in recent_transactions %}
                        <tr>
                            <td class="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">
                                {{ transaction.transaction_id }}
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                                {% if transaction.transaction_type == 'deposit' %}
                                <span class="px-2 inline-flex text-xs leading-5 font-semibold rounded-full bg-green-100 text-green-800">
                                    Deposit
                                </span>
                                {% else %}
                                <span class="px-2 inline-flex text-xs leading-5 font-semibold rounded-full bg-blue-100 text-blue-800">
                                    Withdrawal
                                </span>
                                {% endif %}
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                                {{ transaction.amount_khr|format_khr }}
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                                {{ transaction.transaction_date|date:"d M Y H:i" }}
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                                {% if transaction.status == 'completed' %}
                                <span class="px-2 inline-flex text-xs leading-5 font-semibold rounded-full bg-green-100 text-green-800">
                                    Completed
                                </span>
                                {% elif transaction.status == 'pending' %}
                                <span class="px-2 inline-flex text-xs leading-5 font-semibold rounded-full bg-yellow-100 text-yellow-800">
                                    Pending
                                </span>
                                {% else %}
                                <span class="px-2 inline-flex text-xs leading-5 font-semibold rounded-full bg-red-100 text-red-800">
                                    Rejected
                                </span>
                                {% endif %}
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm font-medium">
                                {% if transaction.status == 'completed' %}
                                <a href="{% url 'finance:print_receipt' transaction.id %}" class="text-blue-600 hover:text-blue-900 mr-3">
                                    <i class="fas fa-print"></i> Print
                                </a>
                                {% endif %}

                                {% if transaction.status == 'pending' and transaction.transaction_type == 'withdrawal' and request.user.role == 'admin' or transaction.status == 'pending' and transaction.transaction_type == 'withdrawal' and request.user.is_manager %}
                                <button class="approve-btn text-green-600 hover:text-green-900 mr-3" data-id="{{ transaction.id }}">
                                    <i class="fas fa-check"></i> Approve
                                </button>
                                <button class="reject-btn text-red-600 hover:text-red-900" data-id="{{ transaction.id }}">
                                    <i class="fas fa-times"></i> Reject
                                </button>
                                {% endif %}
                            </td>
                        </tr>
                        {% empty %}
                        <tr>
                            <td colspan="6" class="px-6 py-4 text-center text-sm text-gray-500">
                                No transactions found
                            </td>
                        </tr>
                        {% endfor %}
                    </tbody>
                </table>
            </div>
            <div class="p-4 bg-gray-50 border-t border-gray-200">
                <a href="{% url 'finance:history' %}" class="text-blue-600 hover:text-blue-900">
                    View all transactions <i class="fas fa-arrow-right ml-1"></i>
                </a>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block js %}
<script>
    document.addEventListener('DOMContentLoaded', function() {
        // Approve transaction
        document.querySelectorAll('.approve-btn').forEach(button => {
            button.addEventListener('click', function() {
                const transactionId = this.getAttribute('data-id');
                if (confirm('Are you sure you want to approve this transaction?')) {
                    fetch(`/finance/api/approve-transaction/${transactionId}/`, {
                        method: 'POST',
                        headers: {
                            'X-CSRFToken': getCookie('csrftoken'),
                            'Content-Type': 'application/json'
                        }
                    })
                    .then(response => response.json())
                    .then(data => {
                        if (data.status === 'success') {
                            showNotification('success', data.message);
                            setTimeout(() => {
                                window.location.reload();
                            }, 1000);
                        } else {
                            showNotification('error', data.message);
                        }
                    })
                    .catch(error => {
                        showNotification('error', 'An error occurred while processing your request.');
                    });
                }
            });
        });

        // Reject transaction
        document.querySelectorAll('.reject-btn').forEach(button => {
            button.addEventListener('click', function() {
                const transactionId = this.getAttribute('data-id');
                if (confirm('Are you sure you want to reject this transaction?')) {
                    fetch(`/finance/api/reject-transaction/${transactionId}/`, {
                        method: 'POST',
                        headers: {
                            'X-CSRFToken': getCookie('csrftoken'),
                            'Content-Type': 'application/json'
                        }
                    })
                    .then(response => response.json())
                    .then(data => {
                        if (data.status === 'success') {
                            showNotification('success', data.message);
                            setTimeout(() => {
                                window.location.reload();
                            }, 1000);
                        } else {
                            showNotification('error', data.message);
                        }
                    })
                    .catch(error => {
                        showNotification('error', 'An error occurred while processing your request.');
                    });
                }
            });
        });

        // Helper function to get CSRF token
        function getCookie(name) {
            let cookieValue = null;
            if (document.cookie && document.cookie !== '') {
                const cookies = document.cookie.split(';');
                for (let i = 0; i < cookies.length; i++) {
                    const cookie = cookies[i].trim();
                    if (cookie.substring(0, name.length + 1) === (name + '=')) {
                        cookieValue = decodeURIComponent(cookie.substring(name.length + 1));
                        break;
                    }
                }
            }
            return cookieValue;
        }
    });
</script>
{% endblock %}
