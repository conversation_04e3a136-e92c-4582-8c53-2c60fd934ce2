# PowerShell script to install GNU gettext tools for Windows
# This is required for Django's translation functionality

# Define the download URL and destination
$gettextUrl = "https://github.com/mlocati/gettext-iconv-windows/releases/download/v0.21-v1.16/gettext0.21-iconv1.16-shared-64.exe"
$destination = "$env:TEMP\gettext-installer.exe"

Write-Host "Downloading GNU gettext tools..." -ForegroundColor Cyan
Invoke-WebRequest -Uri $gettextUrl -OutFile $destination

Write-Host "Installing GNU gettext tools..." -ForegroundColor Cyan
Write-Host "Please follow the installation wizard and select the option to add to PATH." -ForegroundColor Yellow
Start-Process -FilePath $destination -Wait

Write-Host "Installation completed. Please restart your terminal/IDE to apply PATH changes." -ForegroundColor Green
Write-Host "After restarting, you can run 'msgfmt --version' to verify the installation." -ForegroundColor Green
