{% extends 'base.html' %}
{% load custom_filters %}

{% block body %}
<div class="conponentSection p-5 bg-gray-200">
    <div class="componentWrapper">
        <!-- Header with navigation -->
        <div class="flex flex-col sm:flex-row sm:justify-between sm:items-center mb-6">
            <h3 class="text-2xl font-bold mb-3 sm:mb-0">Edit Bill</h3>
            <div class="flex space-x-2">
                <a href="{% url 'billmanagement:view_bill' bill.id %}" class="bg-blue-900 hover:bg-blue-950 text-white px-4 py-2 rounded transition-colors flex items-center">
                    <i class="fa-solid fa-arrow-left mr-2"></i>Back to Bill Details
                </a>
            </div>
        </div>

        <!-- Edit Form starts  -->
        <div class="formSection bg-white p-6 rounded-lg shadow-md mb-4">
            <div class="border-b pb-4 mb-6">
                <h3 class="text-xl font-bold text-gray-800 flex items-center">
                    <i class="fa-solid fa-edit mr-2 text-blue-900"></i>Edit Bill: {{ bill.bill_id }}
                </h3>
                <p class="text-sm text-gray-600 mt-1">Update the bill information</p>
            </div>

            <form method="post">
                {% csrf_token %}

                <!-- Basic Information Section -->
                <div class="mb-8">
                    <h4 class="text-md font-semibold text-gray-700 mb-4 border-l-4 border-blue-900 pl-3">Basic Information</h4>
                    <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-5">
                        <!-- Category -->
                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-2">Category<span class="text-red-600">*</span></label>
                            <select name="category" class="border w-full p-3 rounded-md bg-gray-50 focus:ring-2 focus:ring-blue-500 focus:border-blue-500" required>
                                {% for category_value, category_name in bill_categories %}
                                <option value="{{ category_value }}" {% if bill.category == category_value %}selected{% endif %}>{{ category_name }}</option>
                                {% endfor %}
                            </select>
                        </div>

                        <!-- Provider -->
                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-2">Provider/Vendor<span class="text-red-600">*</span></label>
                            <input type="text" name="provider" class="border w-full p-3 rounded-md bg-gray-50 focus:ring-2 focus:ring-blue-500 focus:border-blue-500" value="{{ bill.provider }}" required>
                        </div>

                        <!-- Month & Year -->
                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-2">Month & Year<span class="text-red-600">*</span></label>
                            <input type="month" name="month_year" class="border w-full p-3 rounded-md bg-gray-50 focus:ring-2 focus:ring-blue-500 focus:border-blue-500" value="{{ month_year }}" required>
                        </div>
                    </div>
                </div>

                <!-- Payment Details Section -->
                <div class="mb-8">
                    <h4 class="text-md font-semibold text-gray-700 mb-4 border-l-4 border-green-600 pl-3">Payment Details</h4>
                    <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-5">
                        <!-- Payment Period -->
                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-2">Payment Period<span class="text-red-600">*</span></label>
                            <select name="payment_period" class="border w-full p-3 rounded-md bg-gray-50 focus:ring-2 focus:ring-blue-500 focus:border-blue-500" required>
                                {% for period_value, period_name in payment_periods %}
                                <option value="{{ period_value }}" {% if bill.payment_period == period_value %}selected{% endif %}>{{ period_name }}</option>
                                {% endfor %}
                            </select>
                            <p class="text-xs text-gray-500 mt-1">How long this payment covers</p>
                        </div>

                        <!-- Amount (KHR) -->
                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-2">Amount (KHR)<span class="text-red-600">*</span></label>
                            <div class="relative">
                                <input type="number" name="amount_khr" class="border w-full p-3 rounded-md bg-gray-50 focus:ring-2 focus:ring-blue-500 focus:border-blue-500 pl-8" value="{{ bill.amount_khr }}" required>
                                <span class="absolute left-3 top-3 text-gray-500">៛</span>
                            </div>
                        </div>

                        <!-- Amount (USD) -->
                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-2">Amount (USD)</label>
                            <div class="relative">
                                <input type="number" name="amount_usd" step="0.01" class="border w-full p-3 rounded-md bg-gray-50 focus:ring-2 focus:ring-blue-500 focus:border-blue-500 pl-8" value="{{ bill.amount_usd|default:'' }}">
                                <span class="absolute left-3 top-3 text-gray-500">$</span>
                            </div>
                        </div>

                        <!-- Payment Method -->
                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-2">Payment Method<span class="text-red-600">*</span></label>
                            <select name="payment_method" class="border w-full p-3 rounded-md bg-gray-50 focus:ring-2 focus:ring-blue-500 focus:border-blue-500" required>
                                {% for method_value, method_name in payment_methods %}
                                <option value="{{ method_value }}" {% if bill.payment_method == method_value %}selected{% endif %}>{{ method_name }}</option>
                                {% endfor %}
                            </select>
                        </div>
                    </div>
                </div>

                <!-- Additional Information Section -->
                <div class="mb-8">
                    <h4 class="text-md font-semibold text-gray-700 mb-4 border-l-4 border-purple-600 pl-3">Additional Information</h4>

                    <!-- Description -->
                    <div class="mb-5">
                        <label class="block text-sm font-medium text-gray-700 mb-2">Description</label>
                        <textarea name="description" class="border w-full p-3 rounded-md bg-gray-50 focus:ring-2 focus:ring-blue-500 focus:border-blue-500" rows="2">{{ bill.description }}</textarea>
                    </div>

                    <!-- Notes -->
                    <div class="mb-5">
                        <label class="block text-sm font-medium text-gray-700 mb-2">Notes</label>
                        <textarea name="notes" class="border w-full p-3 rounded-md bg-gray-50 focus:ring-2 focus:ring-blue-500 focus:border-blue-500" rows="2">{{ bill.notes }}</textarea>
                    </div>

                    <!-- Recurring Bill -->
                    <div class="bg-blue-50 p-4 rounded-md border border-blue-200">
                        <div class="flex items-center">
                            <input type="checkbox" name="is_recurring" id="is_recurring" class="w-4 h-4 text-blue-600 bg-gray-100 border-gray-300 rounded focus:ring-blue-500" {% if bill.is_recurring %}checked{% endif %}>
                            <label for="is_recurring" class="ml-2 text-sm font-medium text-gray-900">Recurring Bill</label>
                        </div>
                        <p class="text-xs text-gray-600 mt-1 ml-6">A new bill will be created automatically for the next period when this bill is paid</p>
                    </div>
                </div>

                <div class="border-t pt-6 mt-6 flex flex-wrap gap-3">
                    <button type="submit" class="bg-blue-900 hover:bg-blue-950 text-white px-6 py-3 rounded-md font-semibold transition-colors flex items-center">
                        <i class="fa-solid fa-save mr-2"></i>Update Bill
                    </button>
                    <a href="{% url 'billmanagement:view_bill' bill.id %}" class="bg-gray-500 hover:bg-gray-600 text-white px-6 py-3 rounded-md font-semibold transition-colors">Cancel</a>
                </div>
            </form>
        </div>
        <!-- Edit Form ends  -->
    </div>
</div>
{% endblock %}
