from django import template
from decimal import Decimal
from django.core.cache import cache

register = template.Library()

def get_exchange_rate():
    """
    Get the current USD to KHR exchange rate from settings

    Returns:
        int: The exchange rate (e.g., 4000 for 1 USD = 4,000 KHR)
    """
    # Try to get from cache first
    exchange_rate = cache.get('exchange_rate_usd_to_khr')

    if exchange_rate is None:
        # If not in cache, get from database
        try:
            from settings.models import Settings
            settings = Settings.objects.first()
            if settings:
                exchange_rate = settings.exchange_rate_usd_to_khr
            else:
                exchange_rate = 4000  # Default if no settings exist
        except:
            exchange_rate = 4000  # Default fallback

        # Cache for 1 hour
        cache.set('exchange_rate_usd_to_khr', exchange_rate, 3600)

    return exchange_rate

@register.filter
def format_khr(value):
    """
    Format a number as Cambodian Riel (KHR) currency with thousand separators
    Example: 10000 -> 10,000៛
    """
    if value is None:
        return "0៛"

    try:
        # Handle string values with commas
        if isinstance(value, str):
            value = value.replace(',', '')

        # Convert to integer (remove decimal places)
        value_int = int(float(value))

        # Format with thousand separators
        formatted = "{:,}".format(value_int)

        # Add Riel symbol
        return f"{formatted}៛"
    except (ValueError, TypeError):
        return "0៛"

@register.filter
def format_usd(value):
    """
    Format a number as US Dollar (USD) currency with thousand separators
    Example: 10000 -> $10,000.00
    """
    if value is None:
        return "$0.00"

    try:
        # Handle string values with commas
        if isinstance(value, str):
            value = value.replace(',', '')

        # Convert to float and format with 2 decimal places and thousand separators
        value_float = float(value)
        formatted = "${:,.2f}".format(value_float)

        return formatted
    except (ValueError, TypeError):
        return "$0.00"

@register.filter
def format_number(value, decimals=0):
    """
    Format a number with thousand separators without any currency symbol
    Example: 10000 -> 10,000

    Args:
        value: The number to format
        decimals: Number of decimal places to include (default: 0)
    """
    if value is None:
        return "0"

    try:
        # Handle string values with commas
        if isinstance(value, str):
            value = value.replace(',', '')

        # Convert to float
        value_float = float(value)

        # Format with thousand separators and specified decimal places
        if decimals == 0:
            # For whole numbers, round to integer and format
            formatted = "{:,}".format(int(round(value_float)))
        else:
            # For decimal values, format with specified decimal places
            formatted = "{:,.{}f}".format(value_float, decimals)

        return formatted
    except (ValueError, TypeError):
        return "0"

@register.filter
def convert_usd_to_khr(usd_value):
    """
    Convert a USD value to KHR using the current exchange rate

    Args:
        usd_value: The USD value to convert

    Returns:
        int: The equivalent KHR value
    """
    if usd_value is None:
        return 0

    try:
        # Handle string values with commas
        if isinstance(usd_value, str):
            usd_value = usd_value.replace(',', '')

        # Convert to float
        usd_float = float(usd_value)

        # Get the exchange rate
        exchange_rate = get_exchange_rate()

        # Convert to KHR (round to nearest integer)
        khr_value = int(round(usd_float * exchange_rate))

        return khr_value
    except (ValueError, TypeError):
        return 0

@register.filter
def convert_khr_to_usd(khr_value):
    """
    Convert a KHR value to USD using the current exchange rate

    Args:
        khr_value: The KHR value to convert

    Returns:
        float: The equivalent USD value with 2 decimal places
    """
    if khr_value is None:
        return 0.0

    try:
        # Handle string values with commas
        if isinstance(khr_value, str):
            khr_value = khr_value.replace(',', '')

        # Convert to float
        khr_float = float(khr_value)

        # Get the exchange rate
        exchange_rate = get_exchange_rate()

        # Convert to USD (round to 2 decimal places)
        usd_value = round(khr_float / exchange_rate, 2)

        return usd_value
    except (ValueError, TypeError):
        return 0.0
