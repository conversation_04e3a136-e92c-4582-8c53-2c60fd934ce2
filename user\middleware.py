"""
Middleware for User Action Logging
Automatically logs authentication events and tracks user sessions
"""

from django.utils.deprecation import MiddlewareMixin
from django.contrib.auth.signals import user_logged_in, user_logged_out, user_login_failed
from django.dispatch import receiver
from .logging_utils import log_login_action, log_logout_action, log_failed_login


class UserActionLoggingMiddleware(MiddlewareMixin):
    """
    Middleware to track user actions and sessions
    """
    
    def process_request(self, request):
        """
        Process incoming requests to track user activity
        """
        # Store request start time for performance tracking
        import time
        request._action_log_start_time = time.time()
        
        return None
    
    def process_response(self, request, response):
        """
        Process responses to log any additional information
        """
        # Log suspicious activity patterns if needed
        if hasattr(request, 'user') and request.user.is_authenticated:
            # Check for suspicious patterns (this could be expanded)
            self._check_suspicious_activity(request, response)
        
        return response
    
    def _check_suspicious_activity(self, request, response):
        """
        Check for suspicious activity patterns
        """
        # This is a placeholder for future suspicious activity detection
        # Could include:
        # - Multiple failed attempts
        # - Unusual access patterns
        # - Large data exports
        # - Multiple deletions in short time
        pass


@receiver(user_logged_in)
def log_user_login(sender, request, user, **kwargs):
    """
    Signal receiver to log successful user logins
    """
    try:
        log_login_action(
            user=user,
            request=request,
            status='success',
            description=f"Successful login for user {user.username} (Role: {user.role})"
        )
    except Exception as e:
        # Don't let logging errors break the login process
        print(f"Error logging user login: {e}")


@receiver(user_logged_out)
def log_user_logout(sender, request, user, **kwargs):
    """
    Signal receiver to log user logouts
    """
    try:
        if user:  # user might be None in some cases
            log_logout_action(
                user=user,
                request=request,
                description=f"User {user.username} logged out"
            )
    except Exception as e:
        # Don't let logging errors break the logout process
        print(f"Error logging user logout: {e}")


@receiver(user_login_failed)
def log_user_login_failed(sender, credentials, request, **kwargs):
    """
    Signal receiver to log failed login attempts
    """
    try:
        username = credentials.get('username', 'Unknown')
        log_failed_login(
            username=username,
            request=request,
            description=f"Failed login attempt for username: {username}"
        )
    except Exception as e:
        # Don't let logging errors break the authentication process
        print(f"Error logging failed login: {e}")


class SecurityMonitoringMiddleware(MiddlewareMixin):
    """
    Middleware for advanced security monitoring
    """
    
    def process_request(self, request):
        """
        Monitor requests for security concerns
        """
        # Track IP addresses for potential blocking
        if hasattr(request, 'user') and request.user.is_authenticated:
            self._track_user_ip(request)
        
        return None
    
    def _track_user_ip(self, request):
        """
        Track user IP addresses for security monitoring
        """
        from .logging_utils import get_client_ip
        
        ip_address = get_client_ip(request)
        user = request.user
        
        # Store IP in session for tracking
        if 'user_ips' not in request.session:
            request.session['user_ips'] = []
        
        if ip_address not in request.session['user_ips']:
            request.session['user_ips'].append(ip_address)
            request.session.modified = True
            
            # Log IP change if user has multiple IPs
            if len(request.session['user_ips']) > 1:
                from .logging_utils import log_user_action
                log_user_action(
                    user=user,
                    action_type='other',
                    module='auth',
                    request=request,
                    description=f"User accessing from new IP address: {ip_address}",
                    additional_data={
                        'new_ip': ip_address,
                        'all_ips': request.session['user_ips']
                    }
                )


class SuspiciousActivityDetector:
    """
    Utility class for detecting suspicious activity patterns
    """
    
    @staticmethod
    def check_multiple_deletions(user, time_window_hours=1, threshold=5):
        """
        Check if user has performed multiple deletions in a short time
        """
        from django.utils import timezone
        from datetime import timedelta
        from .models import UserActionLog
        
        cutoff_time = timezone.now() - timedelta(hours=time_window_hours)
        
        deletion_count = UserActionLog.objects.filter(
            user=user,
            action_type__contains='delete',
            action_time__gte=cutoff_time
        ).count()
        
        return deletion_count >= threshold
    
    @staticmethod
    def check_large_financial_operations(user, amount_threshold=1000000):
        """
        Check if user has performed large financial operations
        """
        from django.utils import timezone
        from datetime import timedelta
        from .models import UserActionLog
        
        # Check last 24 hours
        cutoff_time = timezone.now() - timedelta(hours=24)
        
        large_operations = UserActionLog.objects.filter(
            user=user,
            action_time__gte=cutoff_time,
            financial_impact__isnull=False
        ).filter(
            financial_impact__gt=amount_threshold
        ).count()
        
        return large_operations > 0
    
    @staticmethod
    def check_off_hours_activity(user, start_hour=22, end_hour=6):
        """
        Check if user is active during off hours
        """
        from django.utils import timezone
        from datetime import timedelta
        from .models import UserActionLog
        
        # Check last 7 days
        cutoff_time = timezone.now() - timedelta(days=7)
        
        off_hours_activity = UserActionLog.objects.filter(
            user=user,
            action_time__gte=cutoff_time
        ).extra(
            where=["EXTRACT(hour FROM action_time) >= %s OR EXTRACT(hour FROM action_time) <= %s"],
            params=[start_hour, end_hour]
        ).count()
        
        return off_hours_activity > 0
    
    @staticmethod
    def get_user_risk_score(user):
        """
        Calculate a risk score for a user based on their recent activity
        """
        risk_score = 0
        
        # Multiple deletions (+30 points)
        if SuspiciousActivityDetector.check_multiple_deletions(user):
            risk_score += 30
        
        # Large financial operations (+20 points)
        if SuspiciousActivityDetector.check_large_financial_operations(user):
            risk_score += 20
        
        # Off hours activity (+10 points)
        if SuspiciousActivityDetector.check_off_hours_activity(user):
            risk_score += 10
        
        # Role-based adjustments
        if user.role in ['cashier', 'coach']:
            # Higher scrutiny for non-admin roles
            risk_score *= 1.5
        elif user.role == 'admin':
            # Lower base risk for admin users
            risk_score *= 0.8
        
        return min(100, int(risk_score))  # Cap at 100
