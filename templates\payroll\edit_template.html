{% extends 'base.html' %}
{% load custom_filters %}



{% block body %}
<div class="conponentSection p-5 bg-gray-200">
    <div class="componentWrapper">
        <!-- Header with navigation -->
        <div class="flex justify-between items-center mb-4">
            <h3 class="text-2xl font-bold">Edit Salary Slip Template</h3>
            <div class="flex space-x-2">
                <a href="{% url 'payroll:template_list' %}" class="bg-blue-900 text-white px-4 py-2 rounded">
                    <i class="fa-solid fa-arrow-left mr-2"></i>Back to Templates
                </a>
                <a href="{% url 'payroll:preview_template' template.id %}" class="bg-purple-600 text-white px-4 py-2 rounded">
                    <i class="fa-solid fa-eye mr-2"></i>Preview Template
                </a>
            </div>
        </div>

        <!-- Form starts  -->
        <div class="formSection bg-white p-6 rounded shadow-md mb-4">
            <form method="post" enctype="multipart/form-data" class="space-y-6">
                {% csrf_token %}
                
                <!-- Basic Information -->
                <div class="border-b pb-4 mb-4">
                    <h4 class="text-xl font-semibold mb-4">Basic Information</h4>
                    <div class="grid grid-cols-3 gap-4">
                        <div class="col-span-2">
                            <label class="block text-sm font-medium mb-1">Template Name*</label>
                            <input type="text" name="name" class="border w-full p-4 leading-tight bg-slate-100" 
                                   value="{{ template.name }}" required>
                        </div>
                        <div>
                            <label class="block text-sm font-medium mb-1">Language*</label>
                            <select name="language" class="border w-full p-4 leading-tight bg-slate-100" required>
                                {% for code, name in languages %}
                                <option value="{{ code }}" {% if template.language == code %}selected{% endif %}>{{ name }}</option>
                                {% endfor %}
                            </select>
                        </div>
                        <div class="col-span-3">
                            <label class="flex items-center">
                                <input type="checkbox" name="is_default" class="mr-2" {% if template.is_default %}checked{% endif %}>
                                <span>Set as Default Template</span>
                            </label>
                        </div>
                    </div>
                </div>
                
                <!-- Content -->
                <div class="border-b pb-4 mb-4">
                    <h4 class="text-xl font-semibold mb-4">Content</h4>
                    <div class="grid grid-cols-2 gap-4">
                        <div>
                            <label class="block text-sm font-medium mb-1">Header Text*</label>
                            <input type="text" name="header_text" class="border w-full p-4 leading-tight bg-slate-100" 
                                   value="{{ template.header_text }}" required>
                        </div>
                        <div>
                            <label class="block text-sm font-medium mb-1">Subheader Text*</label>
                            <input type="text" name="subheader_text" class="border w-full p-4 leading-tight bg-slate-100" 
                                   value="{{ template.subheader_text }}" required>
                        </div>
                        <div>
                            <label class="block text-sm font-medium mb-1">Footer Text*</label>
                            <input type="text" name="footer_text" class="border w-full p-4 leading-tight bg-slate-100" 
                                   value="{{ template.footer_text }}" required>
                        </div>
                        <div>
                            <label class="block text-sm font-medium mb-1">Company Logo</label>
                            {% if template.company_logo %}
                            <div class="mb-2">
                                <img src="{{ template.company_logo.url }}" alt="Current Logo" class="h-12">
                                <p class="text-sm text-gray-500">Current logo</p>
                            </div>
                            {% endif %}
                            <input type="file" name="company_logo" class="border w-full p-3 leading-tight bg-slate-100" 
                                   accept="image/*">
                        </div>
                    </div>
                </div>
                
                <!-- Styling -->
                <div class="border-b pb-4 mb-4">
                    <h4 class="text-xl font-semibold mb-4">Styling</h4>
                    <div class="grid grid-cols-3 gap-4">
                        <div>
                            <label class="block text-sm font-medium mb-1">Background Color*</label>
                            <input type="color" name="background_color" class="border w-full h-10 p-1 leading-tight bg-slate-100" 
                                   value="{{ template.background_color }}" required>
                        </div>
                        <div>
                            <label class="block text-sm font-medium mb-1">Text Color*</label>
                            <input type="color" name="text_color" class="border w-full h-10 p-1 leading-tight bg-slate-100" 
                                   value="{{ template.text_color }}" required>
                        </div>
                        <div>
                            <label class="block text-sm font-medium mb-1">Accent Color*</label>
                            <input type="color" name="accent_color" class="border w-full h-10 p-1 leading-tight bg-slate-100" 
                                   value="{{ template.accent_color }}" required>
                        </div>
                        <div class="col-span-3">
                            <label class="block text-sm font-medium mb-1">Custom CSS (Optional)</label>
                            <textarea name="custom_css" class="border w-full p-4 leading-tight bg-slate-100 font-mono" rows="4"
                                      placeholder="/* Add your custom CSS here */">{{ template.custom_css }}</textarea>
                        </div>
                    </div>
                </div>
                
                <!-- Display Options -->
                <div class="border-b pb-4 mb-4">
                    <h4 class="text-xl font-semibold mb-4">Display Options</h4>
                    <div class="grid grid-cols-2 gap-4">
                        <div>
                            <label class="flex items-center">
                                <input type="checkbox" name="show_company_info" class="mr-2" {% if template.show_company_info %}checked{% endif %}>
                                <span>Show Company Information</span>
                            </label>
                        </div>
                        <div>
                            <label class="flex items-center">
                                <input type="checkbox" name="show_signatures" class="mr-2" {% if template.show_signatures %}checked{% endif %}>
                                <span>Show Signature Lines</span>
                            </label>
                        </div>
                    </div>
                </div>
                
                <!-- Submit Button -->
                <div class="grid grid-cols-2 gap-4">
                    <button class="bg-blue-900 text-white font-bold py-2 px-4" type="submit">Update Template</button>
                    <a href="{% url 'payroll:template_list' %}" class="bg-gray-500 text-white font-bold py-2 px-4 text-center">Cancel</a>
                </div>
            </form>
        </div>
    </div>
</div>
{% endblock %}
