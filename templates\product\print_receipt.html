{% extends 'base.html' %}
{% load custom_filters %}



{% block head %}
<style>
    @media print {
        body * {
            visibility: hidden;
        }
        .print-section, .print-section * {
            visibility: visible;
        }
        .print-section {
            position: absolute;
            left: 0;
            top: 0;
            width: 100%;
        }
        .no-print {
            display: none;
        }
    }

    .receipt {
        width: 80mm;
        margin: 0 auto;
        padding: 10px;
        font-family: 'Courier New', monospace;
        font-size: 12px;
        line-height: 1.2;
    }

    .receipt-header {
        text-align: center;
        margin-bottom: 10px;
    }

    .receipt-header h1 {
        font-size: 18px;
        margin: 0;
    }

    .receipt-header p {
        margin: 5px 0;
    }

    .receipt-body {
        margin-bottom: 10px;
    }

    .receipt-table {
        width: 100%;
        border-collapse: collapse;
    }

    .receipt-table th, .receipt-table td {
        text-align: left;
        padding: 3px 0;
    }

    .receipt-table th:last-child, .receipt-table td:last-child {
        text-align: right;
    }

    .receipt-total {
        margin-top: 10px;
        text-align: right;
        font-weight: bold;
    }

    .receipt-footer {
        text-align: center;
        margin-top: 20px;
        font-size: 10px;
    }

    .dashed-line {
        border-top: 1px dashed #000;
        margin: 10px 0;
    }
</style>
{% endblock %}

{% block body %}
<div class="conponentSection p-5 bg-gray-200">
    <div class="componentWrapper">
        <div class="bg-white p-8 rounded shadow-md max-w-2xl mx-auto">
            <div class="print-section">
                <div class="receipt">
                    <!-- Receipt Header -->
                    <div class="receipt-header">
                        <h1>LEGEND FITNESS</h1>
                        <p>Official Receipt</p>
                        <p>{{ sale.date|date:"d-M-Y H:i" }}</p>
                        <p>Transaction ID: {{ sale.trxId }}</p>
                        <div class="dashed-line"></div>
                    </div>

                    <!-- Receipt Body -->
                    <div class="receipt-body">
                        <table class="receipt-table">
                            <thead><tr>
                                    <th>Item</th>
                                    <th>Qty</th>
                                    <th>Price</th>
                                    <th>Total</th>
                                </tr>
                            </thead>
                            <tbody>
                                {% for item in sale.items.all %}
                                <tr>
                                    <td>{{ item.product.name }}</td>
                                    <td>{{ item.quantity }}</td>
                                    <td>{{ item.price|format_khr }}</td>
                                    <td>{{ item.total_price|format_khr }}</td>
                                </tr>
                                {% endfor %}
                            </tbody>
                        </table>

                        <div class="dashed-line"></div>

                        <div class="receipt-total">
                            <p>Total: {{ sale.total_amount|format_khr }} (1$ = 4000៛)</p>
                            <p>Payment Method: {{ sale.get_payment_method_display }}</p>
                        </div>
                    </div>

                    <!-- Receipt Footer -->
                    <div class="receipt-footer">
                        <p>Thank you for your purchase!</p>
                        <p>ABA: 012 345 678 | Telegram: @LegendFitness</p>
                    </div>
                </div>
            </div>

            <!-- Print Button -->
            <div class="mt-6 text-center no-print">
                <button onclick="window.print()" class="bg-blue-900 text-white font-bold py-2 px-6 rounded">Print Receipt</button>
                <a href="{% url 'product:pos' %}" class="bg-gray-500 text-white font-bold py-2 px-6 rounded ml-2">Back to POS</a>
            </div>
        </div>
    </div>
</div>
{% endblock %}
