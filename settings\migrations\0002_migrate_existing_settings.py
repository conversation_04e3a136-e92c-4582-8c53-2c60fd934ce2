from django.db import migrations
from django.utils import timezone

def migrate_existing_settings(apps, schema_editor):
    """
    Migrate settings from existing models to the new centralized Settings model
    """
    # Get the models
    Settings = apps.get_model('settings', 'Settings')
    
    # Try to get PayPerVisitSettings
    try:
        PayPerVisitSettings = apps.get_model('paypervisit', 'PayPerVisitSettings')
        ppv_settings = PayPerVisitSettings.objects.first()
    except:
        ppv_settings = None
    
    # Try to get MetaData
    try:
        MetaData = apps.get_model('user', 'MetaData')
        metadata = MetaData.objects.last()
    except:
        metadata = None
    
    # Create or get the settings object
    settings, created = Settings.objects.get_or_create(
        id=1,
        defaults={
            'gym_name': 'Legend Fitness',
            'last_checked': timezone.now().date(),
            'created_at': timezone.now(),
            'last_updated': timezone.now(),
        }
    )
    
    # Update from PayPerVisitSettings if available
    if ppv_settings:
        settings.paypervisit_price_per_person = getattr(ppv_settings, 'price_per_person', 4000)
        settings.paypervisit_quick_select_1 = getattr(ppv_settings, 'quick_select_1', 2)
        settings.paypervisit_quick_select_2 = getattr(ppv_settings, 'quick_select_2', 5)
        settings.paypervisit_quick_select_3 = getattr(ppv_settings, 'quick_select_3', 10)
        settings.paypervisit_custom_price_1 = getattr(ppv_settings, 'custom_price_1', 8000)
        settings.paypervisit_custom_price_2 = getattr(ppv_settings, 'custom_price_2', 20000)
        settings.paypervisit_custom_price_3 = getattr(ppv_settings, 'custom_price_3', 40000)
    
    # Update from MetaData if available
    if metadata:
        settings.funds = getattr(metadata, 'funds', 0)
        settings.last_checked = getattr(metadata, 'lastChecked', timezone.now().date())
        settings.auto_deactivate_out_of_stock = getattr(metadata, 'auto_deactivate_out_of_stock', True)
        settings.auto_reactivate_in_stock = getattr(metadata, 'auto_reactivate_in_stock', True)
        settings.default_items_per_page = getattr(metadata, 'default_items_per_page', 10)
    
    # Save the settings
    settings.save()

class Migration(migrations.Migration):

    dependencies = [
        ('settings', '0001_initial'),
    ]

    operations = [
        migrations.RunPython(migrate_existing_settings),
    ]
