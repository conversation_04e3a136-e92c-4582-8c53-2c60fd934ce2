{% extends "../base.html" %}
{% load custom_filters %}



{% block body %}
<!-- component sections starts  -->
<div class="conponentSection p-5 bg-gray-200">
  <div class="componentWrapper">
    <!-- List section starts  -->
    <div class="bg-gray-50 p-4 shadow-xl rounded-md">
      <div class="relative overflow-x-auto h-[28rem]">
        <table class="w-full">
          <thead class="text-sm uppercase bg-blue-900 text-gray-50 text-center"><tr>
              <th scope="col" class="px-6 py-3">Member ID</th>
              <th scope="col" class="px-6 py-3">Name</th>
              <th scope="col" class="px-6 py-3">phone</th>
              <th scope="col" class="px-6 py-3">Package</th>
              <th scope="col" class="px-6 py-3">Join Date</th>
              <th scope="col" class="px-6 py-3">Status</th>
              <th scope="col" class="px-6 py-3">Due/Advanced</th>
              <th scope="col" class="px-6 py-3">View</th>
            </tr>
          </thead>
          <tbody>
            {% for member in members %}
            <tr class="bg-white border text-sm text-center">
              <td class="px-6 py-4">{{member.memberId}}</td>
              <td class="px-6 py-4">{{member.name}}</td>
              <td class="px-6 py-4">{{member.phone}}</td>
              <td class="px-6 py-4">{{member.package.name}}</td>
              <td class="px-6 py-4">{{member.join_date}}</td>
              <td class="px-6 py-4">{{member.status}}</td>
              <td class="px-6 py-4">
                {% if member.due_payment < 0 %}
                {{ member.due_payment|abs_val }} Taka Advanced
                  {% else %}
                  {{member.due_payment}} Taka Due
                {% endif %}
                </td>
              <td class="px-6 py-4"><a class="underline" href="{% url "member:edit" member.id %}">View</a></td>
            </tr>
            {% endfor %}
          </tbody>
        </table>
      </div>
    </div>
  </div>
</div>
{% endblock body %}
{% block js %}
<script>
  $("document").ready(() => {
    // Package Select
    $("#package").change(e => {
      let price = e.target.value;
      price = price.split(' ');
      price = price[2];
      $("#payable_amount").val(`${price}`);
      $("#amount").removeAttr("disabled");
      $("#amount").val("");

      document.querySelectorAll(".due_amount").forEach(item => {
        item.value = `${price}`;
      })
    });
    // Amount Change
    $("#amount").on('input', (e) => {
      let pa = $("#payable_amount").val();
      let a = e.target.value;
      let val = (Number(pa) - Number(a)) > 0 ? (Number(pa) - Number(a)) : 0;
      document.querySelectorAll(".due_amount").forEach(item => {
        item.value = `${val}`;
      })
    })
  })
</script>
{% endblock js %}