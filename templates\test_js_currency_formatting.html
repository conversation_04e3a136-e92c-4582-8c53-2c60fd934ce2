{% extends 'base.html' %}
{% load static %}

{% block title %}JavaScript Currency Formatting Tests{% endblock %}

{% block body %}
<div class="conponentSection p-5 sm:ml-64 bg-gray-100">
    <div class="componentWrapper max-w-7xl mx-auto">
        <div class="bg-white p-6 rounded-lg shadow-sm mb-6 border-l-4 border-blue-600">
            <h2 class="text-3xl font-bold text-gray-800 mb-4">JavaScript Currency Formatting Tests</h2>
            <p class="text-gray-600 mb-6">This page runs unit tests for the JavaScript currency formatting functions. Check the browser console for test results.</p>

            <div class="bg-gray-50 p-4 rounded-lg border border-gray-200">
                <h3 class="text-xl font-bold text-gray-800 mb-4">Test Results</h3>
                <p class="text-gray-600 mb-2">Open the browser console (F12) to see the test results.</p>
                <div id="test-results" class="mt-4 p-4 bg-gray-100 rounded">
                    <p class="text-gray-500">Test results will be displayed here...</p>
                </div>
                <button id="run-tests" class="mt-4 bg-blue-600 hover:bg-blue-700 text-white font-bold py-2 px-4 rounded">
                    Run Tests
                </button>
            </div>

            <div class="mt-6">
                <h3 class="text-xl font-bold text-gray-800 mb-4">Manual Testing</h3>
                <div class="grid grid-cols-1 md:grid-cols-3 gap-4">
                    <div class="bg-gray-50 p-4 rounded-lg border border-gray-200">
                        <h4 class="text-lg font-semibold text-gray-700 mb-2">KHR Formatting</h4>
                        <div class="mb-4">
                            <label class="block text-sm font-medium text-gray-700 mb-1">Input Value</label>
                            <input type="text" id="khr-input" class="border w-full p-2 rounded" placeholder="Enter a number">
                        </div>
                        <div class="mb-4">
                            <label class="block text-sm font-medium text-gray-700 mb-1">Formatted Result</label>
                            <div id="khr-result" class="p-2 bg-gray-100 rounded min-h-[2.5rem]"></div>
                        </div>
                        <button id="format-khr" class="bg-blue-600 hover:bg-blue-700 text-white font-bold py-2 px-4 rounded w-full">
                            Format KHR
                        </button>
                    </div>

                    <div class="bg-gray-50 p-4 rounded-lg border border-gray-200">
                        <h4 class="text-lg font-semibold text-gray-700 mb-2">USD Formatting</h4>
                        <div class="mb-4">
                            <label class="block text-sm font-medium text-gray-700 mb-1">Input Value</label>
                            <input type="text" id="usd-input" class="border w-full p-2 rounded" placeholder="Enter a number">
                        </div>
                        <div class="mb-4">
                            <label class="block text-sm font-medium text-gray-700 mb-1">Formatted Result</label>
                            <div id="usd-result" class="p-2 bg-gray-100 rounded min-h-[2.5rem]"></div>
                        </div>
                        <button id="format-usd" class="bg-blue-600 hover:bg-blue-700 text-white font-bold py-2 px-4 rounded w-full">
                            Format USD
                        </button>
                    </div>

                    <div class="bg-gray-50 p-4 rounded-lg border border-gray-200">
                        <h4 class="text-lg font-semibold text-gray-700 mb-2">Number Formatting</h4>
                        <div class="mb-4">
                            <label class="block text-sm font-medium text-gray-700 mb-1">Input Value</label>
                            <input type="text" id="number-input" class="border w-full p-2 rounded" placeholder="Enter a number">
                        </div>
                        <div class="mb-2">
                            <label class="block text-sm font-medium text-gray-700 mb-1">Decimal Places</label>
                            <input type="number" id="decimal-places" class="border w-full p-2 rounded" min="0" max="10" value="0">
                        </div>
                        <div class="mb-4">
                            <label class="block text-sm font-medium text-gray-700 mb-1">Formatted Result</label>
                            <div id="number-result" class="p-2 bg-gray-100 rounded min-h-[2.5rem]"></div>
                        </div>
                        <button id="format-number" class="bg-blue-600 hover:bg-blue-700 text-white font-bold py-2 px-4 rounded w-full">
                            Format Number
                        </button>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block js %}
<script src="{% static 'js/currency-formatter.js' %}"></script>
<script src="{% static 'js/tests/currency-formatter.test.js' %}"></script>
<script>
    document.addEventListener('DOMContentLoaded', function() {
        // Get elements
        const testResultsDiv = document.getElementById('test-results');
        const runTestsBtn = document.getElementById('run-tests');
        
        const khrInput = document.getElementById('khr-input');
        const khrResult = document.getElementById('khr-result');
        const formatKhrBtn = document.getElementById('format-khr');
        
        const usdInput = document.getElementById('usd-input');
        const usdResult = document.getElementById('usd-result');
        const formatUsdBtn = document.getElementById('format-usd');
        
        const numberInput = document.getElementById('number-input');
        const decimalPlaces = document.getElementById('decimal-places');
        const numberResult = document.getElementById('number-result');
        const formatNumberBtn = document.getElementById('format-number');
        
        // Function to capture console output
        const originalConsoleLog = console.log;
        const originalConsoleAssert = console.assert;
        const originalConsoleGroup = console.group;
        const originalConsoleGroupEnd = console.groupEnd;
        
        let testOutput = [];
        let testPassed = true;
        
        function captureConsole() {
            testOutput = [];
            testPassed = true;
            
            console.log = function() {
                const args = Array.from(arguments);
                testOutput.push(args.join(' '));
                originalConsoleLog.apply(console, arguments);
            };
            
            console.assert = function(condition, message) {
                if (!condition) {
                    testPassed = false;
                    testOutput.push(`❌ FAILED: ${message}`);
                } else {
                    testOutput.push(`✅ PASSED: ${message}`);
                }
                originalConsoleAssert.apply(console, arguments);
            };
            
            console.group = function(label) {
                testOutput.push(`<strong>${label}</strong>`);
                originalConsoleGroup.apply(console, arguments);
            };
            
            console.groupEnd = function() {
                testOutput.push('<hr>');
                originalConsoleGroupEnd.apply(console);
            };
        }
        
        function restoreConsole() {
            console.log = originalConsoleLog;
            console.assert = originalConsoleAssert;
            console.group = originalConsoleGroup;
            console.groupEnd = originalConsoleGroupEnd;
        }
        
        // Run tests button
        runTestsBtn.addEventListener('click', function() {
            captureConsole();
            runAllTests();
            restoreConsole();
            
            // Display test results
            testResultsDiv.innerHTML = `
                <div class="${testPassed ? 'bg-green-100 border-green-500' : 'bg-red-100 border-red-500'} border-l-4 p-4 mb-4">
                    <p class="${testPassed ? 'text-green-700' : 'text-red-700'} font-bold">
                        ${testPassed ? '✅ All tests passed!' : '❌ Some tests failed!'}
                    </p>
                </div>
                <div class="whitespace-pre-line">
                    ${testOutput.join('<br>')}
                </div>
            `;
        });
        
        // Format KHR button
        formatKhrBtn.addEventListener('click', function() {
            const value = khrInput.value;
            const formatted = formatKHR(value);
            khrResult.textContent = formatted;
        });
        
        // Format USD button
        formatUsdBtn.addEventListener('click', function() {
            const value = usdInput.value;
            const formatted = formatUSD(value);
            usdResult.textContent = formatted;
        });
        
        // Format Number button
        formatNumberBtn.addEventListener('click', function() {
            const value = numberInput.value;
            const decimals = parseInt(decimalPlaces.value) || 0;
            const formatted = formatNumber(value, decimals);
            numberResult.textContent = formatted;
        });
    });
</script>
{% endblock %}
