#!/usr/bin/env python
"""
<PERSON><PERSON><PERSON> to make and compile translations for Legend Fitness Club project.
This script automates the process of creating and compiling translation files.
"""

import os
import sys
import subprocess
import argparse
from pathlib import Path

def run_command(command, cwd=None):
    """Run a shell command and print its output."""
    print(f"Running: {' '.join(command)}")
    try:
        result = subprocess.run(
            command,
            cwd=cwd,
            check=True,
            stdout=subprocess.PIPE,
            stderr=subprocess.PIPE,
            text=True
        )
        print(result.stdout)
        return True
    except subprocess.CalledProcessError as e:
        print(f"Error: {e}")
        print(e.stderr)
        return False

def make_messages(locale=None):
    """Create or update .po files for translation."""
    command = [sys.executable, "manage.py", "makemessages", "--ignore=env/*", "--ignore=venv/*"]
    
    if locale:
        command.extend(["-l", locale])
    else:
        # If no locale specified, create for all configured locales
        command.extend(["-a"])
    
    return run_command(command)

def compile_messages():
    """Compile .po files to .mo files."""
    command = [sys.executable, "manage.py", "compilemessages", "--ignore=env/*", "--ignore=venv/*"]
    return run_command(command)

def main():
    parser = argparse.ArgumentParser(description="Make and compile translations for Legend Fitness Club")
    parser.add_argument("--make", action="store_true", help="Make translation files (.po)")
    parser.add_argument("--compile", action="store_true", help="Compile translation files (.mo)")
    parser.add_argument("--locale", help="Locale to process (e.g., 'km' for Khmer)")
    parser.add_argument("--all", action="store_true", help="Process all steps (make and compile)")
    
    args = parser.parse_args()
    
    # Default to --all if no specific action is provided
    if not (args.make or args.compile):
        args.all = True
    
    if args.all or args.make:
        print("=== Making translation files ===")
        if not make_messages(args.locale):
            print("Failed to make translation files")
            return 1
    
    if args.all or args.compile:
        print("=== Compiling translation files ===")
        if not compile_messages():
            print("Failed to compile translation files")
            return 1
    
    print("Translation process completed successfully!")
    return 0

if __name__ == "__main__":
    sys.exit(main())
