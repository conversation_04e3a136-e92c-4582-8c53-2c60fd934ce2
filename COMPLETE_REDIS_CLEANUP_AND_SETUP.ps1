# Complete Redis Cleanup and 7.4.3 Setup Script
# Run this as Administrator in PowerShell

Write-Host "=" * 70 -ForegroundColor Green
Write-Host "Complete Redis Cleanup and 7.4.3 Setup for Legend Fitness Club" -ForegroundColor Green
Write-Host "=" * 70 -ForegroundColor Green

# Check if running as administrator
if (-NOT ([Security.Principal.WindowsPrincipal] [Security.Principal.WindowsIdentity]::GetCurrent()).IsInRole([Security.Principal.WindowsBuiltInRole] "Administrator")) {
    Write-Host "ERROR: This script must be run as Administrator!" -ForegroundColor Red
    Write-Host "Please right-click PowerShell and select 'Run as Administrator'" -ForegroundColor Yellow
    Read-Host "Press Enter to exit"
    exit 1
}

Write-Host "✓ Running as Administrator - proceeding with cleanup and setup..." -ForegroundColor Green
Write-Host ""

# Step 1: Kill all existing Redis processes
Write-Host "Step 1: Killing all existing Redis processes..." -ForegroundColor Yellow
try {
    $redisProcesses = Get-Process -Name "redis-server" -ErrorAction SilentlyContinue
    if ($redisProcesses) {
        Write-Host "Found Redis processes:" -ForegroundColor Cyan
        $redisProcesses | ForEach-Object { Write-Host "  PID: $($_.Id) - $($_.ProcessName)" -ForegroundColor White }

        $redisProcesses | Stop-Process -Force
        Write-Host "✓ All Redis processes terminated" -ForegroundColor Green
    } else {
        Write-Host "✓ No Redis processes found running" -ForegroundColor Green
    }
} catch {
    Write-Host "⚠ Error killing Redis processes: $($_.Exception.Message)" -ForegroundColor Yellow
}

Write-Host ""

# Step 2: Check what processes are using port 6379
Write-Host "Step 2: Checking what processes are using port 6379..." -ForegroundColor Yellow
try {
    $port6379 = netstat -ano | Select-String ":6379"
    if ($port6379) {
        Write-Host "Processes using port 6379:" -ForegroundColor Cyan
        $port6379 | ForEach-Object {
            Write-Host "  $_" -ForegroundColor White
            # Extract PID and kill if it's a Redis process
            if ($_ -match "\s+(\d+)$") {
                $processId = $matches[1]
                try {
                    $process = Get-Process -Id $processId -ErrorAction SilentlyContinue
                    if ($process -and $process.ProcessName -like "*redis*") {
                        Write-Host "  Killing Redis process PID $processId" -ForegroundColor Yellow
                        Stop-Process -Id $processId -Force
                    }
                } catch {
                    Write-Host "  Could not kill process PID $processId" -ForegroundColor Yellow
                }
            }
        }
    } else {
        Write-Host "✓ Port 6379 is free" -ForegroundColor Green
    }
} catch {
    Write-Host "⚠ Error checking port 6379: $($_.Exception.Message)" -ForegroundColor Yellow
}

Write-Host ""

# Step 3: Remove any Redis services
Write-Host "Step 3: Removing Redis services..." -ForegroundColor Yellow
try {
    # Try to stop and delete Redis service
    $redisService = Get-Service -Name "redis" -ErrorAction SilentlyContinue
    if ($redisService) {
        Write-Host "Found Redis service: $($redisService.Status)" -ForegroundColor Cyan

        if ($redisService.Status -eq "Running") {
            Stop-Service -Name "redis" -Force
            Write-Host "✓ Redis service stopped" -ForegroundColor Green
        }

        # Delete the service
        & sc.exe delete redis
        Write-Host "✓ Redis service deleted" -ForegroundColor Green
    } else {
        Write-Host "✓ No Redis service found" -ForegroundColor Green
    }

    # Check for any other Redis services
    $allServices = Get-Service | Where-Object { $_.Name -like "*redis*" }
    if ($allServices) {
        Write-Host "Other Redis services found:" -ForegroundColor Cyan
        $allServices | ForEach-Object {
            Write-Host "  $($_.Name) - $($_.Status)" -ForegroundColor White
            try {
                Stop-Service -Name $_.Name -Force -ErrorAction SilentlyContinue
                & sc.exe delete $_.Name
                Write-Host "  ✓ Deleted service: $($_.Name)" -ForegroundColor Green
            } catch {
                Write-Host "  ⚠ Could not delete service: $($_.Name)" -ForegroundColor Yellow
            }
        }
    }
} catch {
    Write-Host "⚠ Error managing Redis services: $($_.Exception.Message)" -ForegroundColor Yellow
}

Write-Host ""

# Step 4: Locate existing Redis installations
Write-Host "Step 4: Locating existing Redis installations..." -ForegroundColor Yellow
try {
    # Check common Redis installation paths
    $commonPaths = @(
        "C:\Program Files\Redis",
        "C:\Redis",
        "C:\Program Files (x86)\Redis"
    )

    foreach ($path in $commonPaths) {
        if (Test-Path $path) {
            Write-Host "Found Redis installation: $path" -ForegroundColor Cyan
            $files = Get-ChildItem $path -Filter "redis-*.exe" -ErrorAction SilentlyContinue
            if ($files) {
                $files | ForEach-Object { Write-Host "  $($_.Name)" -ForegroundColor White }
            }
        }
    }

    # Try to find Redis executables in PATH
    $redisServer = Get-Command "redis-server" -ErrorAction SilentlyContinue
    if ($redisServer) {
        Write-Host "Redis server found in PATH: $($redisServer.Source)" -ForegroundColor Cyan
    }

    $redisCli = Get-Command "redis-cli" -ErrorAction SilentlyContinue
    if ($redisCli) {
        Write-Host "Redis CLI found in PATH: $($redisCli.Source)" -ForegroundColor Cyan
    }

} catch {
    Write-Host "⚠ Error locating Redis installations: $($_.Exception.Message)" -ForegroundColor Yellow
}

Write-Host ""

# Step 5: Verify port 6379 is completely free
Write-Host "Step 5: Verifying port 6379 is completely free..." -ForegroundColor Yellow
try {
    $port6379Final = netstat -an | Select-String ":6379"
    if ($port6379Final) {
        Write-Host "⚠ Port 6379 still has connections:" -ForegroundColor Yellow
        $port6379Final | ForEach-Object { Write-Host "  $_" -ForegroundColor White }
    } else {
        Write-Host "✓ Port 6379 is completely free" -ForegroundColor Green
    }
} catch {
    Write-Host "⚠ Error checking port 6379: $($_.Exception.Message)" -ForegroundColor Yellow
}

Write-Host ""

# Step 6: Verify Redis 7.4.3 installation
Write-Host "Step 6: Verifying Redis 7.4.3 installation..." -ForegroundColor Yellow
$redis743Path = "C:\Redis-7.4.3"

if (Test-Path $redis743Path) {
    Write-Host "✓ Redis 7.4.3 directory found: $redis743Path" -ForegroundColor Green

    $requiredFiles = @("redis-server.exe", "redis-cli.exe")
    $allFilesFound = $true

    foreach ($file in $requiredFiles) {
        if (Test-Path "$redis743Path\$file") {
            Write-Host "✓ Found: $file" -ForegroundColor Green
        } else {
            Write-Host "✗ Missing: $file" -ForegroundColor Red
            $allFilesFound = $false
        }
    }

    if (-not $allFilesFound) {
        Write-Host "ERROR: Required Redis 7.4.3 files are missing!" -ForegroundColor Red
        Read-Host "Press Enter to exit"
        exit 1
    }
} else {
    Write-Host "ERROR: Redis 7.4.3 directory not found at $redis743Path" -ForegroundColor Red
    Read-Host "Press Enter to exit"
    exit 1
}

Write-Host ""

# Step 7: Create Redis 7.4.3 configuration
Write-Host "Step 7: Creating Redis 7.4.3 configuration..." -ForegroundColor Yellow
$configPath = "$redis743Path\redis.windows-service.conf"

$redisConfig = @"
# Redis 7.4.3 Configuration for Legend Fitness Club
port 6379
bind 127.0.0.1
timeout 0
tcp-keepalive 60
tcp-backlog 511
loglevel notice
logfile "redis-server.log"
databases 16
save 900 1
save 300 10
save 60 10000
stop-writes-on-bgsave-error yes
rdbcompression yes
rdbchecksum yes
dbfilename dump.rdb
dir ./
maxmemory 1gb
maxmemory-policy allkeys-lru
tcp-keepalive 300
notify-keyspace-events Ex
lazyfree-lazy-eviction yes
lazyfree-lazy-expire yes
lazyfree-lazy-server-del yes
replica-lazy-flush yes
"@

try {
    $redisConfig | Out-File -FilePath $configPath -Encoding UTF8
    Write-Host "Redis 7.4.3 configuration created: $configPath" -ForegroundColor Green
} catch {
    Write-Host "Error creating configuration: $($_.Exception.Message)" -ForegroundColor Red
}

Write-Host ""

# Step 8: Start Redis 7.4.3 server
Write-Host "Step 8: Starting Redis 7.4.3 server..." -ForegroundColor Yellow
Set-Location $redis743Path

try {
    Write-Host "Starting Redis 7.4.3 server..." -ForegroundColor Cyan
    Write-Host "Command: .\redis-server.exe redis.windows-service.conf" -ForegroundColor White

    # Start Redis 7.4.3 in background
    $redisProcess = Start-Process -FilePath ".\redis-server.exe" -ArgumentList "redis.windows-service.conf" -PassThru -WindowStyle Normal

    if ($redisProcess) {
        Write-Host "✓ Redis 7.4.3 server started (PID: $($redisProcess.Id))" -ForegroundColor Green
        Write-Host "Waiting for Redis 7.4.3 to initialize..." -ForegroundColor Cyan
        Start-Sleep -Seconds 5
    } else {
        Write-Host "✗ Failed to start Redis 7.4.3 server" -ForegroundColor Red
    }
} catch {
    Write-Host "✗ Error starting Redis 7.4.3: $($_.Exception.Message)" -ForegroundColor Red
}

Write-Host ""

# Step 9: Test Redis 7.4.3 connectivity
Write-Host "Step 9: Testing Redis 7.4.3 connectivity..." -ForegroundColor Yellow

try {
    # Test ping
    $pingResult = & .\redis-cli.exe ping 2>$null
    if ($pingResult -eq "PONG") {
        Write-Host "✓ Redis 7.4.3 is responding to ping!" -ForegroundColor Green

        # Get version
        $versionInfo = & .\redis-cli.exe info server 2>$null | Select-String "redis_version"
        if ($versionInfo) {
            Write-Host "✓ $versionInfo" -ForegroundColor Green

            if ($versionInfo -match "7\.4\.3") {
                Write-Host "✓ CONFIRMED: Redis 7.4.3 is running correctly!" -ForegroundColor Green
            } else {
                Write-Host "⚠ Warning: Version mismatch - expected 7.4.3" -ForegroundColor Yellow
                Write-Host "  Actual version: $versionInfo" -ForegroundColor Yellow
            }
        }

        # Test basic operations
        & .\redis-cli.exe set test "Redis 7.4.3 Working" >$null 2>&1
        $testResult = & .\redis-cli.exe get test 2>$null
        & .\redis-cli.exe del test >$null 2>&1

        if ($testResult -eq "Redis 7.4.3 Working") {
            Write-Host "✓ Redis 7.4.3 basic operations working!" -ForegroundColor Green
        }

    } else {
        Write-Host "✗ Redis 7.4.3 is not responding to ping" -ForegroundColor Red
        Write-Host "Ping result: $pingResult" -ForegroundColor Yellow
    }
} catch {
    Write-Host "✗ Error testing Redis 7.4.3: $($_.Exception.Message)" -ForegroundColor Red
}

Write-Host ""

# Step 10: Test Django integration
Write-Host "Step 10: Testing Django integration..." -ForegroundColor Yellow
Set-Location "c:\Final Project\legend_fitness_club-gym-ms"

try {
    Write-Host "Running Django Redis test..." -ForegroundColor Cyan
    $djangoTest = & python manage.py test_redis --detailed 2>&1

    if ($djangoTest) {
        Write-Host "Django Redis Test Results:" -ForegroundColor Cyan
        $djangoTest | ForEach-Object {
            if ($_ -match "Redis 7\.4\.3 detected") {
                Write-Host "✓ $_" -ForegroundColor Green
            } elseif ($_ -match "channels_redis\.core\.RedisChannelLayer") {
                Write-Host "✓ $_" -ForegroundColor Green
            } elseif ($_ -match "✓") {
                Write-Host "$_" -ForegroundColor Green
            } elseif ($_ -match "⚠") {
                Write-Host "$_" -ForegroundColor Yellow
            } else {
                Write-Host "$_" -ForegroundColor White
            }
        }
    }
} catch {
    Write-Host "✗ Error testing Django integration: $($_.Exception.Message)" -ForegroundColor Red
}

Write-Host ""

# Final summary
Write-Host "=" * 70 -ForegroundColor Green
Write-Host "Redis Cleanup and 7.4.3 Setup Complete!" -ForegroundColor Green
Write-Host "=" * 70 -ForegroundColor Green

Write-Host "Next Steps:" -ForegroundColor Cyan
Write-Host "1. Start Django server: python manage.py runserver 8000" -ForegroundColor White
Write-Host "2. Check for Redis 7.4.3 detection in startup logs" -ForegroundColor White
Write-Host "3. Test WebSocket functionality in browser" -ForegroundColor White
Write-Host "4. Verify real-time permission updates work" -ForegroundColor White

Write-Host ""
Write-Host "Expected Django startup messages:" -ForegroundColor Cyan
Write-Host "✓ Redis 7.4.3 detected - using Redis channel layer" -ForegroundColor Green
Write-Host "✓ Redis detected - using Redis cache backend" -ForegroundColor Green

Read-Host "`nPress Enter to exit"
