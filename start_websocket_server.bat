@echo off
echo Starting Legend Fitness Club with WebSocket Support...
echo.

REM Check if virtual environment is activated
if not defined VIRTUAL_ENV (
    echo Activating virtual environment...
    call venv\Scripts\activate.bat
    if %errorlevel% neq 0 (
        echo Failed to activate virtual environment. Please ensure venv exists.
        pause
        exit /b 1
    )
)

echo Checking Django configuration...
python manage.py check
if %errorlevel% neq 0 (
    echo Django configuration check failed. Please fix the errors above.
    pause
    exit /b 1
)

echo.
echo Starting Daphne ASGI server with WebSocket support...
echo Server will be available at: http://127.0.0.1:8000/
echo WebSocket endpoints:
echo   - ws://127.0.0.1:8000/ws/permissions/
echo   - ws://127.0.0.1:8000/ws/notifications/
echo.
echo Press Ctrl+C to stop the server
echo.

REM Set Django settings module
set DJANGO_SETTINGS_MODULE=core.settings

REM Start Daphne server
daphne -p 8000 core.asgi:application

pause
