# Manual Redis Setup for Windows

## Quick Redis Installation

### Step 1: Download Redis
1. Go to: https://github.com/microsoftarchive/redis/releases/tag/win-3.0.504
2. Download: `Redis-x64-3.0.504.zip`
3. Extract to: `C:\Redis\`

### Step 2: Start Redis Server
1. Open Command Prompt as Administrator
2. Navigate to Redis directory:
   ```cmd
   cd C:\Redis
   ```
3. Start Redis server:
   ```cmd
   redis-server.exe
   ```

### Step 3: Test Redis (in another Command Prompt)
```cmd
cd C:\Redis
redis-cli.exe ping
```
Expected response: `PONG`

## Alternative: Using Docker

If you have Docker installed:
```cmd
docker run -d -p 6379:6379 --name redis redis:alpine
```

## Alternative: Using WSL

If you have Windows Subsystem for Linux:
```bash
# Install WSL first if not available
wsl --install

# Then in WSL terminal:
sudo apt update
sudo apt install redis-server
sudo service redis-server start

# Test
redis-cli ping
```

## Verification Commands

After Red<PERSON> is running, test with our Django command:
```cmd
cd "c:\Final Project\legend_fitness_club-gym-ms"
python manage.py test_redis --detailed
```

Expected output should show:
- ✓ Redis is available and responding
- ✓ Cache Backend: django_redis.cache.RedisCache
- ✓ Channel Backend: channels_redis.core.RedisChannelLayer

## Troubleshooting

### Port Already in Use
```cmd
netstat -an | findstr :6379
taskkill /f /im redis-server.exe
```

### Permission Issues
- Run Command Prompt as Administrator
- Check Windows Firewall settings

### Connection Issues
- Ensure Redis is running on 127.0.0.1:6379
- Check if antivirus is blocking Redis
- Verify no other service is using port 6379
