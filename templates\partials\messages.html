{% if messages %}


<!-- Hide the original messages container - we'll use notifications instead -->
<div class="messages-container mb-4" style="display: none;">
    {% for message in messages %}
        <div class="{% if message.tags == 'error' %}bg-red-100 border-l-4 border-red-500 text-red-700{% else %}bg-green-100 border-l-4 border-green-500 text-green-700{% endif %} p-4 mb-2 rounded shadow">
            {{ message }}
        </div>
    {% endfor %}
</div>

<!-- Convert Django messages to notifications -->
<script>
    document.addEventListener('DOMContentLoaded', function() {
        {% for message in messages %}
            {% if 'error' in message.tags %}
                showNotification('error', 'Error', '{{ message|escapejs }}', 7000);
            {% elif 'success' in message.tags %}
                showNotification('success', 'Success', '{{ message|escapejs }}', 7000);
            {% elif 'warning' in message.tags %}
                showNotification('warning', 'Warning', '{{ message|escapejs }}', 7000);
            {% else %}
                showNotification('info', 'Information', '{{ message|escapejs }}', 7000);
            {% endif %}
        {% endfor %}
    });
</script>
{% endif %}
