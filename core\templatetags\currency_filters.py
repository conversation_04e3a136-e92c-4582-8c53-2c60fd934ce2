from django import template
from decimal import Decimal
from core.templatetags.currency_formatters import format_khr as format_khr_with_separators
from core.templatetags.currency_formatters import format_usd as format_usd_with_separators
from core.templatetags.currency_formatters import format_number

register = template.Library()

@register.filter
def format_khr(value):
    """
    Format a number as KHR currency with thousand separators
    """
    return format_khr_with_separators(value)

@register.filter
def format_usd(value):
    """
    Format a number as USD currency with thousand separators
    """
    return format_usd_with_separators(value)

@register.filter
def format_number_with_commas(value):
    """
    Format a number with thousand separators without any currency symbol
    """
    return format_number(value)

@register.filter
def multiply(value, arg):
    """
    Multiply the value by the argument
    """
    try:
        return int(float(value)) * int(float(arg))
    except (ValueError, TypeError):
        return 0
