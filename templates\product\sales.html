{% extends "../base.html" %}
{% load custom_filters %}
{% load permission_tags %}
{% load currency_filters %}

{% block extra_css %}
<style>
    /* Responsive table improvements */
    @media (max-width: 768px) {
        .sales-table th:nth-child(2),
        .sales-table td:nth-child(2) {
            display: none; /* Hide Date column on mobile */
        }

        .sales-table th:nth-child(4),
        .sales-table td:nth-child(4) {
            display: none; /* Hide Payment Method column on mobile */
        }

        .delete-sale-btn {
            padding: 0.25rem 0.5rem !important;
            font-size: 0.625rem !important;
        }

        .delete-sale-btn i {
            margin-right: 0 !important;
        }

        .delete-sale-btn .btn-text {
            display: none;
        }
    }

    @media (max-width: 640px) {
        .sales-table th:nth-child(5),
        .sales-table td:nth-child(5) {
            display: none; /* Hide Sold By column on small mobile */
        }
    }
</style>
{% endblock %}

{% block body %}
<!-- Hidden CSRF token for JavaScript -->
<input type="hidden" name="csrfmiddlewaretoken" value="{{ csrf_token }}">

<div class="conponentSection p-5 bg-gray-200">
    <div class="componentWrapper">
        <!-- Sales Overview -->
        <div class="bg-white p-4 rounded shadow-md mb-4">

            <div class="flex justify-between items-center mb-4">
                <div class="flex items-center">
                    <a href="{% url 'product:pos' %}" class="bg-blue-900 text-white p-2 rounded-full hover:bg-blue-800 mr-3">
                        <i class="fas fa-arrow-left"></i>
                    </a>
                    <h3 class="text-2xl font-bold">Sales History</h3>
                </div>

                <!-- Filter Form -->
                <form method="get" class="flex flex-col md:flex-row items-start md:items-end space-y-2 md:space-y-0 md:space-x-2">
                    <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-2">
                        <!-- Date Filters -->
                        <div>
                            <label for="start_date" class="block text-sm font-medium mb-1">From:</label>
                            <input
                                type="date"
                                id="start_date"
                                name="start_date"
                                class="border rounded p-1 text-sm w-full"
                                value="{{ start_date }}"
                                required
                            >
                        </div>
                        <div>
                            <label for="end_date" class="block text-sm font-medium mb-1">To:</label>
                            <input
                                type="date"
                                id="end_date"
                                name="end_date"
                                class="border rounded p-1 text-sm w-full"
                                value="{{ end_date }}"
                                required
                            >
                        </div>

                        <!-- Payment Method Filter -->
                        <div>
                            <label for="payment_method" class="block text-sm font-medium mb-1">Payment Method:</label>
                            <select
                                id="payment_method"
                                name="payment_method"
                                class="border rounded p-1 text-sm w-full"
                            >
                                <option value="">All Methods</option>
                                {% for value, display in payment_methods %}
                                <option value="{{ value }}" {% if payment_method_filter == value %}selected{% endif %}>{{ display }}</option>
                                {% endfor %}
                            </select>
                        </div>

                        <!-- Cashier Filter -->
                        <div>
                            <label for="cashier" class="block text-sm font-medium mb-1">Cashier:</label>
                            <select
                                id="cashier"
                                name="cashier"
                                class="border rounded p-1 text-sm w-full"
                            >
                                <option value="">All Cashiers</option>
                                {% for cashier in cashiers %}
                                <option value="{{ cashier.id }}" {% if cashier_filter == cashier.id|stringformat:"s" %}selected{% endif %}>{{ cashier.username }}</option>
                                {% endfor %}
                            </select>
                        </div>
                    </div>

                    <!-- Filter Buttons -->
                    <div class="flex space-x-2">
                        <button type="submit" class="bg-blue-900 text-white px-3 py-1 rounded text-sm">
                            <i class="fas fa-filter mr-1"></i> Filter
                        </button>
                        {% if filter_active %}
                        <a href="{% url 'product:sales' %}" class="bg-gray-500 text-white px-3 py-1 rounded text-sm">
                            <i class="fas fa-redo mr-1"></i> Reset
                        </a>
                        {% endif %}
                    </div>
                </form>
            </div>

            <!-- Summary Cards -->
            <div class="grid grid-cols-1 md:grid-cols-3 gap-4 mb-4">
                <div class="bg-blue-100 p-4 rounded-lg">
                    <p class="text-sm text-blue-600">Total Sales</p>
                    <p class="text-2xl font-bold">{{ total_amount|format_khr }}</p>
                </div>
                <div class="bg-green-100 p-4 rounded-lg">
                    <p class="text-sm text-green-600">Total Transactions</p>
                    <p class="text-2xl font-bold">{{ sales|length }}</p>
                </div>
                <div class="bg-purple-100 p-4 rounded-lg">
                    <p class="text-sm text-purple-600">Average Sale</p>
                    <p class="text-2xl font-bold">
                        {% if sales|length > 0 %}{{ total_amount|format_khr }}{% else %}0៛{% endif %}
                    </p>
                </div>
            </div>

            <!-- Sales by Product -->
            <div class="mb-6">
                <h4 class="text-lg font-semibold mb-2">Sales by Product</h4>
                <div class="relative overflow-x-auto">
                    <table class="w-full text-sm text-left">
                        <thead class="text-sm uppercase bg-gray-200 text-gray-700"><tr>
                                <th scope="col" class="px-6 py-3">Product</th>
                                <th scope="col" class="px-6 py-3">Quantity Sold</th>
                                <th scope="col" class="px-6 py-3">Total Amount</th>
                            </tr>
                        </thead>
                        <tbody>
                            {% for item in sales_by_product %}
                            <tr class="bg-white border">
                                <td class="px-6 py-4">{{ item.product__name }}</td>
                                <td class="px-6 py-4">{{ item.total_quantity }}</td>
                                <td class="px-6 py-4">{{ item.total_amount|format_khr }}</td>
                            </tr>
                            {% empty %}
                            <tr class="bg-white border">
                                <td colspan="3" class="px-6 py-4 text-center">No sales data available.</td>
                            </tr>
                            {% endfor %}
                        </tbody>
                    </table>
                </div>
            </div>

            <!-- Sales Transactions -->
            <div>
                <h4 class="text-lg font-semibold mb-2">Sales Transactions</h4>
                <div class="relative overflow-x-auto">
                    <table class="w-full text-sm text-left sales-table">
                        <thead class="text-sm uppercase bg-blue-900 text-gray-50"><tr>
                                <th scope="col" class="px-6 py-3">Transaction ID</th>
                                <th scope="col" class="px-6 py-3">Date</th>
                                <th scope="col" class="px-6 py-3">Total Amount</th>
                                <th scope="col" class="px-6 py-3">Payment Method</th>
                                <th scope="col" class="px-6 py-3">Sold By</th>
                                <th scope="col" class="px-6 py-3">Actions</th>
                            </tr>
                        </thead>
                        <tbody>
                            {% for sale in sales %}
                            <tr class="bg-white border">
                                <td class="px-6 py-4">{{ sale.trxId }}</td>
                                <td class="px-6 py-4">{{ sale.date }}</td>
                                <td class="px-6 py-4">{{ sale.total_amount|format_khr }}</td>
                                <td class="px-6 py-4">{{ sale.get_payment_method_display }}</td>
                                <td class="px-6 py-4">{{ sale.sold_by.username }}</td>
                                <td class="px-6 py-4">
                                    <div class="flex items-center space-x-2">
                                        <button type="button" class="text-blue-600 hover:underline view-details" data-sale-id="{{ sale.id }}">View Details</button>
                                        {% has_permission user 'product' 'full' as can_delete_sale %}
                                        {% if can_delete_sale %}
                                        <button
                                            type="button"
                                            class="bg-red-600 hover:bg-red-700 text-white font-medium py-1 px-3 rounded text-xs transition duration-200 delete-sale-btn"
                                            data-sale-id="{{ sale.id }}"
                                            data-sale-trx="{{ sale.trxId }}"
                                            data-sale-amount="{{ sale.total_amount|format_khr }}"
                                            data-sale-products="{{ sale.items.count }}"
                                            data-sale-soldby="{{ sale.sold_by.username|default:'Unknown' }}"
                                            title="Delete Sale">
                                            <i class="fas fa-trash mr-1"></i><span class="btn-text">Delete</span>
                                        </button>
                                        {% else %}
                                        <span class="text-gray-400 text-xs">No Access</span>
                                        {% endif %}
                                    </div>
                                </td>
                            </tr>
                            <tr class="bg-blue-50 border-t-2 border-blue-200 hidden sale-details" id="details-{{ sale.id }}">
                                <td colspan="6" class="px-6 py-4">
                                    <div class="flex justify-between items-center mb-2">
                                        <h4 class="font-semibold">Sale Items:</h4>
                                        <button type="button" class="close-details text-red-600 hover:text-red-800 text-sm" data-sale-id="{{ sale.id }}">Close</button>
                                    </div>


                                    <table class="w-full text-xs">
                                        <thead class="bg-gray-200"><tr>
                                                <th class="px-2 py-1 text-left">Product</th>
                                                <th class="px-2 py-1 text-left">Quantity</th>
                                                <th class="px-2 py-1 text-left">Price</th>
                                                <th class="px-2 py-1 text-left">Total</th>
                                            </tr>
                                        </thead>
                                        <tbody>
                                            {% for item in sale.items.all %}
                                            <tr>
                                                <td class="px-2 py-1">{{ item.product.name }}</td>
                                                <td class="px-2 py-1">{{ item.display_quantity }}</td>
                                                <td class="px-2 py-1">{{ item.price|format_khr }}</td>
                                                <td class="px-2 py-1">{{ item.total_price|format_khr }}</td>
                                            </tr>
                                            {% empty %}
                                            <tr>
                                                <td colspan="4" class="px-2 py-1 text-center">No items found for this sale.</td>
                                            </tr>
                                            {% endfor %}
                                        </tbody>
                                    </table>
                                    {% if sale.notes %}
                                    <div class="mt-2">
                                        <strong>Notes:</strong> {{ sale.notes }}
                                    </div>
                                    {% endif %}
                                </td>
                            </tr>
                            {% empty %}
                            <tr class="bg-white border">
                                <td colspan="6" class="px-6 py-4 text-center">
                                    {% if filter_active %}
                                    No sales found for the selected filters.
                                    {% else %}
                                    No sales recorded yet.
                                    {% endif %}
                                </td>
                            </tr>
                            {% endfor %}
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block js %}
<script>
    document.addEventListener('DOMContentLoaded', function() {
        console.log('DOM loaded, setting up view-details buttons');

        // Toggle sale details
        const detailButtons = document.querySelectorAll('.view-details');
        console.log('Found ' + detailButtons.length + ' detail buttons');

        // Function to toggle details row
        function toggleDetailsRow(saleId, show) {
            const detailsRowId = 'details-' + saleId;
            const detailsRow = document.getElementById(detailsRowId);

            if (detailsRow) {
                if (show === true || (show === undefined && detailsRow.classList.contains('hidden'))) {
                    // Show the row
                    detailsRow.classList.remove('hidden');
                    detailsRow.style.display = 'table-row';
                    console.log('Showing details row for sale ID: ' + saleId);
                } else {
                    // Hide the row
                    detailsRow.classList.add('hidden');
                    detailsRow.style.display = 'none';
                    console.log('Hiding details row for sale ID: ' + saleId);
                }
            } else {
                console.error('Could not find details row with ID: ' + detailsRowId);
            }
        }

        // Add click handlers to view details buttons
        for (let i = 0; i < detailButtons.length; i++) {
            const button = detailButtons[i];
            const saleId = button.getAttribute('data-sale-id');

            console.log('Setting up view button for sale ID: ' + saleId);

            button.onclick = function(e) {
                e.preventDefault();
                toggleDetailsRow(saleId);
                return false;
            };
        }

        // Add click handlers to close buttons
        const closeButtons = document.querySelectorAll('.close-details');
        for (let i = 0; i < closeButtons.length; i++) {
            const button = closeButtons[i];
            const saleId = button.getAttribute('data-sale-id');

            console.log('Setting up close button for sale ID: ' + saleId);

            button.onclick = function(e) {
                e.preventDefault();
                toggleDetailsRow(saleId, false);
                return false;
            };
        }

        // Delete sale functionality with confirmation
        document.querySelectorAll('.delete-sale-btn').forEach(button => {
            button.addEventListener('click', function() {
                const saleId = this.getAttribute('data-sale-id');
                const saleTrx = this.getAttribute('data-sale-trx');
                const saleAmount = this.getAttribute('data-sale-amount');
                const saleProducts = this.getAttribute('data-sale-products');
                const soldBy = this.getAttribute('data-sale-soldby');

                // Use the product/products text based on the number of items
                const productText = saleProducts == 1 ? "product" : "products";

                // Create confirmation dialog
                const confirmMessage = `Are you sure you want to delete this sale?\n\n` +
                    `Transaction ID: ${saleTrx}\n` +
                    `Amount: ${saleAmount}\n` +
                    `Products: ${saleProducts} ${productText}\n` +
                    `Sold by: ${soldBy}\n\n` +
                    `This action cannot be undone. The sale amount will be deducted from gym funds.`;

                if (confirm(confirmMessage)) {
                    // Create a form and submit it to delete the sale
                    const form = document.createElement('form');
                    form.method = 'POST';
                    form.action = `/product/sales/delete/${saleId}/`;

                    // Add CSRF token
                    const csrfToken = document.querySelector('[name=csrfmiddlewaretoken]').value;
                    const csrfInput = document.createElement('input');
                    csrfInput.type = 'hidden';
                    csrfInput.name = 'csrfmiddlewaretoken';
                    csrfInput.value = csrfToken;
                    form.appendChild(csrfInput);

                    // Submit the form
                    document.body.appendChild(form);
                    form.submit();
                }
            });
        });
    });
</script>
{% endblock %}
