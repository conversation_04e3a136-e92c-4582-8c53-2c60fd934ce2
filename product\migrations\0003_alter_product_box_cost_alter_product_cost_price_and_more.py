# Generated by Django 5.0.2 on 2025-04-26 05:27

from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('product', '0002_initial'),
    ]

    operations = [
        migrations.AlterField(
            model_name='product',
            name='box_cost',
            field=models.IntegerField(blank=True, null=True, verbose_name='Box Cost'),
        ),
        migrations.AlterField(
            model_name='product',
            name='cost_price',
            field=models.IntegerField(verbose_name='Cost Price'),
        ),
        migrations.AlterField(
            model_name='product',
            name='retail_price',
            field=models.IntegerField(verbose_name='Retail Price'),
        ),
        migrations.AlterField(
            model_name='purchase',
            name='total_amount',
            field=models.IntegerField(verbose_name='Total Amount'),
        ),
        migrations.AlterField(
            model_name='purchaseitem',
            name='cost_price',
            field=models.IntegerField(verbose_name='Cost Price'),
        ),
        migrations.AlterField(
            model_name='purchaseitem',
            name='stored_box_cost',
            field=models.IntegerField(default=0, verbose_name='Stored Box Cost'),
        ),
        migrations.AlterField(
            model_name='sale',
            name='total_amount',
            field=models.IntegerField(verbose_name='Total Amount'),
        ),
        migrations.AlterField(
            model_name='saleitem',
            name='price',
            field=models.IntegerField(verbose_name='Price'),
        ),
    ]
