# Currency Formatting Cheatsheet

## Django Templates

### Load Filters
```django
{% load currency_filters %}
```

### Format KHR
```django
{{ value|format_khr }}  <!-- 10000 -> 10,000៛ -->
```

### Format USD
```django
{{ value|format_usd }}  <!-- 10000 -> $10,000.00 -->
```

### Format Number (no currency)
```django
{{ value|format_number_with_commas }}  <!-- 10000 -> 10,000 -->
```

## JavaScript

### Include Script
```html
<script src="{% static 'js/currency-formatter.js' %}"></script>
```

### Format KHR
```javascript
formatKHR(10000);  // Returns "10,000៛"
```

### Format USD
```javascript
formatUSD(10000);  // Returns "$10,000.00"
```

### Format Number (no currency)
```javascript
formatNumber(10000);  // Returns "10,000"
formatNumber(10000, 2);  // Returns "10,000.00"
```

## Form Handling

### Setup Input Field
```javascript
const amountInput = document.getElementById('amount_khr');
setupFormattedInput(amountInput, 'khr');
```

### Handle Form Submission
```javascript
const form = document.getElementById('my-form');
form.addEventListener('submit', function(e) {
    // Convert formatted value to numeric value
    amountInput.value = amountInput.value.replace(/[^\d.-]/g, '');
});
```

## Testing

### Run Django Tests
```bash
python manage.py test core.tests.test_currency_formatters
```

### Run JavaScript Tests
Visit: `/test-js-currency-formatting/`
