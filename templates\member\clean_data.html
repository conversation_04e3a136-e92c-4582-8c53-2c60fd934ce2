{% extends "../base.html" %}
{% load custom_filters %}



{% block body %}
<!-- component sections starts  -->
<div class="conponentSection p-5 bg-gray-200">
    <div class="componentWrapper">
        <!-- Header -->
        <div class="bg-white p-4 rounded shadow-md mb-4">
            <h3 class="text-2xl font-bold mb-4">Data Cleaning Tools</h3>
            <p class="text-red-600 font-bold mb-4">Warning: These actions are irreversible. Use with caution!</p>

            <!-- Current Data Stats -->
            <div class="grid grid-cols-2 gap-4 mb-6">
                <div class="bg-blue-50 p-4 rounded shadow">
                    <h4 class="text-lg font-semibold mb-2">Current Data</h4>
                    <p><span class="font-medium">Members:</span> {{ member_count }}</p>
                    <p><span class="font-medium">Payments:</span> {{ payment_count }}</p>
                </div>
                <div class="bg-yellow-50 p-4 rounded shadow">
                    <h4 class="text-lg font-semibold mb-2">Instructions</h4>
                    <p>Use these tools to clean data from the database. This is useful for:</p>
                    <ul class="list-disc pl-5 mt-2">
                        <li>Fixing foreign key constraint errors</li>
                        <li>Starting fresh with empty tables</li>
                        <li>Removing test data before going to production</li>
                    </ul>
                </div>
            </div>

            <!-- Clean Transaction Tables -->
            <div class="bg-white border border-gray-200 p-4 rounded shadow-md mb-4">
                <h4 class="text-lg font-semibold mb-2">Clean Transaction Tables</h4>
                <p class="mb-4">This will clean the transaction_payrollrecord and transaction_transaction tables that might be causing foreign key constraint errors.</p>
                <div class="bg-yellow-50 p-3 rounded mb-4">
                    <p class="text-sm text-yellow-800"><i class="fas fa-exclamation-triangle mr-1"></i> These tables are legacy tables that are no longer used by the application but may still exist in the database.</p>
                </div>
                <form method="post" onsubmit="return confirm('Are you sure you want to clean the transaction tables? This action cannot be undone.')">
                    {% csrf_token %}
                    <input type="hidden" name="action" value="clean_transaction_table">
                    <button type="submit" class="bg-red-600 hover:bg-red-700 text-white font-bold py-2 px-4 rounded">Clean Transaction Tables</button>
                </form>
            </div>

            <!-- Clean Members -->
            <div class="bg-white border border-gray-200 p-4 rounded shadow-md mb-4">
                <h4 class="text-lg font-semibold mb-2">Delete All Members</h4>
                <p class="mb-4">This will delete all members from the database. It will also attempt to clean the transaction table first.</p>
                <form method="post" onsubmit="return confirm('Are you sure you want to delete ALL members? This action cannot be undone.')">
                    {% csrf_token %}
                    <input type="hidden" name="action" value="clean_members">
                    <button type="submit" class="bg-red-600 hover:bg-red-700 text-white font-bold py-2 px-4 rounded">Delete All Members ({{ member_count }})</button>
                </form>
            </div>

            <!-- Clean Payments -->
            <div class="bg-white border border-gray-200 p-4 rounded shadow-md mb-4">
                <h4 class="text-lg font-semibold mb-2">Delete All Payments</h4>
                <p class="mb-4">This will delete all payment records from the database.</p>
                <form method="post" onsubmit="return confirm('Are you sure you want to delete ALL payments? This action cannot be undone.')">
                    {% csrf_token %}
                    <input type="hidden" name="action" value="clean_payments">
                    <button type="submit" class="bg-red-600 hover:bg-red-700 text-white font-bold py-2 px-4 rounded">Delete All Payments ({{ payment_count }})</button>
                </form>
            </div>

            <!-- Clean All Data -->
            <div class="bg-white border border-gray-200 p-4 rounded shadow-md mb-4">
                <h4 class="text-lg font-semibold mb-2">Clean All Data</h4>
                <p class="mb-4">This will delete all members and payments, and clean the transaction table. Use this to start fresh.</p>
                <form method="post" onsubmit="return confirm('Are you sure you want to clean ALL data? This will delete all members and payments. This action cannot be undone.')">
                    {% csrf_token %}
                    <input type="hidden" name="action" value="clean_all">
                    <button type="submit" class="bg-red-600 hover:bg-red-700 text-white font-bold py-2 px-4 rounded">Clean All Data</button>
                </form>
            </div>

            <!-- Back Button -->
            <div class="mt-6">
                <a href="{% url 'member:index' %}" class="bg-gray-500 hover:bg-gray-600 text-white font-bold py-2 px-4 rounded">Back to Members</a>
            </div>
        </div>
    </div>
</div>
{% endblock body %}
