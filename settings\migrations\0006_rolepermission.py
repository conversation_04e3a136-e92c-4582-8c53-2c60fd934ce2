# Generated by Django 5.0.2 on 2025-05-20 01:07

from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('settings', '0005_delete_printtemplate'),
    ]

    operations = [
        migrations.CreateModel(
            name='RolePermission',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('role', models.CharField(choices=[('admin', 'Admin'), ('cashier', 'Cashier'), ('coach', 'Coach'), ('cleaner', 'Cleaner'), ('security', 'Security Guard')], max_length=20, verbose_name='Role')),
                ('module', models.CharField(choices=[('dashboard', 'Dashboard'), ('member', 'Member Management'), ('payment', 'Payment Processing'), ('payroll', 'Payroll Management'), ('product', 'Product Management'), ('inventory', 'Inventory Management'), ('purchase', 'Purchase Management'), ('pos', 'Point of Sale'), ('paypervisit', 'Pay-per-visit'), ('bill', 'Bill Management'), ('finance', 'Finance Management'), ('settings', 'Settings')], max_length=50, verbose_name='Module')),
                ('permission_level', models.CharField(choices=[('none', 'No Access'), ('view', 'View Only'), ('edit', 'View and Edit'), ('full', 'Full Access')], default='none', max_length=10, verbose_name='Permission Level')),
            ],
            options={
                'verbose_name': 'Role Permission',
                'verbose_name_plural': 'Role Permissions',
                'unique_together': {('role', 'module')},
            },
        ),
    ]
