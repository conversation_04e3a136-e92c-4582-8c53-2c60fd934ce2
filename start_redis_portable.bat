@echo off
echo Starting Redis Server (Portable Version)...
echo.

REM Check if Redis directory exists
if not exist "redis-server" (
    echo Redis not found. Downloading portable Redis...
    echo.
    
    REM Create redis directory
    mkdir redis-server
    cd redis-server
    
    echo Please download Redis for Windows from:
    echo https://github.com/microsoftarchive/redis/releases/download/win-3.0.504/Redis-x64-3.0.504.zip
    echo.
    echo Extract the contents to the redis-server folder and run this script again.
    echo.
    pause
    exit /b 1
)

cd redis-server

REM Check if redis-server.exe exists
if not exist "redis-server.exe" (
    echo redis-server.exe not found in redis-server directory.
    echo Please download and extract Redis for Windows to this folder.
    echo Download from: https://github.com/microsoftarchive/redis/releases
    pause
    exit /b 1
)

echo Starting Redis server on port 6379...
echo Press Ctrl+C to stop the server
echo.

REM Start Redis server
redis-server.exe redis.windows.conf

pause
