# Legend Fitness Club - Demo Data Documentation

## Overview

This document describes the comprehensive demo data creation script for the Legend Fitness Club gym management system. The script creates realistic, culturally appropriate data for a Cambodian gym context.

## Demo Data Features

### 🏋️ **Comprehensive Coverage**
- **Users/Employees**: 6 staff members (1 admin + 5 employees)
- **Members**: 8 gym members with varied membership statuses
- **Packages**: 4 membership packages (Basic, Premium, VIP, Super VIP)
- **Products**: 12 gym products across 4 categories
- **Suppliers**: 4 Cambodian suppliers
- **Financial Data**: Focused transactions, payments, bills, and salary records
- **Operational Data**: Pay-per-visit transactions, purchases, sales

### 🇰🇭 **Cambodian Context**
- **Authentic Names**: Real Cambodian first and last names
- **Realistic Addresses**: Phnom Penh districts and street formats
- **Phone Numbers**: Proper Cambodian mobile prefixes (010, 012, 015, etc.)
- **Currency**: Proper KHR and USD amounts with realistic exchange rates
- **Local Providers**: Cambodian utility companies and service providers

### 💰 **Financial Realism**
- **Membership Prices**: $30-250 USD (120,000-1,000,000 KHR)
- **Pay-per-visit**: 4,000 KHR per person
- **Product Pricing**: Realistic gym supplement and merchandise prices
- **Utility Bills**: Authentic Cambodian utility costs
- **Salaries**: Appropriate salary ranges for different roles

## Data Structure

### Users & Employees (6 total)
```
- Admin: Dara Sok (LFC-001) - Full system access
- Coach: Sokha Meas (LFC-002) - Training and member management
- Cashiers: Bopha Chhay, Sreypov Prak (LFC-003, LFC-004) - POS and payment access
- Cleaner: Srey Tep (LFC-005) - Limited access
- Security: Rithy Khun (LFC-006) - Basic access
```

### Membership Packages
```
- Basic (1 month): $30 USD / 120,000 KHR
- Premium (3 months): $75 USD / 300,000 KHR
- VIP (6 months): $137.50 USD / 550,000 KHR
- Super VIP (12 months): $250 USD / 1,000,000 KHR
```

### Member Distribution (8 total)
```
- 3 Active members (recently joined)
- 3 Members near expiration (60-90 days old)
- 2 Mixed status members (new, expired, overdue)
- Realistic gender distribution
- Authentic Cambodian names and addresses
```

### Product Categories (12 total)
```
- Beverages: Water, Sports drinks
- Snacks: Protein bars, Energy bars
- Supplements: Whey protein, BCAA, Creatine, Pre-workout
- Merchandise: T-shirts, Towels, Water bottles, Gym gloves
```

### Financial Transactions (Focused Results)
```
- 10 Finance Transactions (6 deposits, 4 withdrawals)
- 4 Pay-per-visit transactions
- 10 Member payment records
- 10 Salary payments (2 months for 6 employees)
- 3 Bills (electricity, water, rent for current month)
- 16 Purchases and 4 Sales transactions
```

## Usage Instructions

### Running the Script

1. **Navigate to project directory:**
   ```bash
   cd "c:\Final Project\legend_fitness_club-gym-ms"
   ```

2. **Run the demo data script:**
   ```bash
   python create_demo_data.py
   ```

3. **Expected output:**
   ```
   === Starting Comprehensive Demo Data Creation ===
   Creating realistic data for Legend Fitness Club - Cambodia

   Initializing system settings...
   Creating users with different roles...
   Creating membership packages...
   Creating sample members...
   [... detailed creation logs ...]

   === Demo Data Creation Summary ===
   ✅ Users/Employees: 6 (1 admin + 5 employees)
   ✅ Members: 8
   ✅ Packages: 4
   ✅ Products: 12
   ✅ Suppliers: 4
   ✅ Purchases: 16
   ✅ Sales: 4
   ✅ Pay-per-visit: 4
   ✅ Member Payments: 10
   ✅ Salary Payments: 10
   ✅ Finance Transactions: 10
   ✅ Bills: 3

   👥 Employee Breakdown:
      - 1 Coach, 2 Cashiers, 1 Cleaner, 1 Security Guard

   🎉 Comprehensive demo data creation completed successfully!
   ```

### Accessing the System

After running the script, you can access the system with these credentials:

**Admin Account:**
- Username: `admin`
- Password: `admin123`
- Role: Administrator
- Full access to all features

**Staff Accounts:**
- Username: `coach1` / Password: `password123` (Coach)
- Username: `cashier1` / Password: `password123` (Cashier)
- Username: `cashier2` / Password: `password123` (Cashier)
- Username: `cleaner1` / Password: `password123` (Cleaner)
- Username: `security1` / Password: `password123` (Security)

### System URLs

- **Main Dashboard**: http://127.0.0.1:8000/
- **Admin Dashboard**: http://127.0.0.1:8000/adminDashboard/
- **Admin Panel**: http://127.0.0.1:8000/admin/

## Data Quality Standards

### ✅ **Authenticity**
- Real Cambodian names from common name databases
- Accurate Phnom Penh district names and addresses
- Proper Cambodian mobile phone number formats
- Realistic pricing based on Cambodian market research

### ✅ **Consistency**
- All relationships between models are properly maintained
- Payment records match membership packages
- Financial transactions balance correctly
- Date ranges are logical and realistic

### ✅ **Volume**
- Sufficient data for meaningful testing and presentation
- Varied scenarios (active, expired, near-expiration members)
- Multiple transaction types and payment methods
- Comprehensive coverage of all system features

### ✅ **Professional Quality**
- Clean, formatted output with progress indicators
- Proper error handling and validation
- Comprehensive logging of all created records
- Summary statistics for verification

## Technical Details

### Dependencies
- Django 5.1.7
- All project apps (core, members, payments, etc.)
- Python standard libraries (random, datetime, etc.)

### Database Impact
- Creates data across all major models
- Uses Django's transaction.atomic() for data integrity
- Respects existing data (won't duplicate)
- Safe to run multiple times

### Performance
- Optimized for single-run execution
- Batch operations where possible
- Minimal database queries
- Completes in under 30 seconds

## Customization

The script can be easily customized by modifying:

- **Name lists**: Update `get_cambodian_names()` function
- **Address formats**: Modify `generate_cambodian_address()` function
- **Pricing**: Adjust amounts in package and product creation
- **Volume**: Change range values in loops
- **Business logic**: Update status distributions and relationships

## Support

For questions or issues with the demo data:

1. Check the console output for detailed creation logs
2. Verify all dependencies are installed
3. Ensure database migrations are up to date
4. Contact the development team for assistance

---

**Note**: This demo data is designed for development, testing, and presentation purposes. It should not be used in production environments.
