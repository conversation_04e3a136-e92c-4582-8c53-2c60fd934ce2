from django.db import models
from django.utils.translation import gettext_lazy as _
from user.models import User
from django.conf import settings

# Create your models here.
class Category(models.Model):
    name = models.CharField(_('Name'), max_length=100)
    description = models.TextField(_('Description'), blank=True, null=True)

    def __str__(self):
        return self.name

    class Meta:
        verbose_name_plural = 'Categories'

class Supplier(models.Model):
    name = models.CharField(_('Name'), max_length=100)
    phone = models.CharField(_('Phone'), max_length=20, blank=True, null=True)
    telegram = models.CharField(_('Telegram/Messenger'), max_length=100, blank=True, null=True)
    address = models.TextField(_('Address'), blank=True, null=True)
    note = models.TextField(_('Note'), blank=True, null=True)

    def __str__(self):
        return self.name

class Product(models.Model):
    name = models.CharField(_('Name'), max_length=100)
    sku = models.CharField(_('SKU'), max_length=20, unique=True)
    category = models.ForeignKey(Category, on_delete=models.SET_NULL, null=True, related_name='products')
    description = models.TextField(_('Description'), blank=True, null=True)
    image = models.ImageField(_('Image'), upload_to='products/', blank=True, null=True)

    # Pricing
    cost_price = models.IntegerField(_('Cost Price'))
    retail_price = models.IntegerField(_('Retail Price'))

    # Quantity (kept for product management)
    quantity = models.IntegerField(_('Quantity'), default=0)

    # Box information
    box_quantity = models.IntegerField(_('Box Quantity'), default=1, help_text='Number of items per box')
    box_cost = models.IntegerField(_('Box Cost'), blank=True, null=True)

    # Status
    is_active = models.BooleanField(_('Active'), default=True, help_text='Whether this product is active and available for sale')

    # Tracking
    created_at = models.DateTimeField(_('Created At'), auto_now_add=True)
    updated_at = models.DateTimeField(_('Updated At'), auto_now=True)

    def __str__(self):
        return f"{self.name} ({self.sku})"

    @property
    def total_value(self):
        return self.quantity * self.retail_price

    @property
    def boxes_count(self):
        """Calculate the number of complete boxes based on total quantity and box_quantity"""
        if self.box_quantity > 1:
            return self.quantity // self.box_quantity
        return 0

    @property
    def units_count(self):
        """Calculate the remaining units that don't make a complete box"""
        if self.box_quantity > 1:
            return self.quantity % self.box_quantity
        return self.quantity



class Purchase(models.Model):
    trxId = models.CharField(_('Transaction ID'), max_length=50, unique=True)
    supplier = models.ForeignKey(Supplier, on_delete=models.SET_NULL, null=True, blank=True, related_name='purchases')
    date = models.DateTimeField(_('Date'))
    total_amount = models.IntegerField(_('Total Amount'))
    notes = models.TextField(_('Notes'), blank=True, null=True)
    created_by = models.ForeignKey(User, on_delete=models.SET_NULL, null=True)
    created_at = models.DateTimeField(_('Created At'), auto_now_add=True)

    def __str__(self):
        return f"{self.trxId} - {self.date.strftime('%Y-%m-%d')}"

class PurchaseItem(models.Model):
    purchase = models.ForeignKey(Purchase, on_delete=models.CASCADE, related_name='items')
    product = models.ForeignKey(Product, on_delete=models.CASCADE)
    supplier = models.ForeignKey(Supplier, on_delete=models.SET_NULL, null=True, blank=True, related_name='purchase_items')
    quantity = models.IntegerField(_('Quantity'))
    cost_price = models.IntegerField(_('Cost Price'))
    is_box_purchase = models.BooleanField(_('Box Purchase'), default=False)
    box_quantity = models.IntegerField(_('Box Quantity'), default=1, help_text='Number of units per box')
    original_quantity = models.IntegerField(_('Original Quantity'), default=0, help_text='Original quantity entered (in boxes if box purchase)')
    stored_box_cost = models.IntegerField(_('Stored Box Cost'), default=0)

    def __str__(self):
        supplier_info = f" from {self.supplier.name}" if self.supplier else ""
        return f"{self.product.name} x {self.quantity}{supplier_info}"

    @property
    def box_cost(self):
        # Use the stored box cost if available, otherwise calculate it
        if self.stored_box_cost and self.stored_box_cost > 0:
            return self.stored_box_cost
        else:
            # Calculate the cost of one box
            return self.cost_price * self.box_quantity

    @property
    def total_cost(self):
        from decimal import Decimal

        if self.is_box_purchase and self.box_quantity > 1:
            # For box purchases, calculate total based on original box quantity and box cost
            # We need to match the total amount that was recorded during purchase
            # This should be the original quantity (in boxes) times the box cost
            return Decimal(self.original_quantity) * self.box_cost
        else:
            # For unit purchases (only for products without box quantities)
            return Decimal(self.quantity) * self.cost_price

    @property
    def display_quantity(self):
        if self.is_box_purchase and self.box_quantity > 1:
            return f"{self.original_quantity} boxes ({self.quantity} units)"
        else:
            return f"{self.quantity} units"

    @property
    def display_cost(self):
        if self.is_box_purchase and self.box_quantity > 1:
            # For box purchases, show the box cost
            return f"{self.box_cost}៛ per box ({self.cost_price}៛ per unit)"
        else:
            # For unit purchases, show the unit cost
            return f"{self.cost_price}៛ per unit"

class Sale(models.Model):
    trxId = models.CharField(_('Transaction ID'), max_length=50, unique=True)
    date = models.DateTimeField(_('Date'), auto_now_add=True)
    total_amount = models.IntegerField(_('Total Amount'))
    payment_method = models.CharField(_('Payment Method'), max_length=20,
                                    choices=[
                                        ('cash', _('Cash')),
                                        ('bank', _('Bank Transfer')),
                                        ('other', _('Other')),
                                    ], default='cash')
    sold_by = models.ForeignKey(User, on_delete=models.SET_NULL, null=True)
    notes = models.TextField(_('Notes'), blank=True, null=True)

    def __str__(self):
        return f"{self.trxId} - {self.date.strftime('%Y-%m-%d')}"

class SaleItem(models.Model):
    sale = models.ForeignKey(Sale, on_delete=models.CASCADE, related_name='items')
    product = models.ForeignKey(Product, on_delete=models.CASCADE)
    quantity = models.IntegerField(_('Quantity'))
    price = models.IntegerField(_('Price'))
    is_box_equivalent = models.BooleanField(_('Box Equivalent'), default=False, help_text='Whether this sale represents full box equivalents')
    box_quantity = models.IntegerField(_('Box Quantity'), default=1, help_text='Number of units per box')

    def __str__(self):
        return f"{self.product.name} x {self.quantity}"

    @property
    def total_price(self):
        from decimal import Decimal

        # For box equivalents, ensure we use the exact box price
        if self.is_box_equivalent and self.box_quantity > 1 and self.quantity % self.box_quantity == 0:
            num_boxes = Decimal(self.quantity) / Decimal(self.box_quantity)
            box_price = self.price * Decimal(self.box_quantity)
            return num_boxes * box_price
        else:
            return Decimal(self.quantity) * self.price

    @property
    def display_quantity(self):
        if self.is_box_equivalent and self.box_quantity > 1:
            from decimal import Decimal
            num_boxes = int(Decimal(self.quantity) / Decimal(self.box_quantity))
            return f"{num_boxes} boxes ({self.quantity} units)"
        else:
            return f"{self.quantity} units"




from django.db.models.signals import pre_save
from django.dispatch import receiver

# Signal handler to automatically manage product active status based on stock
@receiver(pre_save, sender=Product)
def auto_manage_product_status(sender, instance, **kwargs):
    """
    Automatically manage product active status based on stock:
    - Deactivate products when they go out of stock
    - Reactivate products when they come back in stock
    """
    # Get the current product from the database if it exists
    try:
        current_product = Product.objects.get(pk=instance.pk)
        was_out_of_stock = current_product.quantity <= 0
    except Product.DoesNotExist:
        # New product being created
        was_out_of_stock = True  # Default to true for new products

    # Get settings from MetaData or fallback to settings.py
    from user.models import MetaData
    try:
        metadata = MetaData.objects.last()
        if metadata:
            deactivate_enabled = metadata.auto_deactivate_out_of_stock
            reactivate_enabled = metadata.auto_reactivate_in_stock
        else:
            deactivate_enabled = getattr(settings, 'PRODUCT_AUTO_DEACTIVATE_OUT_OF_STOCK', True)
            reactivate_enabled = getattr(settings, 'PRODUCT_AUTO_REACTIVATE_IN_STOCK', True)
    except Exception:
        # If there's an error, use the settings from settings.py
        deactivate_enabled = getattr(settings, 'PRODUCT_AUTO_DEACTIVATE_OUT_OF_STOCK', True)
        reactivate_enabled = getattr(settings, 'PRODUCT_AUTO_REACTIVATE_IN_STOCK', True)

    # Auto-deactivate out-of-stock products
    if deactivate_enabled and instance.quantity <= 0 and instance.is_active:
        print(f"Auto-deactivating out-of-stock product: {instance.name} (ID: {instance.id})")
        instance.is_active = False

    # Auto-reactivate products that come back in stock
    if reactivate_enabled and was_out_of_stock and instance.quantity > 0 and not instance.is_active:
        print(f"Auto-reactivating in-stock product: {instance.name} (ID: {instance.id})")
        instance.is_active = True
