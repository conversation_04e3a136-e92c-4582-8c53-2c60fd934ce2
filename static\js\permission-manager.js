/**
 * Real-time Permission Management System
 * Handles WebSocket connections and dynamic sidebar updates
 */

class PermissionManager {
    constructor() {
        this.permissionSocket = null;
        this.notificationSocket = null;
        this.currentPermissions = {};
        this.reconnectAttempts = 0;
        this.maxReconnectAttempts = 5;
        this.reconnectDelay = 1000; // Start with 1 second
        this.isConnected = false;
        this.pollingInterval = null;
        this.pollingFrequency = 30000; // 30 seconds fallback polling

        this.init();
    }

    init() {
        this.connectWebSockets();
        this.setupFallbackPolling();
        this.setupEventListeners();

        // Log initialization
        console.log('Permission Manager initialized');
    }

    connectWebSockets() {
        try {
            // Connect to permission updates WebSocket
            const protocol = window.location.protocol === 'https:' ? 'wss:' : 'ws:';
            const permissionUrl = `${protocol}//${window.location.host}/ws/permissions/`;
            const notificationUrl = `${protocol}//${window.location.host}/ws/notifications/`;

            this.permissionSocket = new WebSocket(permissionUrl);
            this.notificationSocket = new WebSocket(notificationUrl);

            this.setupPermissionSocketHandlers();
            this.setupNotificationSocketHandlers();

        } catch (error) {
            console.error('Error connecting to WebSockets:', error);
            this.fallbackToPolling();
        }
    }

    setupPermissionSocketHandlers() {
        this.permissionSocket.onopen = () => {
            console.log('Permission WebSocket connected');
            this.isConnected = true;
            this.reconnectAttempts = 0;
            this.reconnectDelay = 1000;

            // Stop polling when WebSocket is connected
            this.stopPolling();
        };

        this.permissionSocket.onmessage = (event) => {
            try {
                const data = JSON.parse(event.data);
                this.handlePermissionMessage(data);
            } catch (error) {
                console.error('Error parsing permission message:', error);
            }
        };

        this.permissionSocket.onclose = () => {
            console.log('Permission WebSocket disconnected');
            this.isConnected = false;
            this.attemptReconnect();
        };

        this.permissionSocket.onerror = (error) => {
            console.error('Permission WebSocket error:', error);
            this.fallbackToPolling();
        };
    }

    setupNotificationSocketHandlers() {
        this.notificationSocket.onopen = () => {
            console.log('Notification WebSocket connected');
        };

        this.notificationSocket.onmessage = (event) => {
            try {
                const data = JSON.parse(event.data);
                this.handleNotificationMessage(data);
            } catch (error) {
                console.error('Error parsing notification message:', error);
            }
        };

        this.notificationSocket.onclose = () => {
            console.log('Notification WebSocket disconnected');
        };

        this.notificationSocket.onerror = (error) => {
            console.error('Notification WebSocket error:', error);
        };
    }

    handlePermissionMessage(data) {
        switch (data.type) {
            case 'permission_sync':
            case 'permission_update':
                this.updatePermissions(data.permissions);
                this.updateSidebar(data.permissions);

                if (data.message) {
                    this.showNotification(data.message, 'info');
                }
                break;

            case 'permission_notification':
                this.showNotification(data.message, data.notification_type);
                break;

            default:
                console.log('Unknown permission message type:', data.type);
        }
    }

    handleNotificationMessage(data) {
        if (data.type === 'system_notification') {
            this.showNotification(data.message, data.notification_type);
        }
    }

    updatePermissions(permissions) {
        this.currentPermissions = permissions;
        console.log('Permissions updated:', permissions);
    }

    updateSidebar(permissions) {
        console.log('Updating sidebar with permissions:', permissions);

        // Update sidebar menu visibility based on new permissions
        const sidebar = document.querySelector('.dashBoardLinks');
        if (!sidebar) {
            console.error('Sidebar not found');
            return;
        }

        // Define module to menu item mapping with more specific selectors
        const moduleMenuMap = {
            'member': {
                selectors: ['[href*="/member/"]', '[href*="member:index"]', '[href*="member:package_list"]'],
                submenuId: 'membershipSubmenu'
            },
            'payment': {
                selectors: ['[href*="/payment/"]', '[href*="payment:index"]'],
                submenuId: 'paymentsSubmenu'
            },
            'paypervisit': {
                selectors: ['[href*="/paypervisit/"]', '[href*="paypervisit:index"]', '[href*="paypervisit:transaction"]'],
                submenuId: 'paymentsSubmenu'
            },
            'payroll': {
                selectors: ['[href*="/payroll/"]', '[href*="payroll:index"]'],
                submenuId: 'paymentsSubmenu'
            },
            'bill': {
                selectors: ['[href*="/billmanagement/"]', '[href*="billmanagement:index"]'],
                submenuId: 'paymentsSubmenu'
            },
            'product': {
                selectors: ['[href*="/product/"]', '[href*="product:index"]', '[href*="product:pos"]'],
                submenuId: 'productsSubmenu'
            },
            'finance': {
                selectors: ['[href*="/finance/"]', '[href*="finance:index"]'],
                submenuId: 'financeSubmenu'
            },
            'financialreport': {
                selectors: ['[href*="/financialreport/"]', '[href*="financialreport:index"]'],
                submenuId: 'reportsSubmenu'
            },
            'user': {
                selectors: ['[href*="/user/"]', '[href*="user:index"]'],
                submenuId: 'staffSubmenu'
            },
            'settings': {
                selectors: ['[href*="/settings/"]', '[href*="settings:dashboard"]'],
                submenuId: 'settingsSubmenu'
            }
        };

        // Track changes for visual feedback
        const changedItems = [];

        // Update menu visibility
        Object.keys(moduleMenuMap).forEach(module => {
            const hasAccess = permissions[module] && permissions[module] !== 'none';
            const moduleConfig = moduleMenuMap[module];

            console.log(`Module ${module}: hasAccess=${hasAccess}, permission=${permissions[module]}`);

            moduleConfig.selectors.forEach(selector => {
                const menuItems = sidebar.querySelectorAll(selector);
                menuItems.forEach(item => {
                    const listItem = item.closest('li');
                    if (listItem) {
                        const wasHidden = listItem.style.display === 'none' || listItem.classList.contains('permission-hidden');

                        if (hasAccess) {
                            listItem.style.display = '';
                            listItem.classList.remove('permission-hidden');
                            if (wasHidden) {
                                changedItems.push(listItem);
                                console.log(`Showing menu item for ${module}:`, item.textContent.trim());
                            }
                        } else {
                            listItem.style.display = 'none';
                            listItem.classList.add('permission-hidden');
                            if (!wasHidden) {
                                console.log(`Hiding menu item for ${module}:`, item.textContent.trim());
                            }
                        }
                    }
                });
            });
        });

        // Handle submenu visibility
        this.updateSubmenuVisibility();

        // Add visual feedback for changed items
        this.highlightChangedMenuItems(changedItems);

        // Force page reload if significant changes occurred
        if (changedItems.length > 0) {
            this.showNotification(`Permissions updated! ${changedItems.length} menu items changed.`, 'success');

            // Automatically reload page after a short delay to ensure all changes are applied
            setTimeout(() => {
                console.log('Auto-reloading page due to permission changes...');
                window.location.reload();
            }, 1500);
        } else {
            // Even if no visual changes detected, check if any permissions actually changed
            const hasPermissionChanges = Object.keys(permissions).some(module => {
                const currentPermission = this.currentPermissions[module];
                const newPermission = permissions[module];
                return currentPermission !== newPermission;
            });

            if (hasPermissionChanges) {
                this.showNotification('Your permissions have been updated.', 'info');
                setTimeout(() => {
                    console.log('Auto-reloading page due to permission changes...');
                    window.location.reload();
                }, 1500);
            }
        }
    }

    updateSubmenuVisibility() {
        // Hide submenus if all child items are hidden
        const submenus = document.querySelectorAll('.submenu');
        submenus.forEach(submenu => {
            const visibleItems = submenu.querySelectorAll('li:not(.permission-hidden)');
            const parentToggle = submenu.previousElementSibling;

            if (visibleItems.length === 0 && parentToggle) {
                parentToggle.closest('li').style.display = 'none';
            } else if (parentToggle) {
                parentToggle.closest('li').style.display = '';
            }
        });
    }

    highlightChangedMenuItems(changedItems) {
        // Add temporary highlight to changed menu items
        changedItems.forEach(item => {
            item.classList.add('permission-updated');
            item.style.backgroundColor = '#10b981'; // Green highlight
            item.style.transition = 'background-color 0.3s ease';

            setTimeout(() => {
                item.classList.remove('permission-updated');
                item.style.backgroundColor = '';
            }, 3000);
        });
    }

    highlightUpdatedMenuItems() {
        // Legacy method - kept for compatibility
        const updatedItems = document.querySelectorAll('.dashBoardLinks li:not(.permission-hidden)');
        this.highlightChangedMenuItems(Array.from(updatedItems));
    }

    showNotification(message, type = 'info') {
        // Create notification element
        const notification = document.createElement('div');
        notification.className = `permission-notification notification-${type}`;
        notification.innerHTML = `
            <div class="notification-content">
                <i class="fas fa-${this.getNotificationIcon(type)}"></i>
                <span>${message}</span>
                <button class="notification-close" onclick="this.parentElement.parentElement.remove()">
                    <i class="fas fa-times"></i>
                </button>
            </div>
        `;

        // Add to page
        const container = this.getNotificationContainer();
        container.appendChild(notification);

        // Auto-remove after 5 seconds
        setTimeout(() => {
            if (notification.parentElement) {
                notification.remove();
            }
        }, 5000);

        console.log(`Notification (${type}): ${message}`);
    }

    getNotificationIcon(type) {
        const icons = {
            'info': 'info-circle',
            'success': 'check-circle',
            'warning': 'exclamation-triangle',
            'error': 'exclamation-circle'
        };
        return icons[type] || 'info-circle';
    }

    getNotificationContainer() {
        let container = document.getElementById('permission-notifications');
        if (!container) {
            container = document.createElement('div');
            container.id = 'permission-notifications';
            container.className = 'permission-notifications-container';
            document.body.appendChild(container);
        }
        return container;
    }

    attemptReconnect() {
        if (this.reconnectAttempts >= this.maxReconnectAttempts) {
            console.log('Max reconnection attempts reached, falling back to polling');
            this.fallbackToPolling();
            return;
        }

        this.reconnectAttempts++;
        console.log(`Attempting to reconnect (${this.reconnectAttempts}/${this.maxReconnectAttempts})...`);

        setTimeout(() => {
            this.connectWebSockets();
        }, this.reconnectDelay);

        // Exponential backoff
        this.reconnectDelay = Math.min(this.reconnectDelay * 2, 30000);
    }

    fallbackToPolling() {
        console.log('Falling back to permission polling');
        this.startPolling();
    }

    setupFallbackPolling() {
        // Set up polling as a fallback mechanism
        this.startPolling();
    }

    startPolling() {
        if (this.pollingInterval) return; // Already polling

        this.pollingInterval = setInterval(() => {
            if (!this.isConnected) {
                this.checkPermissions();
            }
        }, this.pollingFrequency);
    }

    stopPolling() {
        if (this.pollingInterval) {
            clearInterval(this.pollingInterval);
            this.pollingInterval = null;
        }
    }

    checkPermissions() {
        // Make AJAX request to check for permission changes
        fetch('/settings/api/permissions/check/', {
            method: 'GET',
            headers: {
                'X-Requested-With': 'XMLHttpRequest',
                'X-CSRFToken': this.getCSRFToken()
            }
        })
        .then(response => response.json())
        .then(data => {
            if (data.permissions && JSON.stringify(data.permissions) !== JSON.stringify(this.currentPermissions)) {
                this.updatePermissions(data.permissions);
                this.updateSidebar(data.permissions);

                if (data.message) {
                    this.showNotification(data.message, 'info');
                }
            }
        })
        .catch(error => {
            console.error('Error checking permissions:', error);
        });
    }

    getCSRFToken() {
        const token = document.querySelector('[name=csrfmiddlewaretoken]');
        return token ? token.value : '';
    }

    setupEventListeners() {
        // Handle page visibility changes
        document.addEventListener('visibilitychange', () => {
            if (document.hidden) {
                // Page is hidden, reduce polling frequency
                this.pollingFrequency = 60000; // 1 minute
            } else {
                // Page is visible, restore normal polling
                this.pollingFrequency = 30000; // 30 seconds

                // Check permissions immediately when page becomes visible
                if (!this.isConnected) {
                    this.checkPermissions();
                }
            }
        });

        // Handle window focus/blur
        window.addEventListener('focus', () => {
            if (!this.isConnected) {
                this.checkPermissions();
            }
        });
    }

    // Public method to manually check permissions
    refreshPermissions() {
        if (this.isConnected && this.permissionSocket.readyState === WebSocket.OPEN) {
            this.permissionSocket.send(JSON.stringify({
                type: 'permission_check'
            }));
        } else {
            this.checkPermissions();
        }
    }

    // Cleanup method
    destroy() {
        if (this.permissionSocket) {
            this.permissionSocket.close();
        }
        if (this.notificationSocket) {
            this.notificationSocket.close();
        }
        this.stopPolling();
    }
}

// Initialize permission manager when DOM is ready
document.addEventListener('DOMContentLoaded', function() {
    window.permissionManager = new PermissionManager();
});

// Cleanup on page unload
window.addEventListener('beforeunload', function() {
    if (window.permissionManager) {
        window.permissionManager.destroy();
    }
});
