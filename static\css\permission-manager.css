/* Permission Management Styles */
.permission-notifications-container {
    position: fixed;
    top: 20px;
    right: 20px;
    z-index: 9999;
    max-width: 400px;
    pointer-events: none;
}

.permission-notification {
    background: white;
    border-radius: 8px;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
    margin-bottom: 10px;
    overflow: hidden;
    pointer-events: auto;
    transform: translateX(100%);
    animation: slideInRight 0.3s ease-out forwards;
}

.permission-notification.notification-info {
    border-left: 4px solid #3b82f6;
}

.permission-notification.notification-success {
    border-left: 4px solid #10b981;
}

.permission-notification.notification-warning {
    border-left: 4px solid #f59e0b;
}

.permission-notification.notification-error {
    border-left: 4px solid #ef4444;
}

.notification-content {
    display: flex;
    align-items: center;
    padding: 12px 16px;
    gap: 12px;
}

.notification-content i:first-child {
    font-size: 18px;
    flex-shrink: 0;
}

.notification-info .notification-content i:first-child {
    color: #3b82f6;
}

.notification-success .notification-content i:first-child {
    color: #10b981;
}

.notification-warning .notification-content i:first-child {
    color: #f59e0b;
}

.notification-error .notification-content i:first-child {
    color: #ef4444;
}

.notification-content span {
    flex: 1;
    font-size: 14px;
    color: #374151;
    line-height: 1.4;
}

.notification-close {
    background: none;
    border: none;
    color: #9ca3af;
    cursor: pointer;
    padding: 4px;
    border-radius: 4px;
    transition: all 0.2s ease;
    flex-shrink: 0;
}

.notification-close:hover {
    background: #f3f4f6;
    color: #6b7280;
}

/* Permission update animations */
.permission-updated {
    animation: permissionHighlight 2s ease-out;
}

@keyframes slideInRight {
    from {
        transform: translateX(100%);
        opacity: 0;
    }
    to {
        transform: translateX(0);
        opacity: 1;
    }
}

@keyframes permissionHighlight {
    0% {
        background-color: rgba(59, 130, 246, 0.1);
        transform: scale(1);
    }
    50% {
        background-color: rgba(59, 130, 246, 0.2);
        transform: scale(1.02);
    }
    100% {
        background-color: transparent;
        transform: scale(1);
    }
}

/* Hidden permission items */
.permission-hidden {
    display: none !important;
}

/* WebSocket connection status indicator */
.connection-status {
    position: fixed;
    bottom: 20px;
    right: 20px;
    padding: 8px 12px;
    border-radius: 20px;
    font-size: 12px;
    font-weight: 500;
    z-index: 1000;
    transition: all 0.3s ease;
}

.connection-status.connected {
    background: #10b981;
    color: white;
}

.connection-status.disconnected {
    background: #ef4444;
    color: white;
}

.connection-status.connecting {
    background: #f59e0b;
    color: white;
}

/* Responsive adjustments for notifications */
@media (max-width: 640px) {
    .permission-notifications-container {
        top: 10px;
        right: 10px;
        left: 10px;
        max-width: none;
    }
    
    .permission-notification {
        margin-bottom: 8px;
    }
    
    .notification-content {
        padding: 10px 12px;
        gap: 10px;
    }
    
    .notification-content span {
        font-size: 13px;
    }
}

/* Enhanced sidebar transitions for permission updates */
.dashBoardLinks li {
    transition: all 0.3s ease;
}

.dashBoardLinks li.permission-hidden {
    opacity: 0;
    transform: translateX(-20px);
    max-height: 0;
    overflow: hidden;
    margin: 0;
    padding: 0;
}

/* Permission status indicators */
.permission-status {
    display: inline-block;
    width: 8px;
    height: 8px;
    border-radius: 50%;
    margin-left: 8px;
}

.permission-status.full {
    background-color: #10b981;
}

.permission-status.edit {
    background-color: #3b82f6;
}

.permission-status.view {
    background-color: #f59e0b;
}

.permission-status.none {
    background-color: #ef4444;
}

/* Loading states for permission updates */
.permission-loading {
    position: relative;
    opacity: 0.6;
}

.permission-loading::after {
    content: '';
    position: absolute;
    top: 50%;
    right: 10px;
    width: 12px;
    height: 12px;
    border: 2px solid #3b82f6;
    border-top: 2px solid transparent;
    border-radius: 50%;
    animation: spin 1s linear infinite;
    transform: translateY(-50%);
}

@keyframes spin {
    0% { transform: translateY(-50%) rotate(0deg); }
    100% { transform: translateY(-50%) rotate(360deg); }
}

/* Permission change indicators */
.permission-changed {
    position: relative;
}

.permission-changed::before {
    content: '●';
    position: absolute;
    top: 5px;
    right: 5px;
    color: #10b981;
    font-size: 8px;
    animation: pulse 2s infinite;
}

@keyframes pulse {
    0%, 100% { opacity: 1; }
    50% { opacity: 0.5; }
}

/* Sidebar menu item states for permissions */
.sidebar-link.permission-granted {
    background-color: rgba(16, 185, 129, 0.1);
    border-left: 3px solid #10b981;
}

.sidebar-link.permission-revoked {
    background-color: rgba(239, 68, 68, 0.1);
    border-left: 3px solid #ef4444;
}

.sidebar-link.permission-updated {
    background-color: rgba(59, 130, 246, 0.1);
    border-left: 3px solid #3b82f6;
}

/* Fade out animation for removed permissions */
@keyframes fadeOut {
    from {
        opacity: 1;
        max-height: 100px;
        transform: translateX(0);
    }
    to {
        opacity: 0;
        max-height: 0;
        transform: translateX(-20px);
    }
}

.permission-removing {
    animation: fadeOut 0.5s ease-out forwards;
}

/* Fade in animation for new permissions */
@keyframes fadeIn {
    from {
        opacity: 0;
        max-height: 0;
        transform: translateX(-20px);
    }
    to {
        opacity: 1;
        max-height: 100px;
        transform: translateX(0);
    }
}

.permission-adding {
    animation: fadeIn 0.5s ease-out forwards;
}
