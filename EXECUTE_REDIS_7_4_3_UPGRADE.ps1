# Complete Redis 7.4.3 Upgrade Execution Script
# This script performs the full Redis upgrade from 3.0.504 to 7.4.3

Write-Host "=" * 60 -ForegroundColor Green
Write-Host "Redis 7.4.3 Upgrade Execution for Legend Fitness Club" -ForegroundColor Green
Write-Host "=" * 60 -ForegroundColor Green

# Check if running as administrator
if (-NOT ([Security.Principal.WindowsPrincipal] [Security.Principal.WindowsIdentity]::GetCurrent()).IsInRole([Security.Principal.WindowsBuiltInRole] "Administrator")) {
    Write-Host "ERROR: This script must be run as Administrator!" -ForegroundColor Red
    Write-Host "Please right-click PowerShell and select 'Run as Administrator'" -ForegroundColor Yellow
    Read-Host "Press Enter to exit"
    exit 1
}

Write-Host "✓ Running as Administrator - proceeding with upgrade..." -ForegroundColor Green
Write-Host ""

# Step 1: Check current Redis status
Write-Host "Step 1: Checking current Redis status..." -ForegroundColor Yellow
try {
    $redisService = Get-Service -Name "redis" -ErrorAction SilentlyContinue
    if ($redisService) {
        Write-Host "✓ Found Redis service: $($redisService.Status)" -ForegroundColor Cyan
        if ($redisService.Status -eq "Running") {
            Write-Host "✓ Redis 3.0.504 is currently running" -ForegroundColor Green
        }
    } else {
        Write-Host "⚠ Redis service not found" -ForegroundColor Yellow
    }
} catch {
    Write-Host "⚠ Could not check Redis service status" -ForegroundColor Yellow
}

# Check what's on port 6379
Write-Host "Checking port 6379..." -ForegroundColor Cyan
$port6379 = netstat -an | Select-String ":6379"
if ($port6379) {
    Write-Host "✓ Port 6379 is in use:" -ForegroundColor Green
    $port6379 | ForEach-Object { Write-Host "  $_" -ForegroundColor White }
} else {
    Write-Host "⚠ Port 6379 appears to be free" -ForegroundColor Yellow
}

Write-Host ""

# Step 2: Stop Redis 3.0.504 service
Write-Host "Step 2: Stopping Redis 3.0.504 service..." -ForegroundColor Yellow
try {
    Stop-Service -Name "redis" -Force -ErrorAction Stop
    Write-Host "✓ Redis 3.0.504 service stopped successfully" -ForegroundColor Green
    
    # Wait for service to fully stop
    Start-Sleep -Seconds 3
    
    # Verify it's stopped
    $redisService = Get-Service -Name "redis" -ErrorAction SilentlyContinue
    if ($redisService -and $redisService.Status -eq "Stopped") {
        Write-Host "✓ Confirmed: Redis 3.0.504 service is stopped" -ForegroundColor Green
    }
} catch {
    Write-Host "⚠ Could not stop Redis service: $($_.Exception.Message)" -ForegroundColor Yellow
    Write-Host "Attempting to kill Redis processes..." -ForegroundColor Cyan
    
    try {
        Get-Process -Name "redis-server" -ErrorAction SilentlyContinue | Stop-Process -Force
        Write-Host "✓ Redis processes terminated" -ForegroundColor Green
    } catch {
        Write-Host "⚠ Could not terminate Redis processes" -ForegroundColor Yellow
    }
}

Write-Host ""

# Step 3: Verify Redis 7.4.3 installation
Write-Host "Step 3: Verifying Redis 7.4.3 installation..." -ForegroundColor Yellow
$redis743Path = "C:\Redis-7.4.3"

if (Test-Path $redis743Path) {
    Write-Host "✓ Redis 7.4.3 directory found: $redis743Path" -ForegroundColor Green
    
    # Check for required files
    $requiredFiles = @("redis-server.exe", "redis-cli.exe")
    $allFilesFound = $true
    
    foreach ($file in $requiredFiles) {
        if (Test-Path "$redis743Path\$file") {
            Write-Host "✓ Found: $file" -ForegroundColor Green
        } else {
            Write-Host "✗ Missing: $file" -ForegroundColor Red
            $allFilesFound = $false
        }
    }
    
    if (-not $allFilesFound) {
        Write-Host "ERROR: Required Redis 7.4.3 files are missing!" -ForegroundColor Red
        Read-Host "Press Enter to exit"
        exit 1
    }
} else {
    Write-Host "ERROR: Redis 7.4.3 directory not found at $redis743Path" -ForegroundColor Red
    Read-Host "Press Enter to exit"
    exit 1
}

Write-Host ""

# Step 4: Copy configuration file
Write-Host "Step 4: Setting up Redis 7.4.3 configuration..." -ForegroundColor Yellow
$configSource = "c:\Final Project\legend_fitness_club-gym-ms\redis_7_4_3_config.conf"
$configDest = "$redis743Path\redis.windows-service.conf"

if (Test-Path $configSource) {
    try {
        Copy-Item $configSource $configDest -Force
        Write-Host "✓ Configuration file copied successfully" -ForegroundColor Green
    } catch {
        Write-Host "⚠ Could not copy config file: $($_.Exception.Message)" -ForegroundColor Yellow
        Write-Host "Creating default configuration..." -ForegroundColor Cyan
        
        # Create default config
        $defaultConfig = @"
port 6379
bind 127.0.0.1
timeout 0
tcp-keepalive 60
loglevel notice
logfile "redis-server.log"
databases 16
save 900 1
save 300 10
save 60 10000
maxmemory 1gb
maxmemory-policy allkeys-lru
notify-keyspace-events Ex
lazyfree-lazy-eviction yes
lazyfree-lazy-expire yes
lazyfree-lazy-server-del yes
replica-lazy-flush yes
"@
        $defaultConfig | Out-File -FilePath $configDest -Encoding UTF8
        Write-Host "✓ Default configuration created" -ForegroundColor Green
    }
} else {
    Write-Host "⚠ Source config file not found, creating default..." -ForegroundColor Yellow
    # Create default config (same as above)
    $defaultConfig = @"
port 6379
bind 127.0.0.1
timeout 0
tcp-keepalive 60
loglevel notice
logfile "redis-server.log"
databases 16
save 900 1
save 300 10
save 60 10000
maxmemory 1gb
maxmemory-policy allkeys-lru
notify-keyspace-events Ex
lazyfree-lazy-eviction yes
lazyfree-lazy-expire yes
lazyfree-lazy-server-del yes
replica-lazy-flush yes
"@
    $defaultConfig | Out-File -FilePath $configDest -Encoding UTF8
    Write-Host "✓ Default configuration created" -ForegroundColor Green
}

Write-Host ""

# Step 5: Start Redis 7.4.3 server
Write-Host "Step 5: Starting Redis 7.4.3 server..." -ForegroundColor Yellow
Set-Location $redis743Path

try {
    Write-Host "Starting Redis 7.4.3 server..." -ForegroundColor Cyan
    Write-Host "Command: redis-server.exe redis.windows-service.conf" -ForegroundColor White
    
    # Start Redis 7.4.3 in background
    $redisProcess = Start-Process -FilePath ".\redis-server.exe" -ArgumentList "redis.windows-service.conf" -PassThru -WindowStyle Minimized
    
    if ($redisProcess) {
        Write-Host "✓ Redis 7.4.3 server started (PID: $($redisProcess.Id))" -ForegroundColor Green
        
        # Wait for Redis to initialize
        Write-Host "Waiting for Redis 7.4.3 to initialize..." -ForegroundColor Cyan
        Start-Sleep -Seconds 5
        
    } else {
        Write-Host "✗ Failed to start Redis 7.4.3 server" -ForegroundColor Red
    }
} catch {
    Write-Host "✗ Error starting Redis 7.4.3: $($_.Exception.Message)" -ForegroundColor Red
}

Write-Host ""

# Step 6: Test Redis 7.4.3 connectivity
Write-Host "Step 6: Testing Redis 7.4.3 connectivity..." -ForegroundColor Yellow

try {
    # Test ping
    $pingResult = & .\redis-cli.exe ping 2>$null
    if ($pingResult -eq "PONG") {
        Write-Host "✓ Redis 7.4.3 is responding to ping!" -ForegroundColor Green
        
        # Get version
        $versionInfo = & .\redis-cli.exe info server 2>$null | Select-String "redis_version"
        if ($versionInfo) {
            Write-Host "✓ $versionInfo" -ForegroundColor Green
            
            if ($versionInfo -match "7\.4\.3") {
                Write-Host "✓ CONFIRMED: Redis 7.4.3 is running!" -ForegroundColor Green
            } else {
                Write-Host "⚠ Warning: Version mismatch detected" -ForegroundColor Yellow
            }
        }
        
        # Test basic operations
        & .\redis-cli.exe set test "Redis 7.4.3 Working" >$null 2>&1
        $testResult = & .\redis-cli.exe get test 2>$null
        & .\redis-cli.exe del test >$null 2>&1
        
        if ($testResult -eq "Redis 7.4.3 Working") {
            Write-Host "✓ Redis 7.4.3 basic operations working!" -ForegroundColor Green
        }
        
        # Test keyspace notifications
        $notifications = & .\redis-cli.exe config get notify-keyspace-events 2>$null
        if ($notifications) {
            Write-Host "✓ Keyspace notifications configured: $($notifications[1])" -ForegroundColor Green
        }
        
    } else {
        Write-Host "✗ Redis 7.4.3 is not responding to ping" -ForegroundColor Red
        Write-Host "Ping result: $pingResult" -ForegroundColor Yellow
    }
} catch {
    Write-Host "✗ Error testing Redis 7.4.3: $($_.Exception.Message)" -ForegroundColor Red
}

Write-Host ""

# Step 7: Test Django integration
Write-Host "Step 7: Testing Django integration..." -ForegroundColor Yellow
Set-Location "c:\Final Project\legend_fitness_club-gym-ms"

try {
    Write-Host "Running Django Redis test..." -ForegroundColor Cyan
    $djangoTest = & python manage.py test_redis --detailed 2>&1
    
    if ($djangoTest) {
        Write-Host "Django Redis Test Results:" -ForegroundColor Cyan
        $djangoTest | ForEach-Object { 
            if ($_ -match "Redis 7\.4\.3 detected") {
                Write-Host "✓ $_" -ForegroundColor Green
            } elseif ($_ -match "channels_redis\.core\.RedisChannelLayer") {
                Write-Host "✓ $_" -ForegroundColor Green
            } elseif ($_ -match "✓") {
                Write-Host "$_" -ForegroundColor Green
            } elseif ($_ -match "⚠") {
                Write-Host "$_" -ForegroundColor Yellow
            } else {
                Write-Host "$_" -ForegroundColor White
            }
        }
    }
} catch {
    Write-Host "✗ Error testing Django integration: $($_.Exception.Message)" -ForegroundColor Red
}

Write-Host ""

# Final summary
Write-Host "=" * 60 -ForegroundColor Green
Write-Host "Redis 7.4.3 Upgrade Execution Complete!" -ForegroundColor Green
Write-Host "=" * 60 -ForegroundColor Green

Write-Host "Next Steps:" -ForegroundColor Cyan
Write-Host "1. Start Django server: python manage.py runserver 8000" -ForegroundColor White
Write-Host "2. Check for Redis 7.4.3 detection in startup logs" -ForegroundColor White
Write-Host "3. Test WebSocket functionality in browser" -ForegroundColor White
Write-Host "4. Verify real-time permission updates work" -ForegroundColor White

Write-Host ""
Write-Host "Expected Django startup messages:" -ForegroundColor Cyan
Write-Host "✓ Redis 7.4.3 detected - using Redis channel layer" -ForegroundColor Green
Write-Host "✓ Redis detected - using Redis cache backend" -ForegroundColor Green

Read-Host "`nPress Enter to exit"
