import os
import django
import sys

# Set up Django
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'core.settings')
django.setup()

from django.contrib.auth.hashers import make_password
from django.contrib.auth.models import Group
from user.models import User

def create_system_access_user(employee_id, username, password, email=None):
    """
    Create system access for an existing employee
    """
    try:
        # Get the employee
        employee = User.objects.get(id=employee_id)
        
        # Check if the username already exists
        if User.objects.filter(username=username).exists():
            print(f"Username '{username}' already exists. Please choose a different username.")
            return False
        
        # Determine if this role should have system access
        role = employee.role
        has_system_access = role in ['admin', 'manager', 'cashier', 'coach']
        
        if not has_system_access:
            print(f"Employee role '{role}' cannot have system access. Only manager, cashier, and coach roles can have system access.")
            return False
        
        # Update the employee with user credentials
        employee.username = username
        if email:
            employee.email = email
        employee.password = make_password(password)
        employee.is_manager = (role in ['admin', 'manager'])  # Set is_manager based on role
        employee.is_staff = True  # Give system access
        employee.save()
        
        # Assign to appropriate group based on role
        # Clear existing groups first
        employee.groups.clear()
        
        # Add to appropriate group
        if role == 'admin':
            admin_group, _ = Group.objects.get_or_create(name='admin')
            employee.groups.add(admin_group)
        elif role == 'manager':
            manager_group, _ = Group.objects.get_or_create(name='manager')
            employee.groups.add(manager_group)
        elif role == 'cashier':
            cashier_group, _ = Group.objects.get_or_create(name='cashier')
            employee.groups.add(cashier_group)
        elif role == 'coach':
            coach_group, _ = Group.objects.get_or_create(name='coach')
            employee.groups.add(coach_group)
        
        print(f"System access created successfully for {employee.name} (ID: {employee.id}) with username: {username}")
        return True
    
    except User.DoesNotExist:
        print(f"Employee with ID {employee_id} not found.")
        return False
    except Exception as e:
        print(f"Error creating system access: {str(e)}")
        return False

def main():
    # Define the employees to create system access for
    employees_to_create = [
        {
            'id': 47,
            'username': 'dara_manager',
            'password': 'password123',
            'email': '<EMAIL>'
        },
        {
            'id': 48,
            'username': 'bopha_cashier',
            'password': 'password123',
            'email': '<EMAIL>'
        },
        {
            'id': 49,
            'username': 'sokha_cashier',
            'password': 'password123',
            'email': '<EMAIL>'
        },
        {
            'id': 50,
            'username': 'veasna_coach',
            'password': 'password123',
            'email': '<EMAIL>'
        }
    ]
    
    # Create system access for each employee
    success_count = 0
    for employee in employees_to_create:
        if create_system_access_user(
            employee_id=employee['id'],
            username=employee['username'],
            password=employee['password'],
            email=employee.get('email')
        ):
            success_count += 1
    
    print(f"\nCreated system access for {success_count} out of {len(employees_to_create)} employees.")
    
    # Print all employees with system access
    print("\nEmployees with system access:")
    for user in User.objects.filter(is_employee=True, is_staff=True):
        print(f"ID: {user.id}, Username: {user.username}, Name: {user.name}, Role: {user.role}")

if __name__ == "__main__":
    main()
