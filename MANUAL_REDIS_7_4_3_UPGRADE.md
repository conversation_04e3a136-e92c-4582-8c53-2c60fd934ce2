# Manual Redis 7.4.3 Upgrade Guide

## 🎯 **Current Status**
- ✅ Redis 3.0.504: Running on port 6379
- ✅ Redis 7.4.3: Installed at C:\Redis-7.4.3
- 🎯 Goal: Switch to Redis 7.4.3 for full WebSocket functionality

## 🚀 **Step-by-Step Manual Upgrade**

### **Step 1: Stop Redis 3.0.504 Service**

**Open Command Prompt as Administrator:**
1. Press `Win + X`
2. Select "Command Prompt (Admin)" or "PowerShell (Admin)"
3. Run these commands:

```cmd
sc stop redis
sc query redis
```

**Expected output:** Service should show "STOPPED"

### **Step 2: Create Redis 7.4.3 Configuration**

**Navigate to Redis 7.4.3 directory:**
```cmd
cd C:\Redis-7.4.3
```

**Create configuration file:**
```cmd
notepad redis.windows-service.conf
```

**Copy and paste this configuration:**
```conf
# Redis 7.4.3 Configuration for Legend Fitness Club
port 6379
bind 127.0.0.1
timeout 0
tcp-keepalive 60
tcp-backlog 511

# Logging
loglevel notice
logfile "redis-server.log"

# Database configuration
databases 16

# Persistence
save 900 1
save 300 10
save 60 10000
stop-writes-on-bgsave-error yes
rdbcompression yes
rdbchecksum yes
dbfilename dump.rdb
dir ./

# Memory management
maxmemory 1gb
maxmemory-policy allkeys-lru

# Network and performance
tcp-keepalive 300

# Enable keyspace notifications for Django Channels
notify-keyspace-events Ex

# Redis 7.4.3 optimizations
lazyfree-lazy-eviction yes
lazyfree-lazy-expire yes
lazyfree-lazy-server-del yes
replica-lazy-flush yes
```

**Save and close Notepad**

### **Step 3: Start Redis 7.4.3**

**Option A: Manual Start (Quick Test)**
```cmd
cd C:\Redis-7.4.3
redis-server.exe redis.windows-service.conf
```

**Option B: Install as Windows Service (Recommended)**
```cmd
cd C:\Redis-7.4.3

# If install_redis_service.bat exists:
install_redis_service.bat

# Or manually:
RedisService.exe -install -servicename Redis743 -port 6379 -configfile redis.windows-service.conf
sc start Redis743
```

### **Step 4: Test Redis 7.4.3**

**Test connectivity:**
```cmd
cd C:\Redis-7.4.3
redis-cli.exe ping
```
**Expected:** `PONG`

**Check version:**
```cmd
redis-cli.exe info server | findstr redis_version
```
**Expected:** `redis_version:7.4.3`

**Test basic operations:**
```cmd
redis-cli.exe set test "Redis 7.4.3 Working"
redis-cli.exe get test
redis-cli.exe del test
```

### **Step 5: Test Django Integration**

**Navigate to Django project:**
```cmd
cd "c:\Final Project\legend_fitness_club-gym-ms"
```

**Test Redis integration:**
```cmd
python manage.py test_redis --detailed
```

**Expected output:**
```
✓ Redis 7.4.3 detected - using Redis channel layer
✓ Cache Backend: django_redis.cache.RedisCache
✓ Channel Backend: channels_redis.core.RedisChannelLayer
```

### **Step 6: Start Django Server**

```cmd
python manage.py runserver 8000
```

**Look for these startup messages:**
```
✓ Redis 7.4.3 detected - using Redis channel layer
✓ Redis detected - using Redis cache backend
Starting ASGI/Daphne version 4.1.0 development server
```

### **Step 7: Test WebSocket Functionality**

1. **Open browser:** `http://127.0.0.1:8000/adminDashboard/`
2. **Open Developer Console** (F12)
3. **Look for:** "Permission WebSocket connected"
4. **Test real-time updates:**
   - Go to Settings → Permissions
   - Change a permission setting
   - Watch sidebar update instantly

## 🎊 **Success Indicators**

You'll know the upgrade worked when:

✅ **Redis 7.4.3 responds to ping**
✅ **Django detects Redis 7.4.3**
✅ **WebSocket connections work without errors**
✅ **Real-time permission updates work**
✅ **Performance is noticeably faster**

## 🚨 **Troubleshooting**

### **If Redis 7.4.3 won't start:**
```cmd
# Check if port 6379 is free
netstat -an | findstr :6379

# Kill any remaining Redis processes
taskkill /F /IM redis-server.exe

# Try starting again
cd C:\Redis-7.4.3
redis-server.exe redis.windows-service.conf
```

### **If Django doesn't detect Redis 7.4.3:**
1. Verify Redis is running: `redis-cli.exe ping`
2. Check Redis version: `redis-cli.exe info server`
3. Restart Django server
4. Check Django logs for Redis detection messages

## 📈 **Performance Benefits**

### **Before (Redis 3.0.504):**
- Cache: Redis (fast)
- Channels: In-memory (single server)
- WebSocket: Basic functionality

### **After (Redis 7.4.3):**
- Cache: Redis 7.4.3 (fastest)
- Channels: Redis 7.4.3 (production-level)
- WebSocket: Full Redis pub/sub
- Scalability: Multi-server support
- Features: Latest Redis optimizations

## 🎯 **Quick Commands Summary**

```cmd
# Stop old Redis
sc stop redis

# Start Redis 7.4.3
cd C:\Redis-7.4.3
redis-server.exe redis.windows-service.conf

# Test Redis 7.4.3
redis-cli.exe ping

# Test Django integration
cd "c:\Final Project\legend_fitness_club-gym-ms"
python manage.py test_redis --detailed

# Start Django server
python manage.py runserver 8000
```

**Follow these steps to complete your Redis 7.4.3 upgrade!** 🚀
