{% extends 'base.html' %}
{% load custom_filters %}



{% block head %}
<style>
    /* Additional print styles specific to this template */
    @media print {
        /* Make the print section visible */
        .print-section, .print-section * {
            visibility: visible !important;
            display: block !important;
        }

        /* Hide everything else */
        body > *:not(.main-content),
        .main-content > *:not(.conponentSection),
        .conponentSection > *:not(.componentWrapper),
        .componentWrapper > *:not(.bg-white),
        .bg-white > *:not(.print-section) {
            display: none !important;
        }

        /* Ensure proper positioning */
        body {
            margin: 0 !important;
            padding: 0 !important;
        }

        /* Remove any background colors or borders */
        .receipt-border {
            border: 1px solid #ccc !important;
            background-color: white !important;
            margin: 0 auto !important;
            width: 100% !important;
            max-width: 800px !important;
        }

        /* Add page breaks between slips */
        .page-break {
            page-break-after: always;
        }
    }

    .receipt-border {
        border: 1px solid #ccc;
        padding: 20px;
        border-radius: 5px;
        margin-bottom: 30px;
    }

    .khmer-font {
        font-family: 'Khmer OS', 'Khmer OS System', sans-serif;
    }
</style>
{% endblock %}

{% block body %}
<div class="conponentSection p-5 bg-gray-200">
    <div class="componentWrapper">
        <div class="bg-white p-8 rounded shadow-md">
            <!-- Header with navigation -->
            <div class="flex justify-between items-center mb-4 no-print">
                <h3 class="text-2xl font-bold">Bulk Print Salary Slips</h3>
                <div class="flex space-x-2">
                    <a href="{% url 'payroll:index' %}" class="bg-blue-900 text-white px-4 py-2 rounded">
                        <i class="fa-solid fa-arrow-left mr-2"></i>Back to Salary Payments
                    </a>
                </div>
            </div>

            <!-- Print Button -->
            <div class="mb-6 text-center no-print">
                <button onclick="window.print()" class="bg-blue-900 text-white font-bold py-2 px-6 rounded">Print All Salary Slips ({{ payments|length }})</button>
                <a href="{% url 'payroll:index' %}" class="bg-gray-500 text-white font-bold py-2 px-6 rounded ml-2">Cancel</a>
            </div>

            <!-- Salary Slips -->
            <div class="print-section">
                {% for payment in payments %}
                <div class="receipt-border {% if not forloop.last %}page-break{% endif %}">
                    <!-- Receipt Header -->
                    <div class="text-center mb-6">
                        <h1 class="text-3xl font-bold">LEGEND FITNESS</h1>
                        <p class="text-lg">Salary Slip</p>
                    </div>

                    <!-- Receipt Content -->
                    <div class="border-t border-b border-gray-300 py-4 mb-4">
                        <div class="grid grid-cols-2 gap-4">
                            <div>
                                <p><strong>Payroll ID:</strong> {{ payment.payroll_id }}</p>
                                <p><strong>Employee ID:</strong> {{ payment.employee.emp_id }}</p>
                                <p><strong>Employee Name:</strong> {{ payment.employee.name }}</p>
                                <p><strong>Role:</strong> {{ payment.employee.role|title }}</p>
                            </div>
                            <div>
                                <p><strong>Month:</strong> {{ payment.month|date:"F Y" }}</p>
                                <p><strong>Payment Date:</strong> {{ payment.payment_date|date:"d-M-Y" }}</p>
                                <p><strong>Payment Method:</strong> {{ payment.get_payment_method_display }}</p>
                                <p><strong>Employment Type:</strong> {{ payment.get_employment_type_display }}</p>
                            </div>
                        </div>
                    </div>

                    <!-- Salary Breakdown -->
                    <div class="mb-6">
                        <h2 class="text-xl font-bold mb-3">Salary Breakdown</h2>
                        <table class="w-full">
                            <tr class="border-b">
                                <td class="py-2">Base Salary</td>
                                <td class="py-2 text-right">{{ payment.base_salary|format_khr }}</td>
                            </tr>
                            {% if payment.bonus > 0 %}
                            <tr class="border-b">
                                <td class="py-2">Bonus</td>
                                <td class="py-2 text-right text-green-600">+ {{ payment.bonus|format_khr }}</td>
                            </tr>
                            {% endif %}
                            {% if payment.overtime_hours > 0 %}
                            <tr class="border-b">
                                <td class="py-2">Overtime ({{ payment.overtime_hours }} hours)</td>
                                <td class="py-2 text-right text-green-600">+ 0៛</td>
                            </tr>
                            {% endif %}
                            {% if payment.deduction > 0 %}
                            <tr class="border-b">
                                <td class="py-2">Deductions</td>
                                <td class="py-2 text-right text-red-600">- {{ payment.deduction|format_khr }}</td>
                            </tr>
                            {% endif %}
                            <tr class="border-t-2 border-gray-400 font-bold">
                                <td class="py-3">Final Pay</td>
                                <td class="py-3 text-right text-lg">{{ payment.final_pay|format_khr }}</td>
                            </tr>
                        </table>
                    </div>

                    {% if payment.notes %}
                    <div class="mb-6">
                        <h2 class="text-xl font-bold mb-2">Notes</h2>
                        <p class="bg-gray-50 p-3 rounded">{{ payment.notes }}</p>
                    </div>
                    {% endif %}

                    <!-- Receipt Footer -->
                    <div class="text-center text-sm khmer-font mt-8">
                        <div class="grid grid-cols-2 gap-8 mb-8">
                            <div class="text-center">
                                <p class="mb-12">_________________________</p>
                                <p>Employee Signature</p>
                            </div>
                            <div class="text-center">
                                <p class="mb-12">_________________________</p>
                                <p>Manager Signature</p>
                            </div>
                        </div>
                        <p class="mt-8">Thank you for your service!</p>
                    </div>
                </div>
                {% endfor %}
            </div>
        </div>
    </div>
</div>
{% endblock %}
