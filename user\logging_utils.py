"""
User Action Logging Utilities for Legend Fitness Club
Provides comprehensive logging functionality for security-sensitive operations
"""

from django.utils import timezone
from django.contrib.auth import get_user_model
from .models import UserActionLog
import json

User = get_user_model()


def get_client_ip(request):
    """
    Get the client's IP address from the request
    """
    x_forwarded_for = request.META.get('HTTP_X_FORWARDED_FOR')
    if x_forwarded_for:
        ip = x_forwarded_for.split(',')[0]
    else:
        ip = request.META.get('REMOTE_ADDR')
    return ip


def get_user_agent(request):
    """
    Get the user agent from the request
    """
    return request.META.get('HTTP_USER_AGENT', '')


def log_user_action(user, action_type, module, request=None, status='success', 
                   target_model='', target_id='', target_description='', 
                   before_values=None, after_values=None, description='', 
                   additional_data=None, financial_impact=None):
    """
    Log a user action with comprehensive details
    
    Args:
        user: User performing the action
        action_type: Type of action (from UserActionLog.ACTION_TYPES)
        module: Module where action occurred (from UserActionLog.MODULE_CHOICES)
        request: Django request object (optional)
        status: Action status ('success', 'failed', 'partial')
        target_model: Model name being affected
        target_id: ID of the target object
        target_description: Human-readable description of the target
        before_values: Dict of values before the change
        after_values: Dict of values after the change
        description: Additional description
        additional_data: Any additional data as dict
        financial_impact: Financial impact in KHR
    
    Returns:
        UserActionLog instance
    """
    ip_address = None
    user_agent = ''
    
    if request:
        ip_address = get_client_ip(request)
        user_agent = get_user_agent(request)
    
    return UserActionLog.log_action(
        user=user,
        action_type=action_type,
        module=module,
        status=status,
        target_model=target_model,
        target_id=target_id,
        target_description=target_description,
        before_values=before_values,
        after_values=after_values,
        ip_address=ip_address,
        user_agent=user_agent,
        description=description,
        additional_data=additional_data,
        financial_impact=financial_impact
    )


def log_delete_action(user, request, target_object, module, financial_impact=None, description=''):
    """
    Log a delete action with standardized format
    
    Args:
        user: User performing the delete
        request: Django request object
        target_object: Object being deleted
        module: Module name
        financial_impact: Financial impact in KHR
        description: Additional description
    """
    target_model = target_object.__class__.__name__
    target_id = str(target_object.pk)
    
    # Create target description based on object type
    if hasattr(target_object, 'name'):
        target_description = f"{target_model}: {target_object.name}"
    elif hasattr(target_object, 'username'):
        target_description = f"{target_model}: {target_object.username}"
    elif hasattr(target_object, 'invoice_no'):
        target_description = f"{target_model}: {target_object.invoice_no}"
    elif hasattr(target_object, 'transaction_id'):
        target_description = f"{target_model}: {target_object.transaction_id}"
    elif hasattr(target_object, 'trxId'):
        target_description = f"{target_model}: {target_object.trxId}"
    elif hasattr(target_object, 'bill_id'):
        target_description = f"{target_model}: {target_object.bill_id}"
    elif hasattr(target_object, 'payroll_id'):
        target_description = f"{target_model}: {target_object.payroll_id}"
    else:
        target_description = f"{target_model}: ID {target_id}"
    
    # Determine action type based on model
    action_type_map = {
        'Payment': 'delete_payment',
        'PayPerVisit': 'delete_paypervisit',
        'Sale': 'delete_sale',
        'Transaction': 'delete_finance_transaction',
        'SalaryPayment': 'delete_salary_payment',
        'Bill': 'delete_bill',
        'User': 'delete_user',
    }
    
    action_type = action_type_map.get(target_model, 'other')
    
    return log_user_action(
        user=user,
        action_type=action_type,
        module=module,
        request=request,
        target_model=target_model,
        target_id=target_id,
        target_description=target_description,
        description=description,
        financial_impact=financial_impact
    )


def log_edit_action(user, request, target_object, module, before_values, after_values, 
                   financial_impact=None, description=''):
    """
    Log an edit action with before/after values
    
    Args:
        user: User performing the edit
        request: Django request object
        target_object: Object being edited
        module: Module name
        before_values: Dict of values before the change
        after_values: Dict of values after the change
        financial_impact: Financial impact in KHR
        description: Additional description
    """
    target_model = target_object.__class__.__name__
    target_id = str(target_object.pk)
    
    # Create target description
    if hasattr(target_object, 'name'):
        target_description = f"{target_model}: {target_object.name}"
    elif hasattr(target_object, 'username'):
        target_description = f"{target_model}: {target_object.username}"
    elif hasattr(target_object, 'invoice_no'):
        target_description = f"{target_model}: {target_object.invoice_no}"
    else:
        target_description = f"{target_model}: ID {target_id}"
    
    # Determine action type based on model
    action_type_map = {
        'Payment': 'edit_payment',
        'PayPerVisit': 'edit_paypervisit',
        'Sale': 'edit_sale',
        'Transaction': 'edit_finance_transaction',
        'SalaryPayment': 'edit_salary_payment',
        'Bill': 'edit_bill',
        'User': 'edit_user',
        'Member': 'edit_member_balance',
    }
    
    action_type = action_type_map.get(target_model, 'other')
    
    return log_user_action(
        user=user,
        action_type=action_type,
        module=module,
        request=request,
        target_model=target_model,
        target_id=target_id,
        target_description=target_description,
        before_values=before_values,
        after_values=after_values,
        description=description,
        financial_impact=financial_impact
    )


def log_login_action(user, request, status='success', description=''):
    """
    Log a login action
    """
    return log_user_action(
        user=user,
        action_type='login',
        module='auth',
        request=request,
        status=status,
        description=description
    )


def log_logout_action(user, request, description=''):
    """
    Log a logout action
    """
    return log_user_action(
        user=user,
        action_type='logout',
        module='auth',
        request=request,
        description=description
    )


def log_failed_login(username, request, description=''):
    """
    Log a failed login attempt
    """
    return log_user_action(
        user=None,
        action_type='failed_login',
        module='auth',
        request=request,
        status='failed',
        description=f"Failed login attempt for username: {username}. {description}"
    )


def log_bulk_action(user, request, action_type, module, count, description=''):
    """
    Log a bulk action (like bulk delete)
    """
    return log_user_action(
        user=user,
        action_type=action_type,
        module=module,
        request=request,
        description=f"Bulk action affecting {count} records. {description}",
        additional_data={'affected_count': count}
    )


def log_export_action(user, request, module, export_type, description=''):
    """
    Log a data export action
    """
    return log_user_action(
        user=user,
        action_type='export_data',
        module=module,
        request=request,
        description=f"Data export: {export_type}. {description}",
        additional_data={'export_type': export_type}
    )


def log_settings_change(user, request, setting_name, before_value, after_value, description=''):
    """
    Log a settings change
    """
    return log_user_action(
        user=user,
        action_type='settings_change',
        module='settings',
        request=request,
        target_description=f"Setting: {setting_name}",
        before_values={'value': before_value},
        after_values={'value': after_value},
        description=description
    )
