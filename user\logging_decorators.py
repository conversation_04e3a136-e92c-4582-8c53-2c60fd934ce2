"""
Logging Decorators for User Action Tracking
Provides decorators to automatically log critical operations
"""

from functools import wraps
from django.shortcuts import get_object_or_404
from .logging_utils import log_delete_action, log_user_action


def log_delete_operation(model_class, module_name, get_financial_impact=None):
    """
    Decorator to automatically log delete operations
    
    Args:
        model_class: The model class being deleted
        module_name: The module name for logging
        get_financial_impact: Function to calculate financial impact (optional)
    
    Usage:
        @log_delete_operation(Payment, 'payment', lambda obj: obj.amount_khr)
        def delete_payment(request, pk):
            # delete logic here
    """
    def decorator(view_func):
        @wraps(view_func)
        def wrapper(request, pk, *args, **kwargs):
            # Get the object before deletion
            obj = get_object_or_404(model_class, pk=pk)
            
            # Calculate financial impact if function provided
            financial_impact = None
            if get_financial_impact and callable(get_financial_impact):
                try:
                    financial_impact = get_financial_impact(obj)
                except:
                    financial_impact = None
            
            # Log the delete action before performing it
            log_delete_action(
                user=request.user,
                request=request,
                target_object=obj,
                module=module_name,
                financial_impact=financial_impact,
                description=f"Delete operation via {view_func.__name__}"
            )
            
            # Execute the original view
            return view_func(request, pk, *args, **kwargs)
        
        return wrapper
    return decorator


def log_bulk_delete_operation(module_name):
    """
    Decorator to log bulk delete operations
    
    Args:
        module_name: The module name for logging
    
    Usage:
        @log_bulk_delete_operation('payment')
        def bulk_delete_payments(request):
            # bulk delete logic here
    """
    def decorator(view_func):
        @wraps(view_func)
        def wrapper(request, *args, **kwargs):
            # Extract count information from POST data if available
            selected_ids = request.POST.getlist('selected_items', [])
            count = len(selected_ids)
            
            # Log the bulk delete action
            log_user_action(
                user=request.user,
                action_type='bulk_delete',
                module=module_name,
                request=request,
                description=f"Bulk delete operation affecting {count} items via {view_func.__name__}",
                additional_data={'affected_count': count, 'selected_ids': selected_ids}
            )
            
            # Execute the original view
            return view_func(request, *args, **kwargs)
        
        return wrapper
    return decorator


def log_export_operation(module_name, export_type):
    """
    Decorator to log data export operations
    
    Args:
        module_name: The module name for logging
        export_type: Type of export (e.g., 'csv', 'pdf', 'excel')
    
    Usage:
        @log_export_operation('payment', 'csv')
        def export_payments_csv(request):
            # export logic here
    """
    def decorator(view_func):
        @wraps(view_func)
        def wrapper(request, *args, **kwargs):
            # Log the export action
            log_user_action(
                user=request.user,
                action_type='export_data',
                module=module_name,
                request=request,
                description=f"Data export ({export_type}) via {view_func.__name__}",
                additional_data={'export_type': export_type}
            )
            
            # Execute the original view
            return view_func(request, *args, **kwargs)
        
        return wrapper
    return decorator


def log_report_generation(module_name, report_type):
    """
    Decorator to log report generation
    
    Args:
        module_name: The module name for logging
        report_type: Type of report being generated
    
    Usage:
        @log_report_generation('finance', 'monthly_summary')
        def generate_monthly_report(request):
            # report generation logic here
    """
    def decorator(view_func):
        @wraps(view_func)
        def wrapper(request, *args, **kwargs):
            # Log the report generation
            log_user_action(
                user=request.user,
                action_type='generate_report',
                module=module_name,
                request=request,
                description=f"Report generation ({report_type}) via {view_func.__name__}",
                additional_data={'report_type': report_type}
            )
            
            # Execute the original view
            return view_func(request, *args, **kwargs)
        
        return wrapper
    return decorator


def log_settings_operation(setting_name):
    """
    Decorator to log settings changes
    
    Args:
        setting_name: Name of the setting being changed
    
    Usage:
        @log_settings_operation('gym_settings')
        def update_gym_settings(request):
            # settings update logic here
    """
    def decorator(view_func):
        @wraps(view_func)
        def wrapper(request, *args, **kwargs):
            # Log the settings change
            log_user_action(
                user=request.user,
                action_type='settings_change',
                module='settings',
                request=request,
                target_description=f"Setting: {setting_name}",
                description=f"Settings change via {view_func.__name__}"
            )
            
            # Execute the original view
            return view_func(request, *args, **kwargs)
        
        return wrapper
    return decorator


def log_permission_change():
    """
    Decorator to log permission changes
    
    Usage:
        @log_permission_change()
        def update_user_permissions(request, user_id):
            # permission update logic here
    """
    def decorator(view_func):
        @wraps(view_func)
        def wrapper(request, *args, **kwargs):
            # Log the permission change
            log_user_action(
                user=request.user,
                action_type='change_permissions',
                module='user',
                request=request,
                description=f"Permission change via {view_func.__name__}"
            )
            
            # Execute the original view
            return view_func(request, *args, **kwargs)
        
        return wrapper
    return decorator


def log_template_operation(module_name, operation_type):
    """
    Decorator to log template operations (create, edit, delete)
    
    Args:
        module_name: The module name for logging
        operation_type: Type of operation ('create', 'edit', 'delete')
    
    Usage:
        @log_template_operation('finance', 'delete')
        def delete_template(request, pk):
            # template deletion logic here
    """
    def decorator(view_func):
        @wraps(view_func)
        def wrapper(request, *args, **kwargs):
            # Log the template operation
            log_user_action(
                user=request.user,
                action_type='template_change',
                module=module_name,
                request=request,
                description=f"Template {operation_type} operation via {view_func.__name__}",
                additional_data={'operation_type': operation_type}
            )
            
            # Execute the original view
            return view_func(request, *args, **kwargs)
        
        return wrapper
    return decorator


def log_data_cleanup(module_name):
    """
    Decorator to log data cleanup operations
    
    Args:
        module_name: The module name for logging
    
    Usage:
        @log_data_cleanup('system')
        def clean_all_data(request):
            # data cleanup logic here
    """
    def decorator(view_func):
        @wraps(view_func)
        def wrapper(request, *args, **kwargs):
            # Log the data cleanup operation
            log_user_action(
                user=request.user,
                action_type='clean_data',
                module=module_name,
                request=request,
                description=f"Data cleanup operation via {view_func.__name__}",
                additional_data={'cleanup_type': 'comprehensive'}
            )
            
            # Execute the original view
            return view_func(request, *args, **kwargs)
        
        return wrapper
    return decorator
