/* Responsive Tables Styles */

/* Table container */
.table-container {
    width: 100%;
    overflow-x: auto;
    margin-bottom: 1.5rem;
    border-radius: 0.5rem;
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
}

/* Responsive table */
.responsive-table {
    width: 100%;
    border-collapse: collapse;
    background-color: white;
}

/* Table header */
.responsive-table thead {
    background-color: #1e40af;
    color: white;
}

.responsive-table th {
    padding: 0.75rem 1rem;
    text-align: left;
    font-weight: 600;
    position: relative;
}

/* Table body */
.responsive-table tbody tr {
    border-bottom: 1px solid #e5e7eb;
    transition: background-color 0.2s ease;
}

.responsive-table tbody tr:last-child {
    border-bottom: none;
}

.responsive-table tbody tr:hover {
    background-color: #f9fafb;
}

.responsive-table td {
    padding: 0.75rem 1rem;
    vertical-align: middle;
}

/* Striped rows */
.responsive-table.striped tbody tr:nth-child(even) {
    background-color: #f9fafb;
}

.responsive-table.striped tbody tr:nth-child(even):hover {
    background-color: #f3f4f6;
}

/* Bordered table */
.responsive-table.bordered th,
.responsive-table.bordered td {
    border: 1px solid #e5e7eb;
}

/* Compact table */
.responsive-table.compact th,
.responsive-table.compact td {
    padding: 0.5rem 0.75rem;
}

/* Hover effect */
.responsive-table.hoverable tbody tr:hover {
    background-color: #f3f4f6;
}

/* Sort indicators */
.sort-indicator {
    margin-left: 0.5rem;
    display: inline-block;
    font-size: 0.75rem;
    opacity: 0.7;
}

/* Filter row */
.filter-row th {
    padding: 0.5rem 1rem;
    background-color: #e5e7eb;
}

.filter-input {
    width: 100%;
    padding: 0.375rem 0.5rem;
    border: 1px solid #d1d5db;
    border-radius: 0.25rem;
    font-size: 0.875rem;
}

.filter-input:focus {
    outline: none;
    border-color: #60a5fa;
    box-shadow: 0 0 0 2px rgba(96, 165, 250, 0.2);
}

/* Global filter */
.global-filter-container {
    display: flex;
    align-items: center;
    margin-bottom: 1rem;
    width: 100%;
    max-width: 30rem;
}

.global-filter-input {
    flex: 1;
    padding: 0.5rem 0.75rem;
    border: 1px solid #d1d5db;
    border-radius: 0.375rem;
    font-size: 0.875rem;
}

.global-filter-input:focus {
    outline: none;
    border-color: #60a5fa;
    box-shadow: 0 0 0 2px rgba(96, 165, 250, 0.2);
}

.global-filter-clear {
    background: none;
    border: none;
    color: #6b7280;
    cursor: pointer;
    margin-left: -2rem;
    opacity: 0.7;
    transition: opacity 0.2s ease;
}

.global-filter-clear:hover {
    opacity: 1;
}

/* Column toggle */
.column-toggle-container {
    position: relative;
    display: inline-block;
    margin-right: 1rem;
    margin-bottom: 1rem;
}

.column-toggle-button {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    padding: 0.5rem 0.75rem;
    background-color: #f9fafb;
    border: 1px solid #d1d5db;
    border-radius: 0.375rem;
    font-size: 0.875rem;
    cursor: pointer;
    transition: all 0.2s ease;
}

.column-toggle-button:hover {
    background-color: #f3f4f6;
    border-color: #9ca3af;
}

.column-toggle-dropdown {
    position: absolute;
    top: 100%;
    left: 0;
    z-index: 10;
    display: none;
    min-width: 12rem;
    padding: 0.5rem;
    margin-top: 0.25rem;
    background-color: white;
    border: 1px solid #d1d5db;
    border-radius: 0.375rem;
    box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
}

.column-toggle-dropdown.show {
    display: block;
}

.column-toggle-option {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    padding: 0.375rem 0.5rem;
    border-radius: 0.25rem;
    cursor: pointer;
    transition: background-color 0.2s ease;
}

.column-toggle-option:hover {
    background-color: #f3f4f6;
}

.column-toggle-option input {
    margin: 0;
}

.column-toggle-option label {
    cursor: pointer;
    font-size: 0.875rem;
}

/* Export functionality */
.export-container {
    position: relative;
    display: inline-block;
    margin-bottom: 1rem;
}

.export-button {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    padding: 0.5rem 0.75rem;
    background-color: #1e40af;
    color: white;
    border: none;
    border-radius: 0.375rem;
    font-size: 0.875rem;
    cursor: pointer;
    transition: all 0.2s ease;
}

.export-button:hover {
    background-color: #1c3879;
}

.export-dropdown {
    position: absolute;
    top: 100%;
    left: 0;
    z-index: 10;
    display: none;
    min-width: 10rem;
    padding: 0.5rem;
    margin-top: 0.25rem;
    background-color: white;
    border: 1px solid #d1d5db;
    border-radius: 0.375rem;
    box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
}

.export-dropdown.show {
    display: block;
}

.export-option {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    padding: 0.5rem 0.75rem;
    border-radius: 0.25rem;
    cursor: pointer;
    transition: background-color 0.2s ease;
}

.export-option:hover {
    background-color: #f3f4f6;
}

.export-option i {
    width: 1.25rem;
    text-align: center;
    color: #4b5563;
}

/* Pagination */
.pagination-container {
    display: flex;
    align-items: center;
    justify-content: space-between;
    margin-top: 1rem;
    padding: 0.5rem 0;
}

.pagination-controls {
    display: flex;
    align-items: center;
    gap: 0.25rem;
}

.pagination-button {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 2rem;
    height: 2rem;
    background-color: #f9fafb;
    border: 1px solid #d1d5db;
    border-radius: 0.375rem;
    font-size: 0.875rem;
    cursor: pointer;
    transition: all 0.2s ease;
}

.pagination-button:hover:not(:disabled) {
    background-color: #f3f4f6;
    border-color: #9ca3af;
}

.pagination-button:disabled {
    opacity: 0.5;
    cursor: not-allowed;
}

.pagination-button.active {
    background-color: #1e40af;
    color: white;
    border-color: #1e40af;
}

.pagination-info {
    font-size: 0.875rem;
    color: #6b7280;
}

/* Responsive styles */
@media (max-width: 768px) {
    .responsive-table {
        display: block;
    }
    
    .responsive-table thead,
    .responsive-table tbody,
    .responsive-table th,
    .responsive-table td,
    .responsive-table tr {
        display: block;
    }
    
    .responsive-table thead tr {
        position: absolute;
        top: -9999px;
        left: -9999px;
    }
    
    .responsive-table tr {
        border: 1px solid #e5e7eb;
        margin-bottom: 1rem;
        border-radius: 0.375rem;
        overflow: hidden;
    }
    
    .responsive-table td {
        border: none;
        border-bottom: 1px solid #e5e7eb;
        position: relative;
        padding-left: 50%;
        text-align: right;
    }
    
    .responsive-table td:last-child {
        border-bottom: none;
    }
    
    .responsive-table td:before {
        content: attr(data-label);
        position: absolute;
        top: 0.75rem;
        left: 1rem;
        width: 45%;
        padding-right: 0.5rem;
        white-space: nowrap;
        text-align: left;
        font-weight: 600;
    }
    
    .filter-row,
    .column-toggle-container,
    .export-container {
        display: none;
    }
    
    .pagination-container {
        flex-direction: column;
        gap: 0.5rem;
    }
    
    .pagination-controls {
        order: 2;
    }
    
    .pagination-info {
        order: 1;
    }
}
