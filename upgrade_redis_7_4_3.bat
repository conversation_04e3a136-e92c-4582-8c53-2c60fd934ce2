@echo off
echo Redis 7.4.3 Upgrade for Legend Fitness Club
echo ==========================================
echo.

REM Check if running as administrator
net session >nul 2>&1
if %errorLevel% neq 0 (
    echo This script must be run as Administrator!
    echo Right-click on this file and select "Run as administrator"
    pause
    exit /b 1
)

echo Running as Administrator - proceeding...
echo.

echo Step 1: Stopping Redis 3.0.504 service...
sc stop redis
if %errorlevel% equ 0 (
    echo ✓ Redis 3.0.504 service stopped successfully
    
    echo Waiting for service to fully stop...
    timeout /t 3 /nobreak >nul
    
    echo Checking service status...
    sc query redis
) else (
    echo ⚠ Redis service may not be running or already stopped
)

echo.
echo Step 2: Verifying Redis 7.4.3 installation...

REM Check if Redis 7.4.3 directory exists
if not exist "C:\Redis-7.4.3" (
    echo ✗ Redis 7.4.3 directory not found at C:\Redis-7.4.3
    echo Please ensure Redis 7.4.3 is installed in the correct location
    pause
    exit /b 1
)

echo ✓ Redis 7.4.3 found at C:\Redis-7.4.3

REM Check for essential files
if not exist "C:\Redis-7.4.3\redis-server.exe" (
    echo ✗ redis-server.exe not found
    pause
    exit /b 1
)

if not exist "C:\Redis-7.4.3\redis-cli.exe" (
    echo ✗ redis-cli.exe not found
    pause
    exit /b 1
)

echo ✓ All required Redis 7.4.3 files found

echo.
echo Step 3: Creating Redis 7.4.3 configuration...

REM Create Redis 7.4.3 configuration file
(
echo # Redis 7.4.3 Configuration for Legend Fitness Club
echo # Basic server configuration
echo port 6379
echo bind 127.0.0.1
echo timeout 0
echo tcp-keepalive 60
echo tcp-backlog 511
echo.
echo # Logging
echo loglevel notice
echo logfile "redis-server.log"
echo.
echo # Database configuration
echo databases 16
echo.
echo # Persistence configuration
echo save 900 1
echo save 300 10
echo save 60 10000
echo stop-writes-on-bgsave-error yes
echo rdbcompression yes
echo rdbchecksum yes
echo dbfilename dump.rdb
echo dir ./
echo.
echo # Memory management
echo maxmemory 1gb
echo maxmemory-policy allkeys-lru
echo.
echo # Network and performance
echo tcp-keepalive 300
echo.
echo # Enable keyspace notifications for Django Channels
echo notify-keyspace-events Ex
echo.
echo # Redis 7.4.3 optimizations
echo lazyfree-lazy-eviction yes
echo lazyfree-lazy-expire yes
echo lazyfree-lazy-server-del yes
echo replica-lazy-flush yes
) > "C:\Redis-7.4.3\redis.windows-service.conf"

echo ✓ Redis 7.4.3 configuration file created

echo.
echo Step 4: Installing Redis 7.4.3 as Windows service...

cd /d "C:\Redis-7.4.3"

REM Check if there's a service installer batch file
if exist "install_redis_service.bat" (
    echo Using provided service installer...
    call install_redis_service.bat
) else (
    echo Installing service manually...
    
    REM Remove old Redis service if it exists
    sc delete redis >nul 2>&1
    
    REM Try using RedisService.exe if available
    if exist "RedisService.exe" (
        RedisService.exe -install -servicename Redis743 -port 6379 -configfile redis.windows-service.conf
    ) else (
        REM Fallback to redis-server service installation
        redis-server.exe --service-install redis.windows-service.conf --loglevel verbose --service-name Redis743
    )
)

if %errorlevel% equ 0 (
    echo ✓ Redis 7.4.3 service installed successfully
    
    echo Starting Redis 7.4.3 service...
    sc start Redis743
    
    if %errorlevel% equ 0 (
        echo ✓ Redis 7.4.3 service started successfully
    ) else (
        echo ⚠ Service start failed, trying manual start...
        start "Redis 7.4.3" redis-server.exe redis.windows-service.conf
    )
) else (
    echo ⚠ Service installation failed, trying manual start...
    start "Redis 7.4.3" redis-server.exe redis.windows-service.conf
)

echo.
echo Step 5: Testing Redis 7.4.3...

timeout /t 5 /nobreak >nul

redis-cli.exe ping >nul 2>&1
if %errorlevel% equ 0 (
    echo ✓ Redis 7.4.3 is responding to ping
    
    echo Getting Redis version...
    redis-cli.exe info server | findstr redis_version
    
    echo Testing basic operations...
    redis-cli.exe set test "Redis 7.4.3 Working" >nul 2>&1
    redis-cli.exe get test
    redis-cli.exe del test >nul 2>&1
    
    echo ✓ Redis 7.4.3 basic operations successful
    
    echo Testing keyspace notifications...
    redis-cli.exe config get notify-keyspace-events
    
) else (
    echo ✗ Redis 7.4.3 is not responding
    echo Please check the Redis server manually
)

echo.
echo Step 6: Updating system PATH...

REM Add Redis 7.4.3 to PATH if not already there
echo %PATH% | findstr /C:"C:\Redis-7.4.3" >nul
if %errorlevel% neq 0 (
    setx PATH "%PATH%;C:\Redis-7.4.3" /M >nul 2>&1
    echo ✓ Redis 7.4.3 added to system PATH
) else (
    echo ✓ Redis 7.4.3 already in system PATH
)

echo.
echo ================================================
echo Redis 7.4.3 Upgrade Completed!
echo ================================================
echo Redis 7.4.3 Location: C:\Redis-7.4.3
echo Configuration: C:\Redis-7.4.3\redis.windows-service.conf
echo Service Name: Redis743
echo.
echo Next Steps:
echo 1. Restart your Django server
echo 2. Run: python manage.py test_redis --detailed
echo 3. Check for Redis 7.4.3 detection in Django logs
echo.
echo Expected Django output:
echo "✓ Redis 7.4.3 detected - using Redis channel layer"
echo "✓ Redis detected - using Redis cache backend"
echo.

pause
