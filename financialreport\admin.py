from django.contrib import admin
from .models import ReportTemplate

@admin.register(ReportTemplate)
class ReportTemplateAdmin(admin.ModelAdmin):
    list_display = ('name', 'template_type', 'is_default', 'created_at', 'updated_at')
    list_filter = ('template_type', 'is_default')
    search_fields = ('name',)
    fieldsets = (
        (None, {
            'fields': ('name', 'template_type', 'is_default')
        }),
        ('Styling Options', {
            'fields': ('header_color', 'text_color', 'accent_color', 'background_color', 'table_header_color')
        }),
        ('Content Options', {
            'fields': ('show_logo', 'show_footer', 'footer_text')
        }),
    )
