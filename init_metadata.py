import os
import django

# Set up Django
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'core.settings')
django.setup()

from user.models import MetaData
from django.utils import timezone

# Check if MetaData exists
if not MetaData.objects.exists():
    print("Creating initial MetaData record...")
    MetaData.objects.create(
        lastChecked=timezone.now(),
        funds=0
    )
    print("MetaData record created successfully!")
else:
    print("MetaData record already exists.")
