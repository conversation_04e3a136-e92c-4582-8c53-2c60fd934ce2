import os
import re
import sys

def add_trans_tags(file_path):
    """Add {% trans %} tags to text content in a template file."""
    with open(file_path, 'r', encoding='utf-8', errors='ignore') as f:
        content = f.read()

    # Keep track of original content to check if changes were made
    original_content = content

    # Add translation tags to common HTML elements

    # Headings
    for tag in ['h1', 'h2', 'h3', 'h4', 'h5', 'h6']:
        pattern = f'<{tag}([^>]*)>(.*?)</{tag}>'
        content = re.sub(pattern,
                        lambda m: f'<{tag}{m.group(1)}>{{% trans "{m.group(2).strip()}" %}}</{tag}>'
                        if m.group(2).strip() and not '{%' in m.group(2) else m.group(0),
                        content, flags=re.DOTALL)

    # Labels
    content = re.sub(r'<label([^>]*)>(.*?)</label>',
                    lambda m: f'<label{m.group(1)}>{{% trans "{m.group(2).strip()}" %}}</label>'
                    if m.group(2).strip() and not '{%' in m.group(2) else m.group(0),
                    content, flags=re.DOTALL)

    # Buttons
    content = re.sub(r'<button([^>]*)>(.*?)</button>',
                    lambda m: f'<button{m.group(1)}>{{% trans "{m.group(2).strip()}" %}}</button>'
                    if m.group(2).strip() and not '{%' in m.group(2) else m.group(0),
                    content, flags=re.DOTALL)

    # Spans
    content = re.sub(r'<span([^>]*)>(.*?)</span>',
                    lambda m: f'<span{m.group(1)}>{{% trans "{m.group(2).strip()}" %}}</span>'
                    if m.group(2).strip() and not '{%' in m.group(2) and not '{{' in m.group(2) else m.group(0),
                    content, flags=re.DOTALL)

    # Paragraphs
    content = re.sub(r'<p([^>]*)>(.*?)</p>',
                    lambda m: f'<p{m.group(1)}>{{% trans "{m.group(2).strip()}" %}}</p>'
                    if m.group(2).strip() and not '{%' in m.group(2) and not '{{' in m.group(2) else m.group(0),
                    content, flags=re.DOTALL)

    # Table headers
    content = re.sub(r'<th([^>]*)>(.*?)</th>',
                    lambda m: f'<th{m.group(1)}>{{% trans "{m.group(2).strip()}" %}}</th>'
                    if m.group(2).strip() and not '{%' in m.group(2) else m.group(0),
                    content, flags=re.DOTALL)

    # Options
    content = re.sub(r'<option([^>]*)>(.*?)</option>',
                    lambda m: f'<option{m.group(1)}>{{% trans "{m.group(2).strip()}" %}}</option>'
                    if m.group(2).strip() and not '{%' in m.group(2) else m.group(0),
                    content, flags=re.DOTALL)

    # Links
    content = re.sub(r'<a([^>]*)>(.*?)</a>',
                    lambda m: f'<a{m.group(1)}>{{% trans "{m.group(2).strip()}" %}}</a>'
                    if m.group(2).strip() and not '{%' in m.group(2) and not '{{' in m.group(2) and not '<i' in m.group(2) else m.group(0),
                    content, flags=re.DOTALL)

    # Attributes
    for attr in ['placeholder', 'title', 'alt']:
        content = re.sub(f'{attr}="([^"]*)"',
                        lambda m: f'{attr}="{{% trans "{m.group(1)}" %}}"'
                        if m.group(1).strip() and not '{%' in m.group(1) else m.group(0),
                        content)

    # Check if any changes were made
    if content == original_content:
        print(f"No changes made to {file_path}")
        return False

    # Write the modified content back to the file
    with open(file_path, 'w', encoding='utf-8') as f:
        f.write(content)

    print(f"Added {% trans %} tags to {file_path}")
    return True

def find_html_files(directory):
    """Find all HTML files in the given directory and its subdirectories."""
    html_files = []
    for root, dirs, files in os.walk(directory):
        for file in files:
            if file.endswith('.html'):
                html_files.append(os.path.join(root, file))
    return html_files

def main():
    if len(sys.argv) < 2:
        print("Usage: python add_trans_tags.py <template_path_or_directory>")
        print("  - Provide a specific template file path to process a single file")
        print("  - Provide a directory path to process all HTML files in that directory and subdirectories")
        sys.exit(1)

    path = sys.argv[1]

    if os.path.isfile(path):
        # Process a single file
        if path.endswith('.html'):
            add_trans_tags(path)
        else:
            print(f"Error: {path} is not an HTML file")
    elif os.path.isdir(path):
        # Process all HTML files in the directory and subdirectories
        html_files = find_html_files(path)
        if not html_files:
            print(f"No HTML files found in {path}")
            sys.exit(1)

        processed_count = 0
        for html_file in html_files:
            if add_trans_tags(html_file):
                processed_count += 1

        print(f"Processed {len(html_files)} HTML files. Added trans tags to {processed_count} files.")
    else:
        print(f"Error: {path} does not exist")
        sys.exit(1)

if __name__ == "__main__":
    main()
