import logging
from django.core.cache import cache
from django.conf import settings
from django.utils import timezone
from .models import Role<PERSON>er<PERSON>, MODULE_CHOICES
from .utils import get_user_permissions

# Import channels for WebSocket functionality
from channels.layers import get_channel_layer
from asgiref.sync import async_to_sync
CHANNELS_AVAILABLE = True

logger = logging.getLogger(__name__)

class PermissionCacheManager:
    """
    Manages caching and real-time updates for user permissions
    """

    # Cache key patterns
    USER_PERMISSIONS_KEY = "user_permissions_{user_id}"
    ROLE_PERMISSIONS_KEY = "role_permissions_{role}"
    PERMISSION_CHECK_KEY = "permission_{role}_{module}_{level}"

    # Cache timeout (in seconds) - 30 minutes default
    CACHE_TIMEOUT = getattr(settings, 'PERMISSION_CACHE_TIMEOUT', 1800)

    @classmethod
    def get_user_permissions_cached(cls, user):
        """
        Get user permissions with caching
        """
        cache_key = cls.USER_PERMISSIONS_KEY.format(user_id=user.id)
        permissions = cache.get(cache_key)

        if permissions is None:
            permissions = get_user_permissions(user)
            cache.set(cache_key, permissions, cls.CACHE_TIMEOUT)
            logger.debug(f"Cached permissions for user {user.username}")

        return permissions

    @classmethod
    def get_role_permissions_cached(cls, role):
        """
        Get role permissions with caching
        """
        cache_key = cls.ROLE_PERMISSIONS_KEY.format(role=role)
        permissions = cache.get(cache_key)

        if permissions is None:
            permissions = {}
            for module_code, module_name in MODULE_CHOICES:
                permission_level = RolePermission.get_module_permission(role, module_code)
                permissions[module_code] = permission_level

            cache.set(cache_key, permissions, cls.CACHE_TIMEOUT)
            logger.debug(f"Cached permissions for role {role}")

        return permissions

    @classmethod
    def check_permission_cached(cls, role, module, required_level='view'):
        """
        Check permission with caching
        """
        cache_key = cls.PERMISSION_CHECK_KEY.format(
            role=role, module=module, level=required_level
        )
        result = cache.get(cache_key)

        if result is None:
            result = RolePermission.has_permission(role, module, required_level)
            cache.set(cache_key, result, cls.CACHE_TIMEOUT)
            logger.debug(f"Cached permission check: {role}.{module}.{required_level} = {result}")

        return result

    @classmethod
    def invalidate_user_cache(cls, user_id):
        """
        Invalidate cache for a specific user
        """
        cache_key = cls.USER_PERMISSIONS_KEY.format(user_id=user_id)
        cache.delete(cache_key)
        logger.info(f"Invalidated cache for user {user_id}")

    @classmethod
    def invalidate_role_cache(cls, role):
        """
        Invalidate cache for a specific role
        """
        cache_key = cls.ROLE_PERMISSIONS_KEY.format(role=role)
        cache.delete(cache_key)

        # Also invalidate all permission checks for this role
        for module_code, _ in MODULE_CHOICES:
            for level in ['view', 'edit', 'full']:
                permission_key = cls.PERMISSION_CHECK_KEY.format(
                    role=role, module=module_code, level=level
                )
                cache.delete(permission_key)

        logger.info(f"Invalidated cache for role {role}")

    @classmethod
    def invalidate_all_permission_caches(cls):
        """
        Invalidate all permission-related caches
        """
        # Get all possible cache keys and delete them
        from user.models import User, ROLE_CHOICES

        # Invalidate user caches
        for user in User.objects.all():
            cls.invalidate_user_cache(user.id)

        # Invalidate role caches
        for role_code, _ in ROLE_CHOICES:
            cls.invalidate_role_cache(role_code)

        logger.info("Invalidated all permission caches")

    @classmethod
    def invalidate_all_cache(cls):
        """
        Alias for invalidate_all_permission_caches for consistency
        """
        return cls.invalidate_all_permission_caches()

    @classmethod
    def notify_permission_change(cls, role, changes=None, affected_users=None):
        """
        Notify users of permission changes via WebSocket
        """
        try:
            channel_layer = get_channel_layer()

            if channel_layer is None:
                logger.warning("Channel layer not configured - WebSocket notifications disabled")
                return

            # Get updated permissions for the role
            updated_permissions = cls.get_role_permissions_cached(role)

            # Prepare notification message
            if changes:
                change_messages = []
                for module, change_info in changes.items():
                    old_level = change_info.get('old', 'none')
                    new_level = change_info.get('new', 'none')
                    module_display = module.replace('_', ' ').title()

                    if old_level == 'none' and new_level != 'none':
                        change_messages.append(f"Granted {new_level} access to {module_display}")
                    elif old_level != 'none' and new_level == 'none':
                        change_messages.append(f"Removed access to {module_display}")
                    elif old_level != new_level:
                        change_messages.append(f"Changed {module_display} access from {old_level} to {new_level}")

                notification_message = "Your permissions have been updated: " + "; ".join(change_messages)
            else:
                notification_message = "Your permissions have been updated."

            # Send to role group
            async_to_sync(channel_layer.group_send)(
                f"role_{role}",
                {
                    'type': 'permission_update',
                    'permissions': updated_permissions,
                    'user_role': role,
                    'changes': changes or {},
                    'message': notification_message
                }
            )

            # Send notification to affected users
            if affected_users:
                for user in affected_users:
                    async_to_sync(channel_layer.group_send)(
                        f"user_{user.id}",
                        {
                            'type': 'permission_notification',
                            'message': notification_message,
                            'notification_type': 'info',
                            'changes': changes or {}
                        }
                    )

            logger.info(f"Sent permission change notifications for role {role}")

        except Exception as e:
            logger.error(f"Error sending permission change notifications: {str(e)}")

    @classmethod
    def send_system_notification(cls, message, notification_type='info'):
        """
        Send system-wide notification
        """
        try:
            channel_layer = get_channel_layer()

            if channel_layer is None:
                logger.warning("Channel layer not configured - WebSocket notifications disabled")
                return

            async_to_sync(channel_layer.group_send)(
                "system_notifications",
                {
                    'type': 'system_notification',
                    'message': message,
                    'notification_type': notification_type
                }
            )

            logger.info(f"Sent system notification: {message}")

        except Exception as e:
            logger.error(f"Error sending system notification: {str(e)}")
