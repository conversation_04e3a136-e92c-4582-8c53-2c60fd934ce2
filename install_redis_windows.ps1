# Redis Installation Script for Windows
# Run this script as Administrator in PowerShell

Write-Host "Installing Redis for Windows..." -ForegroundColor Green
Write-Host "===============================" -ForegroundColor Green

# Create Redis directory
$redisDir = "C:\Redis"
if (!(Test-Path $redisDir)) {
    New-Item -ItemType Directory -Path $redisDir -Force
    Write-Host "Created Redis directory: $redisDir" -ForegroundColor Yellow
}

# Download Redis for Windows
$redisUrl = "https://github.com/microsoftarchive/redis/releases/download/win-3.0.504/Redis-x64-3.0.504.zip"
$zipFile = "$redisDir\Redis-x64-3.0.504.zip"

Write-Host "Downloading Redis from GitHub..." -ForegroundColor Yellow
try {
    Invoke-WebRequest -Uri $redisUrl -OutFile $zipFile -UseBasicParsing
    Write-Host "Download completed successfully!" -ForegroundColor Green
} catch {
    Write-Host "Download failed. Please download manually from:" -ForegroundColor Red
    Write-Host $redisUrl -ForegroundColor Red
    Write-Host "Extract to: $redisDir" -ForegroundColor Red
    exit 1
}

# Extract Redis
Write-Host "Extracting Redis..." -ForegroundColor Yellow
try {
    Expand-Archive -Path $zipFile -DestinationPath $redisDir -Force
    Write-Host "Extraction completed!" -ForegroundColor Green
} catch {
    Write-Host "Extraction failed. Please extract manually." -ForegroundColor Red
    exit 1
}

# Add Redis to PATH
$currentPath = [Environment]::GetEnvironmentVariable("PATH", "Machine")
if ($currentPath -notlike "*$redisDir*") {
    [Environment]::SetEnvironmentVariable("PATH", "$currentPath;$redisDir", "Machine")
    Write-Host "Added Redis to system PATH" -ForegroundColor Green
}

# Create Redis configuration file
$redisConf = @"
# Redis Configuration for Legend Fitness Club
port 6379
bind 127.0.0.1
timeout 0
tcp-keepalive 60
loglevel notice
logfile "redis-server.log"
databases 16
save 900 1
save 300 10
save 60 10000
stop-writes-on-bgsave-error yes
rdbcompression yes
rdbchecksum yes
dbfilename dump.rdb
dir ./
maxmemory 256mb
maxmemory-policy allkeys-lru
"@

$redisConf | Out-File -FilePath "$redisDir\redis.windows.conf" -Encoding UTF8
Write-Host "Created Redis configuration file" -ForegroundColor Green

# Create Redis service installation script
$serviceScript = @"
@echo off
echo Installing Redis as Windows Service...
cd /d C:\Redis
redis-server.exe --service-install redis.windows.conf --loglevel verbose
if %errorlevel% equ 0 (
    echo Redis service installed successfully!
    echo Starting Redis service...
    redis-server.exe --service-start
    if %errorlevel% equ 0 (
        echo Redis service started successfully!
        echo Redis is now running on port 6379
    ) else (
        echo Failed to start Redis service
    )
) else (
    echo Failed to install Redis service
)
pause
"@

$serviceScript | Out-File -FilePath "$redisDir\install-redis-service.bat" -Encoding ASCII
Write-Host "Created Redis service installation script" -ForegroundColor Green

# Create Redis start script
$startScript = @"
@echo off
echo Starting Redis Server...
cd /d C:\Redis
start "Redis Server" redis-server.exe redis.windows.conf
echo Redis server started on port 6379
echo Press any key to stop Redis server...
pause
taskkill /f /im redis-server.exe
"@

$startScript | Out-File -FilePath "$redisDir\start-redis.bat" -Encoding ASCII
Write-Host "Created Redis start script" -ForegroundColor Green

# Create Redis test script
$testScript = @"
@echo off
echo Testing Redis Connection...
cd /d C:\Redis
redis-cli.exe ping
if %errorlevel% equ 0 (
    echo Redis is running and responding!
    echo Testing basic operations...
    redis-cli.exe set test "Hello Redis"
    redis-cli.exe get test
    redis-cli.exe del test
    echo Redis test completed successfully!
) else (
    echo Redis is not responding. Please start Redis server first.
)
pause
"@

$testScript | Out-File -FilePath "$redisDir\test-redis.bat" -Encoding ASCII
Write-Host "Created Redis test script" -ForegroundColor Green

Write-Host ""
Write-Host "Redis Installation Completed!" -ForegroundColor Green
Write-Host "==============================" -ForegroundColor Green
Write-Host "Redis Directory: $redisDir" -ForegroundColor Yellow
Write-Host ""
Write-Host "Next Steps:" -ForegroundColor Cyan
Write-Host "1. Run as Administrator: $redisDir\install-redis-service.bat" -ForegroundColor White
Write-Host "   OR" -ForegroundColor Yellow
Write-Host "2. Run manually: $redisDir\start-redis.bat" -ForegroundColor White
Write-Host ""
Write-Host "To test Redis: $redisDir\test-redis.bat" -ForegroundColor White
Write-Host ""
Write-Host "Redis will be available at: 127.0.0.1:6379" -ForegroundColor Green
