{% extends "../base.html" %}
{% load custom_filters %}


{% load user_filters %}

{% block body %}
<!-- component sections starts  -->
<div class="conponentSection p-5 bg-gray-200">
    <div class="componentWrapper">
        <!-- Header with navigation -->
        <div class="flex justify-between items-center mb-4">
            <h3 class="text-2xl font-bold">Edit System User</h3>
            <div class="flex space-x-2">
                <a href="{% url 'user:register' %}" class="bg-blue-900 text-white px-4 py-2 rounded">
                    <i class="fa-solid fa-arrow-left mr-2"></i>Back to User List
                </a>
            </div>
        </div>

        <!-- Form starts  -->
        <div class="formSection bg-white px-10 py-8 rounded shadow-md mb-4">
            <form class="grid grid-cols-2 gap-4" method="post" enctype="multipart/form-data">
                {% csrf_token %}
                <!-- Col 1 -->
                <div class="grid gap-2">
                    <!-- Employee ID -->
                    <label for="empId" class="block text-sm font-medium text-gray-700">ID</label>
                    <input
                        class="border w-full p-4 leading-tight bg-slate-100 disabled:cursor-not-allowed disabled:bg-slate-300"
                        name="empId" type="text" placeholder="Employee ID" disabled value="{{employee.emp_id|default:''}}" />

                    <!-- Username -->
                    <label for="username" class="block text-sm font-medium text-gray-700">Username</label>
                    <input
                        class="border w-full p-4 leading-tight bg-slate-100 disabled:cursor-not-allowed disabled:bg-slate-300"
                        name="username" type="text" placeholder="Username" value="{{employee.username}}" disabled />

                    <!-- Name -->
                    <label for="name" class="block text-sm font-medium text-gray-700">Name</label>
                    <input class="border w-full p-4 leading-tight bg-slate-100" name="name" type="text"
                        placeholder="Name" value="{{employee.name}}" required />

                    <!-- Email -->
                    <label for="email" class="block text-sm font-medium text-gray-700">Email</label>
                    <input class="border w-full p-4 leading-tight bg-slate-100" name="email" type="email"
                        placeholder="Email" value="{{employee.email}}" />
                </div>

                <!-- Col 2 -->
                <div class="grid gap-2">
                    <!-- Role -->
                    <label for="role" class="block text-sm font-medium text-gray-700">Role</label>
                    <select name="role" id="role" class="border w-full p-4 leading-tight bg-slate-100" required>
                        <option value="" hidden>Select Role</option>
                        <option value="cashier" {% if employee.role == 'cashier' %}selected{% endif %}>Cashier</option>
                        <option value="coach" {% if employee.role == 'coach' %}selected{% endif %}>Coach</option>
                    </select>

                    <!-- Password  -->
                    <label for="password" class="block text-sm font-medium text-gray-700">New Password (leave blank to keep current)</label>
                    <input class="border w-full p-4 leading-tight bg-slate-100" name="password" type="text"
                        placeholder="New Password" />

                    <!-- Join Date -->
                    <label for="join_date" class="block text-sm font-medium text-gray-700">Join Date</label>
                    <input
                        class="border w-full p-4 leading-tight bg-slate-100"
                        name="join_date" type="date" value="{% if employee.join_date %}{{employee.join_date|date:'Y-m-d'}}{% endif %}" required />
                </div>

                <!-- Buttons -->
                <div class="col-span-2 grid grid-cols-3 gap-4 mt-4">
                    <div>
                        <button class="bg-blue-900 text-white font-bold py-2 px-4 w-full" type="submit">Update</button>
                    </div>
                    <div>
                        {% if employee.is_active %}
                        <a href="{% url "user:deactivate" employee.id %}"
                            class="bg-yellow-900 text-white font-bold py-2 px-4 w-full inline-block text-center cursor-pointer">Deactivate Account</a>
                        {% else %}
                        <a href="{% url "user:active" employee.id %}"
                            class="bg-green-900 text-white font-bold py-2 px-4 w-full inline-block text-center cursor-pointer">Activate Account</a>
                        {% endif %}
                    </div>
                    <div>
                        <a href="{% url "user:delete" employee.id %}"
                            class="bg-red-900 text-white font-bold py-2 px-4 w-full inline-block text-center cursor-pointer">Delete Account</a>
                    </div>
                </div>
            </form>
        </div>
    </div>
</div>
{% endblock body %}
