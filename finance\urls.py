from django.urls import path
from . import views

app_name = 'finance'

urlpatterns = [
    # Main views
    path('', views.index, name='index'),
    path('deposit/', views.deposit, name='deposit'),
    path('withdraw/', views.withdraw, name='withdraw'),
    path('history/', views.transaction_history, name='history'),
    path('delete/<int:pk>/', views.delete_transaction, name='delete_transaction'),
    path('print/<int:pk>/', views.print_receipt, name='print_receipt'),

    # API endpoints
    path('api/approve-transaction/<int:pk>/', views.approve_transaction, name='approve_transaction'),
    path('api/reject-transaction/<int:pk>/', views.reject_transaction, name='reject_transaction'),

    # Template management
    path('templates/', views.template_list, name='template_list'),
    path('templates/create/', views.create_template, name='create_template'),
    path('templates/edit/<int:pk>/', views.edit_template, name='edit_template'),
    path('templates/preview/<int:pk>/', views.preview_template, name='preview_template'),
    path('templates/delete/<int:pk>/', views.delete_template, name='delete_template'),
]
