# Admin User Protection System

This document explains the protection mechanisms in place to prevent accidental or intentional deletion of critical admin accounts in the Legend Fitness Club system.

## Protected Admin Accounts

The system has been configured to protect specific admin accounts from deletion and deactivation:

- **Developer Admin** (username: `developer`)
  - This account is used for technical maintenance, debugging, and development
  - It has full superuser privileges
  - It CANNOT be deleted or deactivated through the Django admin interface or the user management UI

- **Gym Owner Admin** (username: `owner`)
  - This account is used for business operations and day-to-day management
  - It has full superuser privileges
  - It CANNOT be deleted or deactivated through the Django admin interface or the user management UI

## Protection Mechanisms

The following protection mechanisms have been implemented:

### 1. Django Admin Interface Protection

The Django admin interface has been modified to prevent deletion of protected accounts:

```python
# In user/admin.py
class UserAdmin(admin.ModelAdmin):
    # Protected usernames that cannot be deleted
    PROTECTED_USERNAMES = ['developer', 'owner']

    def has_delete_permission(self, request, obj=None):
        # If no object is specified, allow access to the delete page
        if obj is None:
            return True

        # Check if the username is in the protected list
        if obj.username in self.PROTECTED_USERNAMES:
            return False

        return True
```

### 2. User Management UI Protection

The user management interface has been modified to prevent deletion and deactivation of protected accounts:

```python
# In user/views.py - Delete protection
def delete(request, pk):
    try:
        employee = User.objects.get(id=pk)
        name = employee.name

        # Protected usernames that cannot be deleted
        PROTECTED_USERNAMES = ['developer', 'owner']

        # Check if the username is protected
        if employee.username in PROTECTED_USERNAMES:
            messages.error(request, f"User '{name}' (username: {employee.username}) cannot be deleted as it is a protected system account.")

            # Log the failed attempt
            AdminActionLog.log_action(
                user=request.user,
                action_type='delete_user',
                ip_address=get_client_ip(request),
                target_user=employee,
                description=f"Failed attempt to delete protected user {employee.username}"
            )

            return redirect("/user/")

        # Proceed with deletion for non-protected accounts
        # ...
```

```python
# In user/views.py - Deactivation protection
def deactivate(request, pk):
    try:
        employee = User.objects.get(id=pk)

        # Protected usernames that cannot be deactivated
        PROTECTED_USERNAMES = ['developer', 'owner']

        # Check if the username is protected
        if employee.username in PROTECTED_USERNAMES:
            messages.error(request, f"User '{employee.name}' (username: {employee.username}) cannot be deactivated as it is a protected system account.")

            # Log the failed attempt
            AdminActionLog.log_action(
                user=request.user,
                action_type='deactivate_user',
                ip_address=get_client_ip(request),
                target_user=employee,
                description=f"Failed attempt to deactivate protected user {employee.username}"
            )

            return redirect("/user/")

        # Proceed with deactivation for non-protected accounts
        # ...
```

### 3. Admin Action Logging

All critical admin actions are logged in the system for security auditing:

- User creation
- User deletion
- User activation/deactivation
- Password changes
- Settings changes

These logs include:
- The admin user who performed the action
- The action type
- The target user (if applicable)
- The IP address of the admin user
- A description of the action
- The timestamp of the action

Logs can be viewed at `/user/admin-action-logs/` by users with full access to the user module.

## Admin Account Management

### Developer Admin Account

- **Username**: developer
- **Email**: <EMAIL>
- **Role**: Admin (with full superuser privileges)
- **Purpose**: Technical maintenance, debugging, and development
- **Protection**: Cannot be deleted or deactivated (protected)

### Gym Owner Admin Account

- **Username**: owner
- **Email**: <EMAIL>
- **Role**: Admin (with full superuser privileges)
- **Purpose**: Business operations and day-to-day management
- **Protection**: Cannot be deleted or deactivated (protected)

## Recommended Practices

1. **Do not attempt to delete or deactivate protected admin accounts**
   - These accounts are essential for system maintenance and business operations
   - Deleting or deactivating them could result in loss of access to critical system functions

2. **Use the Gym Owner account for day-to-day operations**
   - The Gym Owner account has full admin privileges
   - It can be used for all business operations

3. **Create additional admin accounts if needed**
   - Additional admin accounts can be created using the `creategymadmin` command
   - Example: `python manage.py creategymadmin --username=newadmin --email=<EMAIL> --password=SecurePassword --name="New Admin"`

4. **Consider deactivating instead of deleting**
   - If an admin account is no longer needed, consider deactivating it instead of deleting it
   - Deactivation preserves the account history while preventing login

5. **Regularly review the admin action logs**
   - Check the logs at `/user/admin-action-logs/` regularly
   - Look for suspicious activities or unauthorized access attempts
   - Investigate any failed attempts to delete or deactivate protected accounts

6. **Use strong, unique passwords for admin accounts**
   - Ensure all admin accounts have strong passwords
   - Change passwords regularly
   - Do not share admin credentials

## Emergency Recovery

If all admin access is lost, you can create a new superuser from the command line:

```bash
python manage.py createsuperuser
python manage.py update_admin_user  # To ensure proper admin attributes
```

## Adding More Protected Accounts

To add more protected accounts, modify the `PROTECTED_USERNAMES` list in both:
- `user/admin.py`
- `user/views.py`

Example:
```python
PROTECTED_USERNAMES = ['developer', 'owner', 'system']
```

## Technical Details

The protection system works through multiple layers of security:

1. **Django Admin Interface Protection**:
   - Overrides the `has_delete_permission` method to prevent deletion of protected accounts
   - Prevents deletion through the Django admin panel

2. **User Management UI Protection**:
   - Adds checks in the delete and deactivate views to prevent actions on protected accounts
   - Prevents deletion and deactivation through the custom user interface

3. **Action Logging System**:
   - Records all critical admin actions in the `AdminActionLog` model
   - Logs include the admin user, action type, target user, IP address, and timestamp
   - Logs cannot be modified through the UI, ensuring audit integrity

4. **IP Address Tracking**:
   - Records the IP address of the admin user for each action
   - Helps identify the source of suspicious activities

5. **Comprehensive Documentation**:
   - Clear documentation of protected accounts and security mechanisms
   - Guidelines for proper admin account management

This multi-layered protection ensures that protected accounts remain secure even if one protection mechanism is bypassed. The action logging system provides an additional layer of security by creating an audit trail of all admin actions.
