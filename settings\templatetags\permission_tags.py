from django import template
from settings.models import RolePermission
from settings.cache_manager import PermissionCacheManager

register = template.Library()

@register.simple_tag
def has_permission(user, module, required_level='view'):
    """
    Check if a user has the required permission level for a module.
    Usage: {% has_permission user 'member' 'edit' as can_edit_members %}

    Args:
        user: The user object
        module (str): The module to check permissions for
        required_level (str): The minimum required permission level ('view', 'edit', or 'full')

    Returns:
        bool: True if the user has the required permission level, False otherwise
    """
    if not user.is_authenticated:
        return False

    # Check if the user has the required permission level for this module using cache
    # Note: Removed admin bypass to respect granular permission settings
    return PermissionCacheManager.check_permission_cached(user.role, module, required_level)

@register.simple_tag
def get_user_permissions(user):
    """
    Get all permissions for a user with caching
    Usage: {% get_user_permissions user as user_perms %}

    Args:
        user: The user object

    Returns:
        dict: Dictionary of module permissions
    """
    if not user.is_authenticated:
        return {}

    return PermissionCacheManager.get_user_permissions_cached(user)
