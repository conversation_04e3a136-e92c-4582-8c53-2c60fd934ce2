{% extends "../base.html" %}



{% block body %}
<!-- component sections starts  -->
<div class="conponentSection p-5 bg-gray-200">
  <div class="componentWrapper">
    <!-- Entry Form starts  -->
    <div class="formSection bg-white p-4 rounded shadow-md mb-4">
      <h3 class="text-2xl font-bold mb-4">New Package Form</h3>
      <form class="grid grid-cols-3 gap-4" method="post">
        {% csrf_token %}
        <!-- Col 1 -->
        <div class="grid gap-2">
          <!-- Name -->
          <input class="border w-full p-4 leading-tight bg-slate-100" name="name" type="text" placeholder="Name"
            required />
          <!-- Price -->
          <input class="border w-full p-4 leading-tight bg-slate-100" name="price" type="number" placeholder="Price"
            required />
        </div>
        <!-- Col 2 -->
        <div class="grid gap-2">
          <!-- Payment Frequency  -->
          <select name="paymentFreq" class="border w-full p-4 leading-tight bg-slate-100">
            <option hidden>Payment Frequency</option>
            {% for fre in FREQUENCY_CHOICES %}
              <option value="{{fre.0}}">{{fre.1}}</option>
            {% endfor %}
          </select>
        </div>
        <!-- Col 3 -->
        <div class="grid gap-2">
          <!-- Address -->
          <textarea class="border w-full p-4 leading-tight bg-slate-100" name="desc" placeholder="Description"
            required></textarea>
        </div>
        <!-- Schedule and Equipment sections removed -->
        <div class="col-span-3">
          <button class="bg-blue-900 text-white font-bold py-2 px-4 w-full" type="submit">Add New Package</button>
        </div>
      </form>
    </div>
    <!-- List section starts  -->
    <div class="bg-gray-50 p-4 shadow-xl rounded-md">
      <div class="relative overflow-x-auto">
        <table class="w-full">
          <thead class="text-sm uppercase bg-blue-900 text-gray-50 text-center"><tr>
              <th scope="col" class="px-6 py-3">Package Name</th>
              <th scope="col" class="px-6 py-3">Price</th>
              <th scope="col" class="px-6 py-3">Payment Frequency</th>
              <th scope="col" class="px-6 py-3">View</th>
            </tr>
          </thead>
          <tbody>
            {% for package in packages %}
              <tr class="bg-white border text-sm text-center">
                  <td class="px-6 py-4">{{package.name}}</td>
                  <td class="px-6 py-4">{{package.price}}</td>
                  <td class="px-6 py-4">{{package.frequency}}</td>
                  <td class="px-6 py-4"><a class="underline" href="{% url "package:edit" package.id %}">View</a></td>
              </tr>
            {% endfor %}
          </tbody>
        </table>
      </div>
    </div>
  </div>
</div>
{% endblock body %}