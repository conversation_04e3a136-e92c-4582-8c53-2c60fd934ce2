<!DOCTYPE html>
{% load static %}
{% load i18n %}

<html lang="{{ LANGUAGE_CODE }}">

<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>{% trans "Legend Fitness Club" %}</title>

  <!-- Google Fonts - Koulen -->
  <link rel="preconnect" href="https://fonts.googleapis.com">
  <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
  <link href="https://fonts.googleapis.com/css2?family=Koulen&display=swap" rel="stylesheet">

  <!-- Tailwind CSS -->
  <script src="https://cdn.tailwindcss.com"></script>

  <!-- Icons -->
  <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/boxicons@latest/css/boxicons.min.css">
  <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.5.1/css/all.min.css" integrity="sha512-DTOQO9RWCH3ppGqcWaEA1BIZOC6xxalwEsw9c2QQeAIftl+Vegovlnee1c9QX4TctnWMn13TZye+giMm8e2LwA==" crossorigin="anonymous" referrerpolicy="no-referrer" />

  <!-- Notifications CSS -->
  <link rel="stylesheet" href="{% static 'css/notifications.css' %}">

  <style>
    .khmer-font {
      font-family: 'Koulen', sans-serif;
    }

    /* Login page specific styles */
    .login-container {
      background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%);
      min-height: 100vh;
    }

    .login-card {
      background: white;
      border-radius: 1rem;
      box-shadow: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
      overflow: hidden;
    }

    .brand-section {
      background: linear-gradient(135deg, #1e3a8a 0%, #1e40af 50%, #2563eb 100%);
      position: relative;
    }

    .brand-section::before {
      content: '';
      position: absolute;
      top: 0;
      left: 0;
      right: 0;
      bottom: 0;
      background: url("{% static 'img/gym-pattern.png' %}") center/cover;
      opacity: 0.1;
    }

    .form-section {
      padding: 3rem 2.5rem;
    }

    .form-input {
      border: 2px solid #e5e7eb;
      border-radius: 0.75rem;
      padding: 1rem 1.25rem;
      font-size: 1rem;
      transition: all 0.3s ease;
      background-color: #f9fafb;
    }

    .form-input:focus {
      outline: none;
      border-color: #2563eb;
      background-color: white;
      box-shadow: 0 0 0 3px rgba(37, 99, 235, 0.1);
    }

    .login-button {
      background: linear-gradient(135deg, #1e3a8a 0%, #2563eb 100%);
      border-radius: 0.75rem;
      padding: 1rem 2rem;
      font-weight: 600;
      font-size: 1.1rem;
      transition: all 0.3s ease;
      box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
    }

    .login-button:hover {
      background: linear-gradient(135deg, #1e40af 0%, #3b82f6 100%);
      transform: translateY(-1px);
      box-shadow: 0 8px 15px -3px rgba(0, 0, 0, 0.1);
    }

    .password-toggle {
      color: #6b7280;
      transition: color 0.2s ease;
    }

    .password-toggle:hover {
      color: #2563eb;
    }

    /* Notification container adjustment for login page */
    .notification-container {
      width: calc(100% - 2rem);
      max-width: 450px;
      margin-right: 0;
    }

    /* Responsive design */
    @media (max-width: 640px) {
      .notification-container {
        width: calc(100% - 2rem);
      }

      .form-section {
        padding: 2rem 1.5rem;
      }

      .login-card {
        margin: 1rem;
        border-radius: 0.75rem;
      }
    }

    @media (min-width: 768px) {
      .form-section {
        padding: 4rem 3rem;
      }
    }

    @media (min-width: 1024px) {
      .login-card {
        max-width: 900px;
      }
    }
  </style>
</head>

<body>
  <!-- Notification Container -->
  <div id="notification-container" class="notification-container"></div>

  <!-- Main Login Container -->
  <div class="login-container flex items-center justify-center p-4">
    <div class="login-card w-full max-w-4xl grid lg:grid-cols-2 shadow-2xl">

      <!-- Brand Section (Left Side) -->
      <div class="brand-section flex flex-col justify-center items-center p-8 lg:p-12 text-white relative z-10">
        <div class="text-center space-y-6">
          <!-- Logo -->
          <div class="w-32 h-32 lg:w-40 lg:h-40 mx-auto mb-6">
            <img class="w-full h-full rounded-full object-cover border-4 border-white/30 shadow-xl"
              src="{% static 'img/logo.jpg' %}"
              alt="{% trans 'Legend Fitness Club Logo' %}">
          </div>

          <!-- Brand Text -->
          <div class="space-y-3">
            <h1 class="text-3xl lg:text-4xl font-bold tracking-wide">
              {% trans "Legend Fitness Club" %}
            </h1>
            <h2 class="text-xl lg:text-2xl font-semibold opacity-90">
              {% trans "Gym Management System" %}
            </h2>
            <h3 class="text-lg lg:text-xl khmer-font opacity-80">
              {% trans "ប្រព័ន្ធគ្រប់គ្រង​ ក្លឹបហាត់ប្រាណ" %}
            </h3>
          </div>

          <!-- Tagline -->
          <div class="mt-8 pt-6 border-t border-white/20">
            <p class="text-sm lg:text-base opacity-75 leading-relaxed">
              {% trans "Professional gym management made simple and efficient" %}
            </p>
          </div>
        </div>
      </div>

      <!-- Form Section (Right Side) -->
      <div class="form-section bg-white">
        <div class="max-w-md mx-auto">
          <!-- Form Header -->
          <div class="text-center mb-8">
            <div class="inline-flex items-center justify-center w-16 h-16 bg-blue-100 rounded-full mb-4">
              <i class="fas fa-user text-2xl text-blue-600"></i>
            </div>
            <h2 class="text-2xl lg:text-3xl font-bold text-gray-800 mb-2">
              {% trans "Welcome Back" %}
            </h2>
            <p class="text-gray-600">
              {% trans "Please sign in to your account" %}
            </p>
          </div>

          <!-- Login Form -->
          <form method="post" class="space-y-6">
            {% csrf_token %}
            <!-- Messages handling -->
            {% if messages %}
              <!-- Messages will be shown as notifications -->
              <div class="mt-4" style="display: none;">
                  {% for message in messages %}
                      <div class="{% if message.tags == 'error' %}bg-red-100 border-l-4 border-red-500 text-red-700{% else %}bg-green-100 border-l-4 border-green-500 text-green-700{% endif %} p-4 mb-2 rounded shadow">
                          {{ message }}
                      </div>
                  {% endfor %}
              </div>

              <!-- Convert Django messages to notifications -->
              <script>
                  document.addEventListener('DOMContentLoaded', function() {
                      {% for message in messages %}
                          {% if 'error' in message.tags %}
                              showNotification('error', '{% trans "Error" %}', '{{ message|escapejs }}', 7000);
                          {% elif 'success' in message.tags %}
                              showNotification('success', '{% trans "Success" %}', '{{ message|escapejs }}', 7000);
                          {% elif 'warning' in message.tags %}
                              showNotification('warning', '{% trans "Warning" %}', '{{ message|escapejs }}', 7000);
                          {% else %}
                              showNotification('info', '{% trans "Information" %}', '{{ message|escapejs }}', 7000);
                          {% endif %}
                      {% endfor %}
                  });
              </script>
            {% endif %}

            <!-- Username Field -->
            <div class="space-y-2">
              <label for="username" class="block text-sm font-semibold text-gray-700">
                {% trans "Username" %}
              </label>
              <div class="relative">
                <div class="absolute inset-y-0 left-0 pl-4 flex items-center pointer-events-none">
                  <i class="fas fa-user text-gray-400"></i>
                </div>
                <input
                  id="username"
                  name="username"
                  type="text"
                  class="form-input w-full pl-12"
                  placeholder="{% trans 'Enter your username' %}"
                  required
                  autocomplete="username" />
              </div>
            </div>

            <!-- Password Field -->
            <div class="space-y-2">
              <label for="password" class="block text-sm font-semibold text-gray-700">
                {% trans "Password" %}
              </label>
              <div class="relative">
                <div class="absolute inset-y-0 left-0 pl-4 flex items-center pointer-events-none">
                  <i class="fas fa-lock text-gray-400"></i>
                </div>
                <input
                  id="password-input"
                  name="password"
                  type="password"
                  class="form-input w-full pl-12 pr-12"
                  placeholder="{% trans 'Enter your password' %}"
                  required
                  autocomplete="current-password" />
                <button
                  type="button"
                  id="toggle-password"
                  class="absolute inset-y-0 right-0 pr-4 flex items-center password-toggle"
                  aria-label="{% trans 'Toggle password visibility' %}">
                  <i id="password-icon" class="fas fa-eye"></i>
                </button>
              </div>
            </div>

            <!-- Login Button -->
            <div class="pt-4">
              <button
                type="submit"
                class="login-button w-full text-white font-semibold text-center transition-all duration-300 hover:shadow-lg focus:outline-none focus:ring-4 focus:ring-blue-300">
                <i class="fas fa-sign-in-alt mr-2"></i>
                {% trans "Sign In" %}
              </button>
            </div>

            <!-- Footer Text -->
            <div class="text-center pt-6 border-t border-gray-200">


            </div>
          </form>
        </div>
      </div>
    </div>
  </div>
  <!-- Include notification script -->
  <script src="{% static 'js/notifications.js' %}"></script>

  <!-- Password toggle functionality -->
  <script>
    document.addEventListener('DOMContentLoaded', function() {
      const passwordInput = document.getElementById('password-input');
      const toggleButton = document.getElementById('toggle-password');
      const passwordIcon = document.getElementById('password-icon');

      if (passwordInput && toggleButton && passwordIcon) {
        toggleButton.addEventListener('click', function() {
          // Toggle password visibility
          if (passwordInput.type === 'password') {
            passwordInput.type = 'text';
            passwordIcon.classList.remove('fa-eye');
            passwordIcon.classList.add('fa-eye-slash');
            toggleButton.setAttribute('aria-label', '{% trans "Hide password" %}');
          } else {
            passwordInput.type = 'password';
            passwordIcon.classList.remove('fa-eye-slash');
            passwordIcon.classList.add('fa-eye');
            toggleButton.setAttribute('aria-label', '{% trans "Show password" %}');
          }
        });
      }
    });
  </script>
</body>

</html>
