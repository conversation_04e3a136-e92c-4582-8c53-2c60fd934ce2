import os
import re
import sys

def add_load_i18n(file_path):
    """Add {% load i18n %} to a template file if it doesn't have it."""
    with open(file_path, 'r', encoding='utf-8', errors='ignore') as f:
        content = f.read()
    
    # Check if i18n is already loaded
    if re.search(r'{%\s*load\s+i18n\s*%}', content):
        return content
    
    # Find the first {% load ... %} tag
    load_match = re.search(r'{%\s*load\s+[^%]+%}', content)
    
    if load_match:
        # Add {% load i18n %} after the existing load tag
        end_pos = load_match.end()
        new_content = content[:end_pos] + '\n{% load i18n %}' + content[end_pos:]
    else:
        # If no load tag exists, add it after the first line (usually DOCTYPE or html tag)
        lines = content.split('\n', 1)
        if len(lines) > 1:
            new_content = lines[0] + '\n{% load i18n %}\n' + lines[1]
        else:
            new_content = '{% load i18n %}\n' + content
    
    print("Added {% load i18n %} to " + file_path)
    return new_content

def process_templates(templates_dir):
    """Add {% load i18n %} to all templates."""
    for root, _, files in os.walk(templates_dir):
        for file in files:
            if file.endswith('.html'):
                file_path = os.path.join(root, file)
                print("Processing " + file_path)
                
                # Add {% load i18n %} tag
                new_content = add_load_i18n(file_path)
                
                # Write the modified content back to the file
                with open(file_path, 'w', encoding='utf-8') as f:
                    f.write(new_content)

if __name__ == "__main__":
    if len(sys.argv) > 1:
        # Process a single template
        template_path = sys.argv[1]
        if os.path.exists(template_path):
            new_content = add_load_i18n(template_path)
            with open(template_path, 'w', encoding='utf-8') as f:
                f.write(new_content)
        else:
            print("Error: File " + template_path + " does not exist")
    else:
        # Process all templates
        process_templates('templates')
