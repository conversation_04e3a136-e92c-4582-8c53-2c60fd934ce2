@echo off
echo Redis 8.0.1 Upgrade for Legend Fitness Club
echo ==========================================
echo.

REM Check if running as administrator
net session >nul 2>&1
if %errorLevel% neq 0 (
    echo This script must be run as Administrator!
    echo Right-click on this file and select "Run as administrator"
    pause
    exit /b 1
)

echo Running as Administrator - proceeding...
echo.

echo Step 1: Stopping Redis 3.0.504 service...
sc stop redis
if %errorlevel% equ 0 (
    echo ✓ Redis 3.0.504 service stopped successfully
    
    echo Waiting for service to fully stop...
    timeout /t 3 /nobreak >nul
    
    echo Checking service status...
    sc query redis
) else (
    echo ⚠ Redis service may not be running or already stopped
)

echo.
echo Step 2: Configuring Redis 8.0.1...

REM Check if Redis 8.0.1 directory exists
if not exist "C:\redis-8.0.1" (
    echo ✗ Redis 8.0.1 directory not found at C:\redis-8.0.1
    echo Please ensure Redis 8.0.1 is installed in the correct location
    pause
    exit /b 1
)

echo ✓ Redis 8.0.1 found at C:\redis-8.0.1

REM Create Redis 8.0.1 configuration file
echo Creating Redis 8.0.1 configuration...

(
echo # Redis 8.0.1 Configuration for Legend Fitness Club
echo # Basic server configuration
echo port 6379
echo bind 127.0.0.1
echo timeout 0
echo tcp-keepalive 60
echo tcp-backlog 511
echo.
echo # Logging
echo loglevel notice
echo logfile "redis-server.log"
echo.
echo # Database configuration
echo databases 16
echo.
echo # Persistence configuration
echo save 900 1
echo save 300 10
echo save 60 10000
echo stop-writes-on-bgsave-error yes
echo rdbcompression yes
echo rdbchecksum yes
echo dbfilename dump.rdb
echo dir ./
echo.
echo # Memory management
echo maxmemory 1gb
echo maxmemory-policy allkeys-lru
echo.
echo # Network and performance
echo tcp-keepalive 300
echo.
echo # Enable keyspace notifications for Django Channels
echo notify-keyspace-events Ex
echo.
echo # Redis 8.0.1 optimizations
echo lazyfree-lazy-eviction yes
echo lazyfree-lazy-expire yes
echo lazyfree-lazy-server-del yes
echo replica-lazy-flush yes
echo.
echo # Security ^(optional^)
echo # requirepass your_password_here
) > "C:\redis-8.0.1\redis.windows-service.conf"

echo ✓ Redis 8.0.1 configuration file created

echo.
echo Step 3: Installing Redis 8.0.1 as Windows service...

cd /d "C:\redis-8.0.1"

REM Remove old Redis service if it exists
redis-server.exe --service-uninstall --service-name redis >nul 2>&1

REM Install Redis 8.0.1 as service
redis-server.exe --service-install redis.windows-service.conf --loglevel verbose --service-name Redis8
if %errorlevel% equ 0 (
    echo ✓ Redis 8.0.1 service installed successfully
    
    echo Starting Redis 8.0.1 service...
    redis-server.exe --service-start --service-name Redis8
    
    if %errorlevel% equ 0 (
        echo ✓ Redis 8.0.1 service started successfully
    ) else (
        echo ✗ Failed to start Redis 8.0.1 service
        echo Trying manual start...
        start "Redis 8.0.1" redis-server.exe redis.windows-service.conf
    )
) else (
    echo ✗ Failed to install Redis 8.0.1 service
    echo Trying manual start...
    start "Redis 8.0.1" redis-server.exe redis.windows-service.conf
)

echo.
echo Step 4: Testing Redis 8.0.1...

timeout /t 3 /nobreak >nul

redis-cli.exe ping >nul 2>&1
if %errorlevel% equ 0 (
    echo ✓ Redis 8.0.1 is responding to ping
    
    echo Getting Redis version...
    redis-cli.exe info server | findstr redis_version
    
    echo Testing basic operations...
    redis-cli.exe set test "Redis 8.0.1 Working" >nul 2>&1
    redis-cli.exe get test
    redis-cli.exe del test >nul 2>&1
    
    echo ✓ Redis 8.0.1 basic operations successful
) else (
    echo ✗ Redis 8.0.1 is not responding
    echo Please check the Redis server manually
)

echo.
echo Step 5: Updating system PATH...

REM Add Redis 8.0.1 to PATH if not already there
echo %PATH% | findstr /C:"C:\redis-8.0.1" >nul
if %errorlevel% neq 0 (
    setx PATH "%PATH%;C:\redis-8.0.1" /M >nul 2>&1
    echo ✓ Redis 8.0.1 added to system PATH
) else (
    echo ✓ Redis 8.0.1 already in system PATH
)

echo.
echo ================================================
echo Redis 8.0.1 Upgrade Completed!
echo ================================================
echo Redis 8.0.1 Location: C:\redis-8.0.1
echo Configuration: C:\redis-8.0.1\redis.windows-service.conf
echo Service Name: Redis8
echo.
echo Next Steps:
echo 1. Restart your Django server
echo 2. Run: python manage.py test_redis --detailed
echo 3. Check for Redis 8.0.1 detection in Django logs
echo.
echo Expected Django output:
echo "✓ Redis 8.0.1 detected - using Redis channel layer"
echo "✓ Redis detected - using Redis cache backend"
echo.

pause
