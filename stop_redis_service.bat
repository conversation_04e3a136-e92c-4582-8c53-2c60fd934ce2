@echo off
echo Stopping Redis 3.0.504 service...
echo This script needs to be run as Administrator

REM Check if running as administrator
net session >nul 2>&1
if %errorLevel% == 0 (
    echo Running as Administrator - proceeding...
    
    echo Stopping Redis service...
    sc stop redis
    
    if %errorlevel% equ 0 (
        echo Redis service stopped successfully!
        
        echo Waiting for service to fully stop...
        timeout /t 3 /nobreak >nul
        
        echo Checking service status...
        sc query redis
        
        echo Redis 3.0.504 has been stopped.
        echo Ready to install Redis 7.4.0
    ) else (
        echo Failed to stop Redis service
        echo Error code: %errorlevel%
    )
) else (
    echo This script must be run as Administrator!
    echo Right-click on this file and select "Run as administrator"
    pause
    exit /b 1
)

pause
