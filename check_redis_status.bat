@echo off
echo ==========================================
echo   Legend Fitness Club - Redis Status
echo ==========================================
echo.

echo Checking Redis server status...
echo.

REM Check if Redis process is running
tasklist /FI "IMAGENAME eq redis-server.exe" 2>NUL | find /I /N "redis-server.exe">NUL
if "%ERRORLEVEL%"=="0" (
    echo ✓ Redis server process is running
    echo.
    
    echo Testing Redis connectivity...
    python manage.py test_redis
    
) else (
    echo ✗ Redis server process is not running
    echo.
    echo To start Redis, run: start_legend_fitness.bat
    echo Or manually: powershell -Command "cd 'C:\Redis-7.4.3'; .\redis-server.exe redis.conf"
)

echo.
echo Current Redis processes:
tasklist /FI "IMAGENAME eq redis-server.exe" 2>NUL

echo.
echo Network connections on port 6379:
netstat -an | findstr :6379

echo.
pause
