from django.core.management.base import BaseCommand
from user.models import User


class Command(BaseCommand):
    help = 'Updates admin users to have the correct fields'

    def handle(self, *args, **options):
        # Get all superusers
        superusers = User.objects.filter(is_superuser=True)
        
        for user in superusers:
            # Update the user
            user.is_employee = True
            user.is_manager = True
            user.role = 'admin'
            user.name = user.name or 'Administrator'
            user.save()
            
            self.stdout.write(self.style.SUCCESS(f'Successfully updated superuser "{user.username}"'))
