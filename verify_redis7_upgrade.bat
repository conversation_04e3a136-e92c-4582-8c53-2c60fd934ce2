@echo off
echo Redis 7.x Verification for Legend Fitness Club
echo =============================================
echo.

cd /d "c:\Final Project\legend_fitness_club-gym-ms"

echo Step 1: Testing Redis 7.x connectivity...
echo.

REM Test Redis connection
C:\Redis7\redis-cli.exe ping >nul 2>&1
if %errorlevel% equ 0 (
    echo ✓ Redis 7.x is responding to ping
    
    REM Get Redis version
    echo ✓ Redis version:
    C:\Redis7\redis-cli.exe info server | findstr redis_version
    echo.
) else (
    echo ✗ Redis 7.x is not responding
    echo Please ensure Redis 7.x is running
    pause
    exit /b 1
)

echo Step 2: Testing Django Redis integration...
echo.

REM Test Django Redis integration
python manage.py test_redis --detailed

echo.
echo Step 3: Checking Django server startup...
echo.

echo Starting Django server to verify Redis 7.x detection...
echo Look for these messages:
echo "✓ Redis 7.x.x detected - using Redis channel layer"
echo "✓ Redis detected - using Redis cache backend"
echo.

echo Press Ctrl+C to stop the server after verification
echo.

python manage.py runserver 8000

pause
