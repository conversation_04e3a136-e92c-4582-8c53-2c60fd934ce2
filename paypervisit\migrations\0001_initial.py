# Generated by Django 5.1.6 on 2025-04-26 03:49

from django.db import migrations, models


class Migration(migrations.Migration):

    initial = True

    dependencies = [
    ]

    operations = [
        migrations.CreateModel(
            name='PayPerVisit',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('trxId', models.CharField(max_length=50, unique=True, verbose_name='Transaction ID')),
                ('amount', models.IntegerField(verbose_name='Amount')),
                ('num_people', models.IntegerField(verbose_name='Number of People')),
                ('date', models.DateTimeField(auto_now_add=True, verbose_name='Date')),
            ],
            options={
                'ordering': ['-date'],
            },
        ),
    ]
