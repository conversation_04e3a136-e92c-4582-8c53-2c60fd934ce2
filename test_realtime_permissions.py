#!/usr/bin/env python
"""
Test script for real-time permission updates
Run this to test if WebSocket notifications are working
"""

import os
import sys
import django

# Setup Django environment
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'core.settings')
django.setup()

from settings.models import RolePermission
from settings.cache_manager import PermissionCacheManager
from user.models import User

def test_permission_update():
    """Test permission update and WebSocket notification"""
    
    print("🧪 Testing Real-Time Permission Updates")
    print("=" * 50)
    
    # Find a coach user for testing
    try:
        coach_user = User.objects.filter(role='coach').first()
        if not coach_user:
            print("❌ No coach user found. Creating one...")
            coach_user = User.objects.create_user(
                username='test_coach',
                password='testpass123',
                role='coach'
            )
            print(f"✅ Created test coach user: {coach_user.username}")
        else:
            print(f"✅ Found coach user: {coach_user.username}")
    except Exception as e:
        print(f"❌ Error finding/creating coach user: {e}")
        return
    
    # Get current permission for 'product' module
    try:
        permission, created = RolePermission.objects.get_or_create(
            role='coach',
            module='product',
            defaults={'permission_level': 'none'}
        )
        
        current_level = permission.permission_level
        print(f"📋 Current permission for coach/product: {current_level}")
        
        # Toggle permission
        new_level = 'view' if current_level == 'none' else 'none'
        print(f"🔄 Changing permission to: {new_level}")
        
        # Update permission (this should trigger WebSocket notification)
        permission.permission_level = new_level
        permission.save()
        
        print(f"✅ Permission updated successfully!")
        print(f"📡 WebSocket notification should have been sent to all coach users")
        print(f"🌐 Check browser console for WebSocket messages")
        
        # Verify cache invalidation
        cached_permissions = PermissionCacheManager.get_user_permissions_cached(coach_user)
        print(f"💾 Cached permissions for coach: {cached_permissions.get('product', 'none')}")
        
        return True
        
    except Exception as e:
        print(f"❌ Error updating permission: {e}")
        return False

def test_websocket_notification():
    """Test direct WebSocket notification"""
    
    print("\n🔔 Testing Direct WebSocket Notification")
    print("=" * 50)
    
    try:
        # Get coach users
        coach_users = User.objects.filter(role='coach')
        
        if not coach_users.exists():
            print("❌ No coach users found for testing")
            return False
        
        # Send test notification
        changes = {
            'product': {
                'old': 'none',
                'new': 'view'
            }
        }
        
        PermissionCacheManager.notify_permission_change(
            role='coach',
            changes=changes,
            affected_users=coach_users
        )
        
        print(f"✅ WebSocket notification sent to {coach_users.count()} coach users")
        print("🌐 Check browser console for notification messages")
        
        return True
        
    except Exception as e:
        print(f"❌ Error sending WebSocket notification: {e}")
        return False

def main():
    """Main test function"""
    
    print("🚀 Legend Fitness Club - Real-Time Permission Test")
    print("=" * 60)
    print("📝 Instructions:")
    print("1. Make sure Django server is running (python manage.py runserver)")
    print("2. Open browser to http://127.0.0.1:8000/adminDashboard/")
    print("3. Login as a coach user")
    print("4. Open browser console (F12)")
    print("5. Run this test script")
    print("6. Watch for WebSocket messages in console")
    print("=" * 60)
    
    # Test 1: Permission update
    test1_result = test_permission_update()
    
    # Test 2: Direct WebSocket notification
    test2_result = test_websocket_notification()
    
    # Summary
    print("\n📊 Test Results Summary")
    print("=" * 30)
    print(f"Permission Update Test: {'✅ PASS' if test1_result else '❌ FAIL'}")
    print(f"WebSocket Notification Test: {'✅ PASS' if test2_result else '❌ FAIL'}")
    
    if test1_result and test2_result:
        print("\n🎉 All tests passed! Real-time permissions should be working.")
        print("💡 If you don't see updates in browser, check:")
        print("   - WebSocket connection in browser console")
        print("   - Redis server is running")
        print("   - Django server is running with ASGI/Daphne")
    else:
        print("\n⚠️  Some tests failed. Check the error messages above.")

if __name__ == "__main__":
    main()
