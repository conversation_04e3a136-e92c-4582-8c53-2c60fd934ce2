{% extends "../base.html" %}



{% block body %}
<div class="conponentSection p-5 bg-gray-200">
    <div class="componentWrapper">
        <!-- Edit Form starts  -->
        <div class="formSection bg-white p-4 rounded shadow-md mb-4">
            <h3 class="text-2xl font-bold mb-4">Edit Supplier: {{ supplier.name }}</h3>
            <form method="post">
                {% csrf_token %}
                <div class="grid grid-cols-2 gap-4">
                    <!-- Name -->
                    <input class="border w-full p-4 leading-tight bg-slate-100" name="name" type="text" placeholder="Supplier Name" value="{{ supplier.name }}" required />
                    <!-- Phone -->
                    <input class="border w-full p-4 leading-tight bg-slate-100" name="phone" type="text" placeholder="Phone Number" value="{{ supplier.phone|default:'' }}" />
                    <!-- Telegram/Messenger -->
                    <input class="border w-full p-4 leading-tight bg-slate-100" name="telegram" type="text" placeholder="Telegram/Messenger" value="{{ supplier.telegram|default:'' }}" />
                    <!-- Address - Full Width -->
                    <div class="col-span-2">
                        <textarea class="border w-full p-4 leading-tight bg-slate-100" name="address" placeholder="Address" rows="3">{{ supplier.address|default:'' }}</textarea>
                    </div>
                    <!-- Note - Full Width -->
                    <div class="col-span-2">
                        <textarea class="border w-full p-4 leading-tight bg-slate-100" name="note" placeholder="Note" rows="3">{{ supplier.note|default:'' }}</textarea>
                    </div>
                    <div class="col-span-2 flex gap-4">
                        <button class="bg-blue-900 text-white font-bold py-2 px-4 flex-1" type="submit">Update Supplier</button>
                        <a href="{% url 'product:suppliers' %}" class="bg-gray-500 text-white font-bold py-2 px-4 flex-1 text-center">Cancel</a>
                    </div>
                </div>
            </form>
        </div>
    </div>
</div>
{% endblock %}
