{% extends 'base.html' %}



{% block body %}
<div class="conponentSection p-5 bg-gray-200">
    <div class="componentWrapper">
        <!-- Header with Add Button -->
        <div class="flex justify-between items-center mb-4">
            <h3 class="text-2xl font-bold">Package Management</h3>
            <a href="{% url 'member:package_create' %}" class="bg-blue-900 text-white font-bold py-2 px-4 rounded">Add New Package</a>
        </div>
        
        <!-- Packages List -->
        <div class="bg-white p-4 rounded shadow-md">
            <div class="relative overflow-x-auto">
                <table class="w-full text-sm text-left">
                    <thead class="text-sm uppercase bg-blue-900 text-gray-50"><tr>
                            <th scope="col" class="px-6 py-3">Package ID</th>
                            <th scope="col" class="px-6 py-3">Name</th>
                            <th scope="col" class="px-6 py-3">Duration</th>
                            <th scope="col" class="px-6 py-3">Price (KHR)</th>
                            <th scope="col" class="px-6 py-3">Price (USD)</th>
                            <th scope="col" class="px-6 py-3">Access Type</th>
                            <th scope="col" class="px-6 py-3">Freeze Policy</th>
                            <th scope="col" class="px-6 py-3">Actions</th>
                        </tr>
                    </thead>
                    <tbody>
                        {% for package in packages %}
                        <tr class="bg-white border">
                            <td class="px-6 py-4">{{ package.package_id }}</td>
                            <td class="px-6 py-4">{{ package.name }}</td>
                            <td class="px-6 py-4">{{ package.duration }} Month{% if package.duration > 1 %}s{% endif %}</td>
                            <td class="px-6 py-4">{{ package.price_khr }}៛</td>
                            <td class="px-6 py-4">{% if package.price_usd %}${{ package.price_usd }}{% else %}-{% endif %}</td>
                            <td class="px-6 py-4">
                                {% if package.access_type == 'all_hours' %}
                                    All Hours
                                {% elif package.access_type == 'peak_only' %}
                                    Peak Hours Only
                                {% endif %}
                            </td>
                            <td class="px-6 py-4">
                                {% if package.freeze_policy %}
                                    <span class="text-green-600">✓</span>
                                {% else %}
                                    <span class="text-red-600">✗</span>
                                {% endif %}
                            </td>
                            <td class="px-6 py-4">
                                <a href="{% url 'member:package_edit' package.id %}" class="text-blue-600 hover:underline mr-2">Edit</a>
                                <a href="{% url 'member:package_delete' package.id %}" class="text-red-600 hover:underline" 
                                   onclick="return confirm('Are you sure you want to delete this package?');">Delete</a>
                            </td>
                        </tr>
                        {% empty %}
                        <tr class="bg-white border">
                            <td colspan="8" class="px-6 py-4 text-center">No packages available.</td>
                        </tr>
                        {% endfor %}
                    </tbody>
                </table>
            </div>
        </div>
        
        <!-- Package Information -->
        <div class="bg-white p-4 rounded shadow-md mt-4">
            <h4 class="text-xl font-bold mb-2">Package Information</h4>
            <p class="mb-2">Packages define the membership options available to members. Each package includes:</p>
            <ul class="list-disc pl-5 mb-4">
                <li><strong>Duration:</strong> The length of the membership in months</li>
                <li><strong>Price:</strong> The cost in KHR and optionally USD</li>
                <li><strong>Access Type:</strong> Whether the member can access the gym at all hours or only during peak hours</li>
                <li><strong>Freeze Policy:</strong> Whether the member can pause their membership temporarily</li>
            </ul>
            <p>Packages are assigned to members during registration and can be changed when renewing membership.</p>
        </div>
    </div>
</div>
{% endblock %}
