from django.db import migrations

class Migration(migrations.Migration):

    dependencies = [
        ('core', '0004_remove_report_tables'),
    ]

    operations = [
        migrations.RunSQL(
            "DROP TABLE IF EXISTS utilitybill_utilityprovider CASCADE;",
            reverse_sql=migrations.RunSQL.noop,
        ),
        migrations.RunSQL(
            "DROP TABLE IF EXISTS utilitybill_utilitybill CASCADE;",
            reverse_sql=migrations.RunSQL.noop,
        ),
    ]
