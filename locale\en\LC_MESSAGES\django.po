# English translation for Legend Fitness Club.
# Copyright (C) 2025 Legend Fitness Club
# This file is distributed under the same license as the Legend Fitness Club package.
#
#, fuzzy
msgid ""
msgstr ""
"Project-Id-Version: Legend Fitness Club 1.0\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2025-05-15 12:00+0700\n"
"PO-Revision-Date: 2025-05-15 12:00+0700\n"
"Last-Translator: \n"
"Language-Team: English\n"
"Language: en\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: 8bit\n"
"Plural-Forms: nplurals=2; plural=(n != 1);\n"

#: templates/partials/header.html:9
msgid "Legend Fitness Club"
msgstr "Legend Fitness Club"

#: templates/includes/language_switcher.html:9
msgid "Language"
msgstr "Language"

#: templates/auth/login.html:14
msgid "Login"
msgstr "Login"

#: templates/auth/login.html:15
msgid "Username"
msgstr "Username"

#: templates/auth/login.html:16
msgid "Password"
msgstr "Password"

#: templates/auth/login.html:17
msgid "Remember me"
msgstr "Remember me"

#: templates/auth/login.html:18
msgid "Forgot Password?"
msgstr "Forgot Password?"

#: templates/sidebar.html:20
msgid "Dashboard"
msgstr "Dashboard"

#: templates/sidebar.html:30
msgid "Pay-per-visit"
msgstr "Pay-per-visit"

#: templates/sidebar.html:40
msgid "Members"
msgstr "Members"

#: templates/sidebar.html:50
msgid "Packages"
msgstr "Packages"

#: templates/sidebar.html:60
msgid "Products"
msgstr "Products"

#: templates/sidebar.html:70
msgid "Payments"
msgstr "Payments"

#: templates/sidebar.html:80
msgid "Staff"
msgstr "Staff"

#: templates/sidebar.html:90
msgid "Reports"
msgstr "Reports"

#: templates/sidebar.html:100
msgid "Settings"
msgstr "Settings"

#: templates/base.html:8
msgid "Gym Management System"
msgstr "Gym Management System"

#: templates/base.html:8
msgid "Legend Fitness"
msgstr "Legend Fitness"

#: templates/dashboard/manager.html:11
msgid "Quick Payment"
msgstr "Quick Payment"

#: templates/dashboard/manager.html:38
msgid "Quick View"
msgstr "Quick View"

#: templates/dashboard/manager.html:69
msgid "Quick Reports"
msgstr "Quick Reports"

#: templates/dashboard/manager.html:25
msgid "Payment"
msgstr "Payment"

#: templates/sidebar.html:490
msgid "Logout"
msgstr "Logout"

#: templates/dashboard/manager.html:42
msgid "Estimated Funds"
msgstr "Estimated Funds"

#: templates/dashboard/manager.html:47
msgid "Products"
msgstr "Products"

#: templates/dashboard/manager.html:52
msgid "Staff"
msgstr "Staff"

#: templates/dashboard/manager.html:57
msgid "Members"
msgstr "Members"

#: templates/dashboard/manager.html:73
msgid "Financial Report"
msgstr "Financial Report"

#: templates/dashboard/manager.html:77
msgid "Member Report"
msgstr "Member Report"

#: templates/dashboard/manager.html:81
msgid "Payment Report"
msgstr "Payment Report"

#: templates/dashboard/manager.html:85
msgid "Inventory Report"
msgstr "Inventory Report"

#: templates/partials/header.html:48
msgid "My Profile"
msgstr "My Profile"

#: templates/partials/header.html:59
msgid "Pay-per-visit Settings"
msgstr "Pay-per-visit Settings"

#: templates/product/index.html:161
msgid "Select CSV File"
msgstr "Select CSV File"

#: templates/product/index.html:165
msgid "CSV should have the following columns:"
msgstr "CSV should have the following columns:"

#: templates/product/index.html:169
msgid "Cancel"
msgstr "Cancel"

#: templates/product/index.html:170
msgid "Import"
msgstr "Import"

#: templates/product/index.html:188
msgid "Bulk Actions"
msgstr "Bulk Actions"

#: templates/product/index.html:189
msgid "Delete Selected"
msgstr "Delete Selected"

#: templates/product/index.html:190
msgid "Set Active"
msgstr "Set Active"

#: templates/product/index.html:191
msgid "Set Inactive"
msgstr "Set Inactive"

#: templates/product/index.html:192
msgid "Reactivate Products with Stock"
msgstr "Reactivate Products with Stock"

#: templates/product/index.html:193
msgid "Export Selected"
msgstr "Export Selected"

#: templates/product/index.html:195
msgid "Apply"
msgstr "Apply"

#: templates/product/index.html:201
msgid "Products"
msgstr "Products"

#: templates/product/index.html:209
msgid "Image"
msgstr "Image"

#: templates/product/index.html:210
msgid "Name"
msgstr "Name"

#: templates/product/index.html:211
msgid "SKU"
msgstr "SKU"

#: templates/product/index.html:212
msgid "Category"
msgstr "Category"

#: templates/product/index.html:213
msgid "Quantity"
msgstr "Quantity"

#: templates/product/index.html:214
msgid "Retail Price"
msgstr "Retail Price"

#: templates/product/index.html:215
msgid "Stock Status"
msgstr "Stock Status"

#: templates/product/index.html:216
msgid "Active"
msgstr "Active"

#: templates/product/index.html:217
msgid "Actions"
msgstr "Actions"

#: templates/product/index.html:231
msgid "No image"
msgstr "No image"

#: templates/product/index.html:253
msgid "Out of Stock"
msgstr "Out of Stock"

#: templates/product/index.html:255
msgid "Low Stock"
msgstr "Low Stock"

#: templates/product/index.html:257
msgid "In Stock"
msgstr "In Stock"

#: templates/product/index.html:262
msgid "Active"
msgstr "Active"

#: templates/product/index.html:264
msgid "Inactive"
msgstr "Inactive"

#: templates/product/index.html:266
msgid "Auto-deactivated (Out of Stock)"
msgstr "Auto-deactivated (Out of Stock)"

# Login error messages
#: core/views.py:70
msgid "Your account has been deactivated. Please contact the administrator."
msgstr "Your account has been deactivated. Please contact the administrator."

#: core/views.py:73
msgid "Incorrect password"
msgstr "Incorrect password"

#: core/views.py:76
msgid "User not found"
msgstr "User not found"

#: core/views.py:80
msgid "Please enter your username"
msgstr "Please enter your username"

#: core/views.py:82
msgid "Please enter your password"
msgstr "Please enter your password"
