{% extends 'base.html' %}



{% block head %}
<style>
    @media print {
        body * {
            visibility: hidden;
        }
        .print-section, .print-section * {
            visibility: visible;
        }
        .print-section {
            position: absolute;
            left: 0;
            top: 0;
            width: 100%;
        }
        .no-print {
            display: none;
        }
    }

    .id-card {
        width: 85.6mm;
        height: 54mm;
        border: 1px solid #000;
        border-radius: 10px;
        padding: 10px;
        margin: 0 auto;
        background-color: #fff;
        box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
        position: relative;
        overflow: hidden;
    }

    .id-card::before {
        content: ;
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        background: url('/static/img/logo.png') no-repeat center;
        background-size: 50%;
        opacity: 0.05;
        z-index: 0;
    }

    .id-card-header {
        text-align: center;
        border-bottom: 2px solid #0f3460;
        padding-bottom: 5px;
        margin-bottom: 10px;
        position: relative;
        z-index: 1;
    }

    .id-card-content {
        display: flex;
        position: relative;
        z-index: 1;
    }

    .id-card-photo {
        width: 25mm;
        height: 30mm;
        border: 1px solid #ccc;
        margin-right: 10px;
        background-color: #f5f5f5;
        display: flex;
        align-items: center;
        justify-content: center;
        overflow: hidden;
    }

    .id-card-photo img {
        max-width: 100%;
        max-height: 100%;
    }

    .id-card-details {
        flex: 1;
        font-size: 12px;
    }

    .id-card-details p {
        margin: 3px 0;
    }

    .id-card-footer {
        text-align: center;
        font-size: 10px;
        margin-top: 5px;
        border-top: 1px solid #ccc;
        padding-top: 5px;
        position: relative;
        z-index: 1;
    }

    .khmer-font {
        font-family: 'Khmer OS', 'Khmer OS System', sans-serif;
    }

    .coach-card {
        width: 85.6mm;
        min-height: 120mm;
        border: 1px solid #000;
        border-radius: 10px;
        padding: 10px;
        margin: 20px auto;
        background-color: #fff;
        box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
        position: relative;
        overflow: hidden;
    }

    .coach-card::before {
        content: ;
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        background: url('/static/img/logo.png') no-repeat center;
        background-size: 50%;
        opacity: 0.05;
        z-index: 0;
    }

    .coach-card-header {
        text-align: center;
        border-bottom: 2px solid #0f3460;
        padding-bottom: 5px;
        margin-bottom: 10px;
        position: relative;
        z-index: 1;
    }

    .coach-card-content {
        position: relative;
        z-index: 1;
    }

    .coach-card-section {
        margin-bottom: 10px;
        border-bottom: 1px dashed #ccc;
        padding-bottom: 5px;
    }

    .coach-card-section h3 {
        font-size: 14px;
        font-weight: bold;
        margin-bottom: 5px;
        color: #0f3460;
    }

    .coach-card-section p {
        font-size: 12px;
        margin: 2px 0;
    }

    .coach-card-footer {
        text-align: center;
        font-size: 10px;
        margin-top: 5px;
        border-top: 1px solid #ccc;
        padding-top: 5px;
        position: relative;
        z-index: 1;
    }


</style>
{% endblock %}

{% block body %}
<div class="conponentSection p-5 bg-gray-200">
    <div class="componentWrapper">
        <div class="bg-white p-8 rounded shadow-md max-w-2xl mx-auto">
            <!-- Card Type Selection -->
            <div class="mb-4 no-print">
                <h2 class="text-xl font-bold mb-2">Select Card Type:</h2>
                <div class="flex space-x-4">
                    <button id="member-card-btn" class="bg-blue-900 text-white font-bold py-2 px-4 rounded">Member Card</button>
                    <button id="coach-card-btn" class="bg-green-700 text-white font-bold py-2 px-4 rounded">Coach View Card</button>
                </div>
            </div>

            <!-- Member ID Card (Default) -->
            <div class="print-section" id="member-card-section">
                <div class="id-card">
                    <!-- ID Card Header -->
                    <div class="id-card-header">
                        <h1 class="text-xl font-bold">LEGEND FITNESS</h1>
                        <p class="text-sm">Member ID Card</p>
                    </div>

                    <!-- ID Card Content -->
                    <div class="id-card-content">
                        <div class="id-card-photo">
                            {% if member.photo %}
                            <img src="{{ member.photo.url }}" alt="{{ member.name }}">
                            {% else %}
                            <div class="text-center text-gray-400">No Photo</div>
                            {% endif %}
                        </div>
                        <div class="id-card-details">
                            <p><strong>ID:</strong> {{ member.member_id }}</p>
                            <p><strong>Name:</strong> {{ member.name }}</p>
                            <p><strong>Package:</strong> {{ member.package.name }}</p>
                            <p><strong>Valid Until:</strong> {{ member.end_date|date:"d-M-Y" }}</p>
                        </div>
                    </div>

                    <!-- ID Card Footer -->
                    <div class="id-card-footer khmer-font">
                        <p>ABA: 012 345 678 | Telegram: @LegendFitness</p>
                        <p>សូមយកកាតនេះមកជាមួយពេលចូលហាត់ប្រាណ</p>
                    </div>
                </div>
            </div>

            <!-- Coach View Card (Hidden by default) -->
            <div class="print-section" id="coach-card-section" style="display: none;">
                <div class="coach-card">
                    <!-- Coach Card Header -->
                    <div class="coach-card-header">
                        <h1 class="text-xl font-bold">LEGEND FITNESS</h1>
                        <p class="text-sm">Coach View - Member Details</p>
                    </div>

                    <!-- Coach Card Content -->
                    <div class="coach-card-content">
                        <!-- Basic Info Section -->
                        <div class="coach-card-section">
                            <div class="flex">
                                <div class="id-card-photo" style="width: 20mm; height: 25mm;">
                                    {% if member.photo %}
                                    <img src="{{ member.photo.url }}" alt="{{ member.name }}">
                                    {% else %}
                                    <div class="text-center text-gray-400">No Photo</div>
                                    {% endif %}
                                </div>
                                <div class="flex-1 ml-2">
                                    <p><strong>ID:</strong> {{ member.member_id }}</p>
                                    <p><strong>Name:</strong> {{ member.name }}</p>
                                    <p><strong>Gender:</strong> {{ member.get_gender_display }}</p>
                                    <p><strong>DOB:</strong> {% if member.dob %}{{ member.dob|date:"d-M-Y" }}{% else %}Not specified{% endif %}</p>
                                    <p><strong>Contact:</strong> {{ member.contact }}</p>
                                </div>
                            </div>
                        </div>

                        <!-- Membership Info Section -->
                        <div class="coach-card-section">
                            <h3>Membership Information</h3>
                            <p><strong>Package:</strong> {{ member.package.name }} ({{ member.package.duration }} months)</p>
                            <p><strong>Start Date:</strong> {{ member.start_date|date:"d-M-Y" }}</p>
                            <p><strong>End Date:</strong> {{ member.end_date|date:"d-M-Y" }}</p>
                            <p><strong>Status:</strong>
                                {% if member.status %}
                                <span style="color: green;">Active</span>
                                {% else %}
                                <span style="color: red;">Inactive</span>
                                {% endif %}
                            </p>
                        </div>


                    </div>

                    <!-- Coach Card Footer -->
                    <div class="coach-card-footer">
                        <p>FOR COACH USE ONLY - CONFIDENTIAL</p>
                        <p>Printed on: {{ now|date:"d-M-Y" }}</p>
                    </div>
                </div>
            </div>

            <!-- Print Button -->
            <div class="mt-6 text-center no-print">
                <button onclick="window.print()" class="bg-blue-900 text-white font-bold py-2 px-6 rounded">Print Card</button>
                <a href="{% url 'member:edit' member.id %}" class="bg-gray-500 text-white font-bold py-2 px-6 rounded ml-2">Back to Member</a>
            </div>
        </div>
    </div>
</div>

<script>
    document.addEventListener('DOMContentLoaded', function() {
        const memberCardBtn = document.getElementById('member-card-btn');
        const coachCardBtn = document.getElementById('coach-card-btn');
        const memberCardSection = document.getElementById('member-card-section');
        const coachCardSection = document.getElementById('coach-card-section');

        memberCardBtn.addEventListener('click', function() {
            memberCardSection.style.display = 'block';
            coachCardSection.style.display = 'none';
            memberCardBtn.classList.add('bg-blue-900');
            memberCardBtn.classList.remove('bg-blue-700');
            coachCardBtn.classList.add('bg-green-700');
            coachCardBtn.classList.remove('bg-green-900');
        });

        coachCardBtn.addEventListener('click', function() {
            memberCardSection.style.display = 'none';
            coachCardSection.style.display = 'block';
            coachCardBtn.classList.add('bg-green-900');
            coachCardBtn.classList.remove('bg-green-700');
            memberCardBtn.classList.add('bg-blue-700');
            memberCardBtn.classList.remove('bg-blue-900');
        });
    });
</script>
{% endblock %}
