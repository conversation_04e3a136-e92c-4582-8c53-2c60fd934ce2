{% extends 'base.html' %}
{% load custom_filters %}

{% block title %}Action Logs - Legend Fitness Club{% endblock %}

{% block body %}
<div class="conponentSection p-5 bg-gray-200">
    <div class="bg-white rounded-lg shadow-lg p-6">
        <!-- Header -->
        <div class="flex flex-col sm:flex-row justify-between items-start sm:items-center mb-6">
            <div>
                <h1 class="text-2xl font-bold text-gray-800 mb-2">🔍 User Action Logs</h1>
                <p class="text-gray-600">Comprehensive audit trail of all user activities</p>
            </div>
            <div class="flex flex-col sm:flex-row gap-2 mt-4 sm:mt-0">
                <a href="{% url 'user:security_dashboard' %}" 
                   class="bg-red-600 hover:bg-red-700 text-white px-4 py-2 rounded-lg text-sm font-medium transition duration-200">
                    🛡️ Security Dashboard
                </a>
                <a href="{% url 'user:export_action_logs' %}{% if request.GET %}?{{ request.GET.urlencode }}{% endif %}" 
                   class="bg-green-600 hover:bg-green-700 text-white px-4 py-2 rounded-lg text-sm font-medium transition duration-200">
                    📊 Export CSV
                </a>
            </div>
        </div>

        <!-- Suspicious Activity Alerts -->
        {% if suspicious_logs %}
        <div class="bg-red-50 border-l-4 border-red-400 p-4 mb-6">
            <div class="flex">
                <div class="flex-shrink-0">
                    <svg class="h-5 w-5 text-red-400" viewBox="0 0 20 20" fill="currentColor">
                        <path fill-rule="evenodd" d="M8.257 3.099c.765-1.36 2.722-1.36 3.486 0l5.58 9.92c.75 1.334-.213 2.98-1.742 2.98H4.42c-1.53 0-2.493-1.646-1.743-2.98l5.58-9.92zM11 13a1 1 0 11-2 0 1 1 0 012 0zm-1-8a1 1 0 00-1 1v3a1 1 0 002 0V6a1 1 0 00-1-1z" clip-rule="evenodd" />
                    </svg>
                </div>
                <div class="ml-3">
                    <h3 class="text-sm font-medium text-red-800">⚠️ Suspicious Activity Detected</h3>
                    <div class="mt-2 text-sm text-red-700">
                        <p>{{ suspicious_logs|length }} suspicious activit{{ suspicious_logs|length|pluralize:"y,ies" }} detected on this page. Review immediately.</p>
                    </div>
                </div>
            </div>
        </div>
        {% endif %}

        <!-- Filters -->
        <div class="bg-gray-50 rounded-lg p-4 mb-6">
            <form method="GET" class="space-y-4">
                <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
                    <!-- User Filter -->
                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-1">User</label>
                        <select name="user" class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500">
                            <option value="">All Users</option>
                            {% for user in users %}
                            <option value="{{ user.id }}" {% if filters.user == user.id|stringformat:"s" %}selected{% endif %}>
                                {{ user.username }} ({{ user.name }})
                            </option>
                            {% endfor %}
                        </select>
                    </div>

                    <!-- Action Type Filter -->
                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-1">Action Type</label>
                        <select name="action_type" class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500">
                            <option value="">All Actions</option>
                            {% for value, label in action_types %}
                            <option value="{{ value }}" {% if filters.action_type == value %}selected{% endif %}>{{ label }}</option>
                            {% endfor %}
                        </select>
                    </div>

                    <!-- Module Filter -->
                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-1">Module</label>
                        <select name="module" class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500">
                            <option value="">All Modules</option>
                            {% for value, label in modules %}
                            <option value="{{ value }}" {% if filters.module == value %}selected{% endif %}>{{ label }}</option>
                            {% endfor %}
                        </select>
                    </div>

                    <!-- Status Filter -->
                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-1">Status</label>
                        <select name="status" class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500">
                            <option value="">All Statuses</option>
                            {% for value, label in statuses %}
                            <option value="{{ value }}" {% if filters.status == value %}selected{% endif %}>{{ label }}</option>
                            {% endfor %}
                        </select>
                    </div>
                </div>

                <div class="grid grid-cols-1 md:grid-cols-3 gap-4">
                    <!-- Date Range -->
                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-1">Start Date</label>
                        <input type="date" name="start_date" value="{{ filters.start_date }}" 
                               class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500">
                    </div>
                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-1">End Date</label>
                        <input type="date" name="end_date" value="{{ filters.end_date }}" 
                               class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500">
                    </div>

                    <!-- Search -->
                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-1">Search</label>
                        <input type="text" name="search" value="{{ filters.search }}" placeholder="Search descriptions, IDs, IPs..." 
                               class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500">
                    </div>
                </div>

                <div class="flex flex-col sm:flex-row gap-2">
                    <button type="submit" class="bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded-md text-sm font-medium transition duration-200">
                        🔍 Apply Filters
                    </button>
                    <a href="{% url 'user:action_logs' %}" class="bg-gray-500 hover:bg-gray-600 text-white px-4 py-2 rounded-md text-sm font-medium transition duration-200 text-center">
                        🔄 Clear Filters
                    </a>
                </div>
            </form>
        </div>

        <!-- Results Summary -->
        <div class="mb-4">
            <p class="text-sm text-gray-600">
                Showing {{ page_obj.start_index }}-{{ page_obj.end_index }} of {{ page_obj.paginator.count }} action logs
                {% if suspicious_logs %}
                <span class="text-red-600 font-medium">({{ suspicious_logs|length }} suspicious)</span>
                {% endif %}
            </p>
        </div>

        <!-- Action Logs Table -->
        <div class="overflow-x-auto">
            <table class="min-w-full bg-white border border-gray-200">
                <thead class="bg-gray-50">
                    <tr>
                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">User</th>
                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Action</th>
                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Target</th>
                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Time</th>
                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Status</th>
                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Financial Impact</th>
                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Actions</th>
                    </tr>
                </thead>
                <tbody class="bg-white divide-y divide-gray-200">
                    {% for log in page_obj %}
                    <tr class="hover:bg-gray-50 {% if log in suspicious_logs %}bg-red-50 border-l-4 border-red-400{% endif %}">
                        <td class="px-6 py-4 whitespace-nowrap">
                            <div class="flex items-center">
                                <div>
                                    <div class="text-sm font-medium text-gray-900">
                                        {{ log.user.username|default:"Unknown" }}
                                    </div>
                                    <div class="text-sm text-gray-500">{{ log.user.name|default:"N/A" }}</div>
                                </div>
                            </div>
                        </td>
                        <td class="px-6 py-4 whitespace-nowrap">
                            <div class="text-sm text-gray-900">{{ log.get_action_type_display }}</div>
                            <div class="text-sm text-gray-500">{{ log.get_module_display }}</div>
                        </td>
                        <td class="px-6 py-4">
                            <div class="text-sm text-gray-900">{{ log.target_description|truncatechars:50 }}</div>
                            {% if log.target_id %}
                            <div class="text-sm text-gray-500">ID: {{ log.target_id }}</div>
                            {% endif %}
                        </td>
                        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                            {{ log.action_time|date:"M d, Y H:i" }}
                        </td>
                        <td class="px-6 py-4 whitespace-nowrap">
                            <span class="px-2 inline-flex text-xs leading-5 font-semibold rounded-full 
                                {% if log.status == 'success' %}bg-green-100 text-green-800
                                {% elif log.status == 'failed' %}bg-red-100 text-red-800
                                {% else %}bg-yellow-100 text-yellow-800{% endif %}">
                                {{ log.get_status_display }}
                            </span>
                        </td>
                        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                            {% if log.financial_impact %}
                                <span class="font-medium">{{ log.get_financial_impact_display }}</span>
                            {% else %}
                                <span class="text-gray-400">No impact</span>
                            {% endif %}
                        </td>
                        <td class="px-6 py-4 whitespace-nowrap text-sm font-medium">
                            <a href="{% url 'user:action_log_detail' log.pk %}" 
                               class="text-blue-600 hover:text-blue-900">View Details</a>
                            {% if log in suspicious_logs %}
                            <span class="ml-2 text-red-600 font-bold">⚠️ SUSPICIOUS</span>
                            {% endif %}
                        </td>
                    </tr>
                    {% empty %}
                    <tr>
                        <td colspan="7" class="px-6 py-4 text-center text-gray-500">
                            No action logs found matching your criteria.
                        </td>
                    </tr>
                    {% endfor %}
                </tbody>
            </table>
        </div>

        <!-- Pagination -->
        {% if page_obj.has_other_pages %}
        <div class="bg-white px-4 py-3 flex items-center justify-between border-t border-gray-200 sm:px-6 mt-4">
            <div class="flex-1 flex justify-between sm:hidden">
                {% if page_obj.has_previous %}
                <a href="?page={{ page_obj.previous_page_number }}{% if request.GET %}&{{ request.GET.urlencode }}{% endif %}" 
                   class="relative inline-flex items-center px-4 py-2 border border-gray-300 text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50">
                    Previous
                </a>
                {% endif %}
                {% if page_obj.has_next %}
                <a href="?page={{ page_obj.next_page_number }}{% if request.GET %}&{{ request.GET.urlencode }}{% endif %}" 
                   class="ml-3 relative inline-flex items-center px-4 py-2 border border-gray-300 text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50">
                    Next
                </a>
                {% endif %}
            </div>
            <div class="hidden sm:flex-1 sm:flex sm:items-center sm:justify-between">
                <div>
                    <p class="text-sm text-gray-700">
                        Showing <span class="font-medium">{{ page_obj.start_index }}</span> to <span class="font-medium">{{ page_obj.end_index }}</span> of <span class="font-medium">{{ page_obj.paginator.count }}</span> results
                    </p>
                </div>
                <div>
                    <nav class="relative z-0 inline-flex rounded-md shadow-sm -space-x-px" aria-label="Pagination">
                        {% if page_obj.has_previous %}
                        <a href="?page={{ page_obj.previous_page_number }}{% if request.GET %}&{{ request.GET.urlencode }}{% endif %}" 
                           class="relative inline-flex items-center px-2 py-2 rounded-l-md border border-gray-300 bg-white text-sm font-medium text-gray-500 hover:bg-gray-50">
                            Previous
                        </a>
                        {% endif %}
                        
                        {% for num in page_obj.paginator.page_range %}
                        {% if page_obj.number == num %}
                        <span class="relative inline-flex items-center px-4 py-2 border border-gray-300 bg-blue-50 text-sm font-medium text-blue-600">
                            {{ num }}
                        </span>
                        {% elif num > page_obj.number|add:'-3' and num < page_obj.number|add:'3' %}
                        <a href="?page={{ num }}{% if request.GET %}&{{ request.GET.urlencode }}{% endif %}" 
                           class="relative inline-flex items-center px-4 py-2 border border-gray-300 bg-white text-sm font-medium text-gray-700 hover:bg-gray-50">
                            {{ num }}
                        </a>
                        {% endif %}
                        {% endfor %}
                        
                        {% if page_obj.has_next %}
                        <a href="?page={{ page_obj.next_page_number }}{% if request.GET %}&{{ request.GET.urlencode }}{% endif %}" 
                           class="relative inline-flex items-center px-2 py-2 rounded-r-md border border-gray-300 bg-white text-sm font-medium text-gray-500 hover:bg-gray-50">
                            Next
                        </a>
                        {% endif %}
                    </nav>
                </div>
            </div>
        </div>
        {% endif %}
    </div>
</div>
{% endblock %}
