{% extends 'base.html' %}
{% load custom_filters %}

{% block title %}Security Dashboard - Legend Fitness Club{% endblock %}

{% block body %}
<div class="conponentSection p-5 bg-gray-200">
    <div class="bg-white rounded-lg shadow-lg p-6">
        <!-- Header -->
        <div class="flex flex-col sm:flex-row justify-between items-start sm:items-center mb-6">
            <div>
                <h1 class="text-2xl font-bold text-gray-800 mb-2">🛡️ Security Dashboard</h1>
                <p class="text-gray-600">Monitor suspicious activities and user risk scores</p>
            </div>
            <div class="flex gap-2 mt-4 sm:mt-0">
                <a href="{% url 'user:action_logs' %}" 
                   class="bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded-lg text-sm font-medium transition duration-200">
                    📋 View All Logs
                </a>
                <a href="{% url 'user:export_action_logs' %}" 
                   class="bg-green-600 hover:bg-green-700 text-white px-4 py-2 rounded-lg text-sm font-medium transition duration-200">
                    📊 Export Data
                </a>
            </div>
        </div>

        <!-- Security Statistics -->
        <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
            <div class="bg-blue-50 rounded-lg p-6">
                <div class="flex items-center">
                    <div class="flex-shrink-0">
                        <div class="w-8 h-8 bg-blue-500 rounded-full flex items-center justify-center">
                            <span class="text-white text-sm font-bold">📊</span>
                        </div>
                    </div>
                    <div class="ml-4">
                        <p class="text-sm font-medium text-blue-600">Total Actions Today</p>
                        <p class="text-2xl font-bold text-blue-900">{{ stats.total_actions_today }}</p>
                    </div>
                </div>
            </div>

            <div class="bg-red-50 rounded-lg p-6">
                <div class="flex items-center">
                    <div class="flex-shrink-0">
                        <div class="w-8 h-8 bg-red-500 rounded-full flex items-center justify-center">
                            <span class="text-white text-sm font-bold">🗑️</span>
                        </div>
                    </div>
                    <div class="ml-4">
                        <p class="text-sm font-medium text-red-600">Delete Actions Today</p>
                        <p class="text-2xl font-bold text-red-900">{{ stats.delete_actions_today }}</p>
                    </div>
                </div>
            </div>

            <div class="bg-yellow-50 rounded-lg p-6">
                <div class="flex items-center">
                    <div class="flex-shrink-0">
                        <div class="w-8 h-8 bg-yellow-500 rounded-full flex items-center justify-center">
                            <span class="text-white text-sm font-bold">🚫</span>
                        </div>
                    </div>
                    <div class="ml-4">
                        <p class="text-sm font-medium text-yellow-600">Failed Logins Today</p>
                        <p class="text-2xl font-bold text-yellow-900">{{ stats.failed_logins_today }}</p>
                    </div>
                </div>
            </div>

            <div class="bg-green-50 rounded-lg p-6">
                <div class="flex items-center">
                    <div class="flex-shrink-0">
                        <div class="w-8 h-8 bg-green-500 rounded-full flex items-center justify-center">
                            <span class="text-white text-sm font-bold">👥</span>
                        </div>
                    </div>
                    <div class="ml-4">
                        <p class="text-sm font-medium text-green-600">Active Users Today</p>
                        <p class="text-2xl font-bold text-green-900">{{ stats.unique_users_today }}</p>
                    </div>
                </div>
            </div>
        </div>

        <div class="grid grid-cols-1 lg:grid-cols-2 gap-8">
            <!-- Suspicious Activities -->
            <div class="bg-red-50 rounded-lg p-6">
                <h2 class="text-lg font-semibold text-red-800 mb-4">⚠️ Recent Suspicious Activities</h2>
                {% if suspicious_activities %}
                <div class="space-y-4 max-h-96 overflow-y-auto">
                    {% for activity in suspicious_activities %}
                    <div class="bg-white rounded-lg p-4 border-l-4 border-red-400">
                        <div class="flex justify-between items-start">
                            <div class="flex-1">
                                <div class="flex items-center mb-2">
                                    <span class="text-sm font-medium text-gray-900">
                                        {{ activity.log.user.username|default:"Unknown" }}
                                    </span>
                                    <span class="ml-2 px-2 py-1 text-xs bg-red-100 text-red-800 rounded-full">
                                        {{ activity.log.get_action_type_display }}
                                    </span>
                                </div>
                                <p class="text-sm text-gray-600 mb-2">{{ activity.log.target_description|truncatechars:60 }}</p>
                                <div class="text-xs text-red-600">
                                    <strong>Suspicious indicators:</strong>
                                    <ul class="list-disc list-inside mt-1">
                                        {% for indicator in activity.indicators %}
                                        <li>{{ indicator }}</li>
                                        {% endfor %}
                                    </ul>
                                </div>
                                <p class="text-xs text-gray-500 mt-2">{{ activity.log.action_time|date:"M d, Y H:i" }}</p>
                            </div>
                            <div class="ml-4">
                                <a href="{% url 'user:action_log_detail' activity.log.pk %}" 
                                   class="text-red-600 hover:text-red-800 text-sm font-medium">
                                    View Details
                                </a>
                            </div>
                        </div>
                    </div>
                    {% endfor %}
                </div>
                {% else %}
                <div class="text-center py-8">
                    <div class="text-green-600 text-4xl mb-2">✅</div>
                    <p class="text-gray-600">No suspicious activities detected in the last 7 days</p>
                </div>
                {% endif %}
            </div>

            <!-- High Risk Users -->
            <div class="bg-orange-50 rounded-lg p-6">
                <h2 class="text-lg font-semibold text-orange-800 mb-4">🚨 High Risk Users</h2>
                {% if high_risk_users %}
                <div class="space-y-4 max-h-96 overflow-y-auto">
                    {% for user_data in high_risk_users %}
                    <div class="bg-white rounded-lg p-4 border-l-4 border-orange-400">
                        <div class="flex justify-between items-center">
                            <div class="flex-1">
                                <div class="flex items-center mb-2">
                                    <span class="text-sm font-medium text-gray-900">
                                        {{ user_data.user.username }} ({{ user_data.user.name }})
                                    </span>
                                    <span class="ml-2 px-2 py-1 text-xs bg-orange-100 text-orange-800 rounded-full">
                                        {{ user_data.user.role|title }}
                                    </span>
                                </div>
                                <div class="flex items-center">
                                    <span class="text-sm text-gray-600">Risk Score:</span>
                                    <div class="ml-2 flex-1 bg-gray-200 rounded-full h-2">
                                        <div class="bg-gradient-to-r from-yellow-400 to-red-600 h-2 rounded-full" 
                                             style="width: {{ user_data.risk_score }}%"></div>
                                    </div>
                                    <span class="ml-2 text-sm font-bold text-orange-600">{{ user_data.risk_score }}/100</span>
                                </div>
                            </div>
                            <div class="ml-4">
                                <a href="{% url 'user:action_logs' %}?user={{ user_data.user.id }}" 
                                   class="text-orange-600 hover:text-orange-800 text-sm font-medium">
                                    View Activity
                                </a>
                            </div>
                        </div>
                    </div>
                    {% endfor %}
                </div>
                {% else %}
                <div class="text-center py-8">
                    <div class="text-green-600 text-4xl mb-2">✅</div>
                    <p class="text-gray-600">No high-risk users detected</p>
                </div>
                {% endif %}
            </div>
        </div>

        <!-- Security Recommendations -->
        <div class="mt-8 bg-blue-50 rounded-lg p-6">
            <h2 class="text-lg font-semibold text-blue-800 mb-4">💡 Security Recommendations</h2>
            <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div class="bg-white rounded-lg p-4">
                    <h3 class="font-medium text-gray-900 mb-2">🔍 Regular Monitoring</h3>
                    <p class="text-sm text-gray-600">Review action logs daily, especially delete operations and financial transactions.</p>
                </div>
                <div class="bg-white rounded-lg p-4">
                    <h3 class="font-medium text-gray-900 mb-2">👥 User Training</h3>
                    <p class="text-sm text-gray-600">Ensure all staff understand the importance of data security and proper system usage.</p>
                </div>
                <div class="bg-white rounded-lg p-4">
                    <h3 class="font-medium text-gray-900 mb-2">🔒 Access Control</h3>
                    <p class="text-sm text-gray-600">Regularly review user permissions and remove unnecessary access rights.</p>
                </div>
                <div class="bg-white rounded-lg p-4">
                    <h3 class="font-medium text-gray-900 mb-2">📊 Audit Trail</h3>
                    <p class="text-sm text-gray-600">Export and archive action logs regularly for compliance and investigation purposes.</p>
                </div>
            </div>
        </div>

        <!-- Quick Actions -->
        <div class="mt-8 bg-gray-50 rounded-lg p-6">
            <h2 class="text-lg font-semibold text-gray-800 mb-4">⚡ Quick Actions</h2>
            <div class="flex flex-wrap gap-4">
                <a href="{% url 'user:action_logs' %}?action_type=delete_finance_transaction" 
                   class="bg-red-600 hover:bg-red-700 text-white px-4 py-2 rounded-lg text-sm font-medium transition duration-200">
                    🗑️ View Finance Deletions
                </a>
                <a href="{% url 'user:action_logs' %}?action_type=failed_login" 
                   class="bg-yellow-600 hover:bg-yellow-700 text-white px-4 py-2 rounded-lg text-sm font-medium transition duration-200">
                    🚫 View Failed Logins
                </a>
                <a href="{% url 'user:action_logs' %}?module=finance" 
                   class="bg-green-600 hover:bg-green-700 text-white px-4 py-2 rounded-lg text-sm font-medium transition duration-200">
                    💰 View Finance Activity
                </a>
                <a href="{% url 'user:action_logs' %}?start_date={{ today }}" 
                   class="bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded-lg text-sm font-medium transition duration-200">
                    📅 View Today's Activity
                </a>
            </div>
        </div>
    </div>
</div>

<script>
// Auto-refresh the dashboard every 5 minutes
setTimeout(function() {
    location.reload();
}, 300000); // 5 minutes in milliseconds
</script>
{% endblock %}
