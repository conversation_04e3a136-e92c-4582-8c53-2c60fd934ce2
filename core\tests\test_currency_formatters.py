from django.test import TestCase
from core.templatetags.currency_formatters import format_khr, format_usd, format_number

class CurrencyFormattersTestCase(TestCase):
    """
    Test cases for currency formatting functions
    """
    
    def test_format_khr_with_integer(self):
        """Test KHR formatting with integer values"""
        self.assertEqual(format_khr(1000), "1,000៛")
        self.assertEqual(format_khr(10000), "10,000៛")
        self.assertEqual(format_khr(1000000), "1,000,000៛")
        self.assertEqual(format_khr(1234567), "1,234,567៛")
        
    def test_format_khr_with_float(self):
        """Test KHR formatting with float values (should truncate decimal places)"""
        self.assertEqual(format_khr(1000.50), "1,000៛")
        self.assertEqual(format_khr(1000.99), "1,000៛")
        
    def test_format_khr_with_string(self):
        """Test KHR formatting with string values"""
        self.assertEqual(format_khr("1000"), "1,000៛")
        self.assertEqual(format_khr("1,000"), "1,000៛")  # Already formatted
        
    def test_format_khr_with_none(self):
        """Test KHR formatting with None value"""
        self.assertEqual(format_khr(None), "0៛")
        
    def test_format_khr_with_invalid_value(self):
        """Test KHR formatting with invalid value"""
        self.assertEqual(format_khr("invalid"), "0៛")
        
    def test_format_usd_with_integer(self):
        """Test USD formatting with integer values"""
        self.assertEqual(format_usd(1000), "$1,000.00")
        self.assertEqual(format_usd(10000), "$10,000.00")
        self.assertEqual(format_usd(1000000), "$1,000,000.00")
        
    def test_format_usd_with_float(self):
        """Test USD formatting with float values (should keep 2 decimal places)"""
        self.assertEqual(format_usd(1000.50), "$1,000.50")
        self.assertEqual(format_usd(1000.99), "$1,000.99")
        self.assertEqual(format_usd(1000.999), "$1,001.00")  # Round to 2 decimal places
        
    def test_format_usd_with_string(self):
        """Test USD formatting with string values"""
        self.assertEqual(format_usd("1000"), "$1,000.00")
        self.assertEqual(format_usd("1,000"), "$1,000.00")  # Already formatted
        self.assertEqual(format_usd("1000.50"), "$1,000.50")
        
    def test_format_usd_with_none(self):
        """Test USD formatting with None value"""
        self.assertEqual(format_usd(None), "$0.00")
        
    def test_format_usd_with_invalid_value(self):
        """Test USD formatting with invalid value"""
        self.assertEqual(format_usd("invalid"), "$0.00")
        
    def test_format_number_with_integer(self):
        """Test number formatting with integer values"""
        self.assertEqual(format_number(1000), "1,000")
        self.assertEqual(format_number(10000), "10,000")
        self.assertEqual(format_number(1000000), "1,000,000")
        
    def test_format_number_with_float(self):
        """Test number formatting with float values"""
        self.assertEqual(format_number(1000.00), "1,000")  # No decimal places for whole numbers
        self.assertEqual(format_number(1000.50), "1,000.50")  # Keep decimal places for non-whole numbers
        
    def test_format_number_with_string(self):
        """Test number formatting with string values"""
        self.assertEqual(format_number("1000"), "1,000")
        self.assertEqual(format_number("1,000"), "1,000")  # Already formatted
        
    def test_format_number_with_none(self):
        """Test number formatting with None value"""
        self.assertEqual(format_number(None), "0")
        
    def test_format_number_with_invalid_value(self):
        """Test number formatting with invalid value"""
        self.assertEqual(format_number("invalid"), "0")
