# Generated by Django 5.0.2 on 2025-05-18 00:06

import django.db.models.deletion
from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('paypervisit', '0005_paypervisitsettings_custom_price_1_and_more'),
    ]

    operations = [
        migrations.CreateModel(
            name='ReceiptTemplate',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('name', models.CharField(max_length=100, verbose_name='Template Name')),
                ('is_default', models.BooleanField(default=False, verbose_name='Default Template')),
                ('language', models.CharField(choices=[('en', 'English'), ('km', 'Khmer'), ('both', 'Both (Bilingual)')], default='en', max_length=10, verbose_name='Language')),
                ('header_text', models.Char<PERSON><PERSON>(default='LEGEND FITNESS', max_length=200, verbose_name='Header Text')),
                ('subheader_text', models.CharField(default='Pay-per-visit Receipt', max_length=200, verbose_name='Subheader Text')),
                ('footer_text', models.CharField(default='Thank you for visiting Legend Fitness!', max_length=200, verbose_name='Footer Text')),
                ('company_logo', models.ImageField(blank=True, null=True, upload_to='paypervisit_templates/', verbose_name='Company Logo')),
                ('background_color', models.CharField(default='#ffffff', max_length=20, verbose_name='Background Color')),
                ('text_color', models.CharField(default='#333333', max_length=20, verbose_name='Text Color')),
                ('accent_color', models.CharField(default='#1e40af', max_length=20, verbose_name='Accent Color')),
                ('show_company_info', models.BooleanField(default=True, verbose_name='Show Company Info')),
                ('show_signatures', models.BooleanField(default=False, verbose_name='Show Signatures')),
                ('custom_css', models.TextField(blank=True, null=True, verbose_name='Custom CSS')),
                ('created_at', models.DateTimeField(auto_now_add=True, verbose_name='Created At')),
                ('updated_at', models.DateTimeField(auto_now=True, verbose_name='Updated At')),
            ],
        ),
        migrations.AddField(
            model_name='paypervisitsettings',
            name='default_receipt_template',
            field=models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='settings', to='paypervisit.receipttemplate'),
        ),
    ]
