# Generated manually

from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('paypervisit', '0002_initial'),
    ]

    operations = [
        migrations.CreateModel(
            name='PayPerVisitSettings',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('price_per_person', models.IntegerField(default=4000, verbose_name='Price Per Person')),
                ('last_updated', models.DateTimeField(auto_now=True, verbose_name='Last Updated')),
            ],
        ),
    ]
