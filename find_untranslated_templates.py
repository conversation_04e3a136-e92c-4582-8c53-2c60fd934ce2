import os
import re

def check_template_for_translation_tags(file_path):
    """Check if a template file has translation tags."""
    with open(file_path, 'r', encoding='utf-8', errors='ignore') as f:
        content = f.read()
        
    has_load_i18n = re.search(r'{%\s*load\s+i18n\s*%}', content) is not None
    has_trans_tags = re.search(r'{%\s*trans\s+', content) is not None
    has_blocktrans_tags = re.search(r'{%\s*blocktrans', content) is not None
    
    return {
        'has_load_i18n': has_load_i18n,
        'has_trans_tags': has_trans_tags,
        'has_blocktrans_tags': has_blocktrans_tags,
        'needs_translation': not (has_trans_tags or has_blocktrans_tags)
    }

def find_untranslated_templates(templates_dir):
    """Find all HTML templates that don't have translation tags."""
    untranslated_templates = []
    partial_translated_templates = []
    translated_templates = []
    
    for root, _, files in os.walk(templates_dir):
        for file in files:
            if file.endswith('.html'):
                file_path = os.path.join(root, file)
                rel_path = os.path.relpath(file_path, templates_dir)
                
                result = check_template_for_translation_tags(file_path)
                
                if result['needs_translation']:
                    if result['has_load_i18n']:
                        partial_translated_templates.append(rel_path)
                    else:
                        untranslated_templates.append(rel_path)
                else:
                    translated_templates.append(rel_path)
    
    return {
        'untranslated': untranslated_templates,
        'partial': partial_translated_templates,
        'translated': translated_templates
    }

if __name__ == "__main__":
    templates_dir = "templates"
    results = find_untranslated_templates(templates_dir)
    
    print(f"Templates Analysis:")
    print(f"Total templates: {len(results['untranslated']) + len(results['partial']) + len(results['translated'])}")
    print(f"Fully translated templates: {len(results['translated'])}")
    print(f"Partially translated templates (has load i18n but no trans tags): {len(results['partial'])}")
    print(f"Untranslated templates: {len(results['untranslated'])}")
    
    print("\nUntranslated templates:")
    for template in sorted(results['untranslated']):
        print(f"- {template}")
    
    print("\nPartially translated templates:")
    for template in sorted(results['partial']):
        print(f"- {template}")
    
    print("\nNext steps:")
    print("1. Start by adding {% load i18n %} to all untranslated templates")
    print("2. Then mark text strings with {% trans 'text' %} tags")
    print("3. For text with variables, use {% blocktrans %}...{% endblocktrans %}")
    print("4. Run 'python manage.py makemessages -l km' to extract strings")
    print("5. Add Khmer translations to locale/km/LC_MESSAGES/django.po")
    print("6. Run 'python manage.py compilemessages -l km' to compile translations")
