@echo off
echo ======================================================================
echo Complete Redis Cleanup and 7.4.3 Setup for Legend Fitness Club
echo ======================================================================
echo.

REM Check if running as administrator
net session >nul 2>&1
if %errorLevel% neq 0 (
    echo ERROR: This script must be run as Administrator!
    echo Right-click on this file and select "Run as administrator"
    pause
    exit /b 1
)

echo ✓ Running as Administrator - proceeding with cleanup and setup...
echo.

echo Step 1: Killing all existing Redis processes...
taskkill /F /IM redis-server.exe >nul 2>&1
if %errorlevel% equ 0 (
    echo ✓ Redis processes terminated
) else (
    echo ✓ No Redis processes found running
)

taskkill /F /IM redis-cli.exe >nul 2>&1
echo.

echo Step 2: Checking what processes are using port 6379...
netstat -ano | findstr :6379
if %errorlevel% equ 0 (
    echo ⚠ Port 6379 is in use - processes shown above
) else (
    echo ✓ Port 6379 is free
)
echo.

echo Step 3: Removing Redis services...
sc stop redis >nul 2>&1
sc delete redis >nul 2>&1
if %errorlevel% equ 0 (
    echo ✓ Redis service removed
) else (
    echo ✓ No Redis service found
)

echo Checking for other Redis services...
sc query | findstr -i redis
echo.

echo Step 4: Locating existing Redis installations...
echo Checking common Redis paths...
if exist "C:\Program Files\Redis" (
    echo Found: C:\Program Files\Redis
    dir "C:\Program Files\Redis\redis-*.exe" 2>nul
)

if exist "C:\Redis" (
    echo Found: C:\Redis
    dir "C:\Redis\redis-*.exe" 2>nul
)

where redis-server >nul 2>&1
if %errorlevel% equ 0 (
    echo Redis server found in PATH:
    where redis-server
)

where redis-cli >nul 2>&1
if %errorlevel% equ 0 (
    echo Redis CLI found in PATH:
    where redis-cli
)
echo.

echo Step 5: Verifying port 6379 is completely free...
netstat -an | findstr :6379
if %errorlevel% equ 0 (
    echo ⚠ Port 6379 still has connections
) else (
    echo ✓ Port 6379 is completely free
)
echo.

echo Step 6: Verifying Redis 7.4.3 installation...
if not exist "C:\Redis-7.4.3\redis-server.exe" (
    echo ERROR: Redis 7.4.3 server not found at C:\Redis-7.4.3
    pause
    exit /b 1
)

if not exist "C:\Redis-7.4.3\redis-cli.exe" (
    echo ERROR: Redis 7.4.3 CLI not found at C:\Redis-7.4.3
    pause
    exit /b 1
)

echo ✓ Redis 7.4.3 files found
echo.

echo Step 7: Creating Redis 7.4.3 configuration...
(
echo # Redis 7.4.3 Configuration for Legend Fitness Club
echo port 6379
echo bind 127.0.0.1
echo timeout 0
echo tcp-keepalive 60
echo tcp-backlog 511
echo loglevel notice
echo logfile "redis-server.log"
echo databases 16
echo save 900 1
echo save 300 10
echo save 60 10000
echo stop-writes-on-bgsave-error yes
echo rdbcompression yes
echo rdbchecksum yes
echo dbfilename dump.rdb
echo dir ./
echo maxmemory 1gb
echo maxmemory-policy allkeys-lru
echo tcp-keepalive 300
echo notify-keyspace-events Ex
echo lazyfree-lazy-eviction yes
echo lazyfree-lazy-expire yes
echo lazyfree-lazy-server-del yes
echo replica-lazy-flush yes
) > "C:\Redis-7.4.3\redis.windows-service.conf"

echo ✓ Redis 7.4.3 configuration created
echo.

echo Step 8: Starting Redis 7.4.3 server...
cd /d "C:\Redis-7.4.3"

echo Starting Redis 7.4.3 in background...
start "Redis 7.4.3 Server" redis-server.exe redis.windows-service.conf

echo Waiting for Redis 7.4.3 to initialize...
timeout /t 5 /nobreak >nul
echo.

echo Step 9: Testing Redis 7.4.3 connectivity...
redis-cli.exe ping >nul 2>&1
if %errorlevel% equ 0 (
    echo ✓ Redis 7.4.3 is responding to ping!
    
    echo ✓ Redis version:
    redis-cli.exe info server | findstr redis_version
    
    echo Testing basic operations...
    redis-cli.exe set test "Redis 7.4.3 Working" >nul 2>&1
    redis-cli.exe get test
    redis-cli.exe del test >nul 2>&1
    
    echo ✓ Redis 7.4.3 basic operations successful
    
    echo Testing keyspace notifications...
    redis-cli.exe config get notify-keyspace-events
    
) else (
    echo ✗ Redis 7.4.3 is not responding
    echo Please check the Redis server window for errors
)
echo.

echo Step 10: Testing Django integration...
cd /d "c:\Final Project\legend_fitness_club-gym-ms"

echo Running Django Redis test...
python manage.py test_redis --detailed
echo.

echo ======================================================================
echo Redis Cleanup and 7.4.3 Setup Complete!
echo ======================================================================
echo.
echo Next Steps:
echo 1. Start Django server: python manage.py runserver 8000
echo 2. Check for Redis 7.4.3 detection in startup logs
echo 3. Test WebSocket functionality in browser
echo 4. Verify real-time permission updates work
echo.
echo Expected Django startup messages:
echo "✓ Redis 7.4.3 detected - using Redis channel layer"
echo "✓ Redis detected - using Redis cache backend"
echo.

pause
