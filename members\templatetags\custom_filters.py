from django import template
from decimal import Decimal
from core.templatetags.currency_formatters import format_khr as format_khr_with_separators
from core.templatetags.currency_formatters import format_usd as format_usd_with_separators
from core.templatetags.currency_formatters import format_number

register = template.Library()

@register.filter
def abs_val(value):
    """
    Returns the absolute value of a number
    """
    return abs(value)

@register.filter
def format_khr(value):
    """
    Format a number as KHR currency with thousand separators
    """
    return format_khr_with_separators(value)

@register.filter
def format_usd(value):
    """
    Format a number as USD currency with thousand separators
    """
    return format_usd_with_separators(value)

@register.filter
def format_number_with_commas(value):
    """
    Format a number with thousand separators without any currency symbol
    """
    return format_number(value)

@register.filter
def progress_width(days):
    """
    Convert days remaining to a percentage for progress bar width
    Caps at 100% and ensures a minimum of 5%
    """
    try:
        days = int(days)
        if days <= 0:
            return 5  # Minimum width for visibility
        elif days > 30:
            return 100  # Cap at 100%
        else:
            # Scale from 0-30 days to 5-100%
            return max(5, int((days / 30) * 95) + 5)
    except (ValueError, TypeError):
        return 5

@register.filter
def multiply(value, arg):
    """
    Multiply the value by the argument
    """
    try:
        return int(value) * int(arg)
    except (ValueError, TypeError):
        return 0

@register.filter
def add(value, arg):
    """
    Add the argument to the value
    """
    try:
        return float(value) + float(arg)
    except (ValueError, TypeError):
        return 0

@register.filter
def div(value, arg):
    """
    Divide the value by the argument
    """
    try:
        return float(value) / float(arg)
    except (ValueError, TypeError, ZeroDivisionError):
        return 0

@register.filter
def mul(value, arg):
    """
    Multiply the value by the argument
    """
    try:
        return float(value) * float(arg)
    except (ValueError, TypeError):
        return 0

@register.filter
def floatformat(value, arg):
    """
    Format a float to a specific number of decimal places
    """
    try:
        return round(float(value), int(arg))
    except (ValueError, TypeError):
        return 0
