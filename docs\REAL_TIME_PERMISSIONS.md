# Real-Time Permission Management System

## Overview

The Legend Fitness Club gym management system now includes a comprehensive real-time permission management system that provides instant updates to user permissions without requiring page refreshes or re-login. This system ensures that when an admin changes user permissions, affected users immediately see the changes reflected in their sidebar navigation and access controls.

## Features

### 1. Real-Time Menu Visibility Updates
- **Instant Sidebar Updates**: When permissions are changed, affected users' sidebar menu items automatically appear or disappear
- **Dynamic Navigation**: Menu items are shown/hidden based on current permission levels
- **Smooth Animations**: Visual feedback with fade-in/fade-out animations for permission changes

### 2. WebSocket Integration
- **Persistent Connections**: Real-time communication using Django Channels and WebSockets
- **Role-Based Groups**: Users are automatically grouped by role for targeted updates
- **Individual Notifications**: Specific users can receive personalized permission notifications

### 3. JavaScript Permission Polling
- **Fallback Mechanism**: Automatic fallback to polling when WebSocket connections fail
- **Adaptive Frequency**: Polling frequency adjusts based on page visibility
- **Graceful Degradation**: System continues to work even without WebSocket support

### 4. Permission Cache Management
- **Redis-Based Caching**: High-performance caching using Redis for permission lookups
- **Automatic Invalidation**: Cache is automatically cleared when permissions change
- **Optimized Performance**: Reduced database queries through intelligent caching

### 5. Session Management
- **Session Invalidation**: Optional session invalidation when permissions change significantly
- **User Tracking**: Track active users and their permission states
- **Security**: Ensure users can't access resources after permission revocation

### 6. User Notification System
- **Visual Notifications**: Toast-style notifications inform users of permission changes
- **Change Details**: Specific information about what permissions were granted or revoked
- **Multiple Types**: Info, success, warning, and error notification types

### 7. Graceful Fallback
- **Robust Design**: System works even when real-time features are unavailable
- **Progressive Enhancement**: Core functionality remains intact without WebSocket support
- **Error Handling**: Comprehensive error handling and logging

## Technical Architecture

### Components

1. **WebSocket Consumers** (`core/consumers.py`)
   - `PermissionConsumer`: Handles permission-related WebSocket connections
   - `SystemNotificationConsumer`: Manages system-wide notifications

2. **Cache Manager** (`settings/cache_manager.py`)
   - `PermissionCacheManager`: Centralized cache management for permissions
   - Automatic cache invalidation and real-time notifications

3. **Django Signals** (`settings/signals.py`)
   - Automatic cache invalidation when permissions change
   - Real-time notification triggers

4. **JavaScript Client** (`static/js/permission-manager.js`)
   - `PermissionManager`: Client-side permission management
   - WebSocket connection handling and fallback polling

5. **API Endpoints** (`settings/api_views.py`)
   - RESTful endpoints for permission checking and cache management
   - Health check and status endpoints

### Permission Levels

The system supports four permission levels:
- **No Access** (`none`): User cannot access the module
- **View Only** (`view`): User can view but not modify data
- **View and Edit** (`edit`): User can view and modify data
- **Full Access** (`full`): User has complete access including deletion

### Caching Strategy

- **User Permissions**: Cached per user for 30 minutes
- **Role Permissions**: Cached per role for 30 minutes
- **Permission Checks**: Individual permission checks cached for performance
- **Automatic Invalidation**: Cache cleared when permissions change

## Installation and Setup

### 1. Install Dependencies

```bash
pip install channels==4.0.0 channels-redis==4.2.0 redis==5.0.1 daphne==4.1.0
```

### 2. Redis Setup

Ensure Redis is installed and running:
```bash
# Ubuntu/Debian
sudo apt-get install redis-server
sudo systemctl start redis-server

# Windows (using WSL or Docker)
docker run -d -p 6379:6379 redis:alpine
```

### 3. Django Settings

The following settings are automatically configured:

```python
INSTALLED_APPS = [
    'daphne',
    'channels',
    # ... other apps
]

ASGI_APPLICATION = 'core.asgi.application'

CHANNEL_LAYERS = {
    'default': {
        'BACKEND': 'channels_redis.core.RedisChannelLayer',
        'CONFIG': {
            "hosts": [('127.0.0.1', 6379)],
        },
    },
}

CACHES = {
    'default': {
        'BACKEND': 'django.core.cache.backends.redis.RedisCache',
        'LOCATION': 'redis://127.0.0.1:6379/1',
    }
}
```

### 4. Run Migrations

```bash
python manage.py migrate
```

### 5. Start the Server

```bash
# Development
python manage.py runserver

# Production (with Daphne)
daphne -p 8000 core.asgi:application
```

## Usage

### Admin Perspective

1. **Navigate to Settings > Permissions**
2. **Select a role** (Cashier, Coach, Cleaner, Security)
3. **Modify permissions** for any module
4. **Save changes** - affected users receive instant notifications

### User Perspective

1. **Automatic Updates**: Sidebar menu items appear/disappear automatically
2. **Visual Notifications**: Toast notifications inform about permission changes
3. **Seamless Experience**: No page refresh or re-login required

## API Endpoints

### Permission Checking
- `GET /settings/api/permissions/check/` - Check current user permissions
- `GET /settings/api/permissions/status/` - Get detailed permission status
- `POST /settings/api/permissions/status/` - Refresh user permissions

### Cache Management
- `POST /settings/api/permissions/invalidate-cache/` - Invalidate permission cache (Admin only)

### Health Checks
- `GET /settings/api/permissions/health/` - Permission system health check
- `GET /settings/api/websocket/status/` - WebSocket availability status

## WebSocket Endpoints

- `ws://localhost:8000/ws/permissions/` - Permission updates
- `ws://localhost:8000/ws/notifications/` - System notifications

## Configuration Options

### Cache Timeout
```python
PERMISSION_CACHE_TIMEOUT = 1800  # 30 minutes (in seconds)
```

### Polling Frequency
```javascript
// In permission-manager.js
this.pollingFrequency = 30000; // 30 seconds
```

### WebSocket Reconnection
```javascript
this.maxReconnectAttempts = 5;
this.reconnectDelay = 1000; // Start with 1 second, exponential backoff
```

## Troubleshooting

### Common Issues

1. **WebSocket Connection Failed**
   - Ensure Redis is running
   - Check CHANNEL_LAYERS configuration
   - Verify firewall settings

2. **Permissions Not Updating**
   - Check browser console for JavaScript errors
   - Verify WebSocket connection status
   - Test fallback polling mechanism

3. **Cache Issues**
   - Clear Redis cache: `redis-cli FLUSHDB`
   - Restart Django server
   - Check Redis connection

### Debugging

1. **Enable Debug Logging**
   ```python
   LOGGING['loggers']['settings.cache_manager']['level'] = 'DEBUG'
   ```

2. **Check WebSocket Status**
   ```javascript
   // In browser console
   console.log(window.permissionManager.isConnected);
   ```

3. **Monitor Redis**
   ```bash
   redis-cli monitor
   ```

## Security Considerations

1. **Authentication Required**: All WebSocket connections require user authentication
2. **Role-Based Access**: Users only receive updates relevant to their role
3. **Permission Validation**: All permission changes are validated server-side
4. **Audit Logging**: All permission changes are logged for audit purposes

## Performance Optimization

1. **Efficient Caching**: Redis-based caching reduces database queries
2. **Targeted Updates**: Only affected users receive notifications
3. **Connection Pooling**: WebSocket connections are efficiently managed
4. **Graceful Degradation**: System remains performant even under high load

## Future Enhancements

1. **Real-time User Activity**: Show which users are currently online
2. **Permission History**: Track and display permission change history
3. **Bulk Permission Updates**: Update permissions for multiple roles simultaneously
4. **Advanced Notifications**: More detailed notification types and customization
5. **Mobile App Support**: Extend real-time features to mobile applications
