import os
import re
import sys

def add_load_i18n(file_path):
    """Add {% load i18n %} tag to a template file."""
    with open(file_path, 'r', encoding='utf-8', errors='ignore') as f:
        content = f.read()

    # Keep track of original content to check if changes were made
    original_content = content

    # Check if the file already has {% load i18n %}
    if re.search(r'{%\s*load\s+i18n\s*%}', content):
        print(f"File {file_path} already has load i18n tag")
        return False

    # Find the first {% load ... %} tag
    load_match = re.search(r'{%\s*load\s+[^%]+%}', content)

    if load_match:
        # Add {% load i18n %} after the existing load tag
        end_pos = load_match.end()
        content = content[:end_pos] + '\n{% load i18n %}' + content[end_pos:]
    else:
        # If no load tag exists, add it after the first line (usually DOCTYPE or html tag)
        lines = content.split('\n', 1)
        if len(lines) > 1:
            content = lines[0] + '\n{% load i18n %}\n' + lines[1]
        else:
            content = '{% load i18n %}\n' + content

    # Write the modified content back to the file
    with open(file_path, 'w', encoding='utf-8') as f:
        f.write(content)

    print(f"Added load i18n tag to {file_path}")
    return True

def find_html_files(directory):
    """Find all HTML files in the given directory and its subdirectories."""
    html_files = []
    for root, dirs, files in os.walk(directory):
        for file in files:
            if file.endswith('.html'):
                html_files.append(os.path.join(root, file))
    return html_files

def main():
    if len(sys.argv) < 2:
        print("Usage: python add_load_i18n.py <template_path_or_directory>")
        print("  - Provide a specific template file path to process a single file")
        print("  - Provide a directory path to process all HTML files in that directory and subdirectories")
        sys.exit(1)

    path = sys.argv[1]

    if os.path.isfile(path):
        # Process a single file
        if path.endswith('.html'):
            add_load_i18n(path)
        else:
            print(f"Error: {path} is not an HTML file")
    elif os.path.isdir(path):
        # Process all HTML files in the directory and subdirectories
        html_files = find_html_files(path)
        if not html_files:
            print(f"No HTML files found in {path}")
            sys.exit(1)

        processed_count = 0
        for html_file in html_files:
            if add_load_i18n(html_file):
                processed_count += 1

        print(f"Processed {len(html_files)} HTML files. Added load i18n tag to {processed_count} files.")
    else:
        print(f"Error: {path} does not exist")
        sys.exit(1)

if __name__ == "__main__":
    main()
