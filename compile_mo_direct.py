#!/usr/bin/env python
"""
Script to compile .po files to .mo files using Python's gettext module.
This is an alternative to Django's compilemessages command when gettext tools are not installed.
"""

import os
import sys
from pathlib import Path

def compile_po_file(po_file_path):
    """Compile a .po file to a .mo file using Python's gettext module."""
    try:
        # Try to import the required modules
        try:
            from babel.messages.mofile import write_mo
            from babel.messages.pofile import read_po
        except ImportError:
            print("Installing babel package...")
            import subprocess
            subprocess.check_call([sys.executable, '-m', 'pip', 'install', 'babel'])
            from babel.messages.mofile import write_mo
            from babel.messages.pofile import read_po
        
        print(f"Compiling {po_file_path}...")
        with open(po_file_path, 'rb') as po_file:
            catalog = read_po(po_file)
        
        mo_file_path = po_file_path.replace('.po', '.mo')
        with open(mo_file_path, 'wb') as mo_file:
            write_mo(mo_file, catalog)
        
        print(f"Successfully compiled {mo_file_path}")
        return True
    except Exception as e:
        print(f"Error compiling {po_file_path}: {e}")
        return False

def find_po_files(base_dir):
    """Find all .po files in the given directory and its subdirectories."""
    po_files = []
    for root, dirs, files in os.walk(base_dir):
        for file in files:
            if file.endswith('.po'):
                po_files.append(os.path.join(root, file))
    return po_files

def main():
    # Find the locale directory
    locale_dir = Path('locale')
    
    if not locale_dir.exists():
        print(f"Error: Locale directory '{locale_dir}' not found.")
        return 1
    
    # Find all .po files
    po_files = find_po_files(locale_dir)
    
    if not po_files:
        print("No .po files found in the locale directory.")
        return 1
    
    # Compile each .po file
    success_count = 0
    for po_file in po_files:
        if compile_po_file(po_file):
            success_count += 1
    
    print(f"Compiled {success_count} out of {len(po_files)} .po files.")
    return 0 if success_count == len(po_files) else 1

if __name__ == "__main__":
    sys.exit(main())
