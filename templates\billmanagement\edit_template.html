{% extends 'base.html' %}
{% load custom_filters %}



{% block body %}
<div class="conponentSection p-5 bg-gray-200">
    <div class="componentWrapper">
        <!-- Header with navigation -->
        <div class="flex justify-between items-center mb-4">
            <h3 class="text-2xl font-bold">Edit Receipt Template</h3>
            <div class="flex space-x-2">
                <a href="{% url 'billmanagement:template_list' %}" class="bg-blue-900 text-white px-4 py-2 rounded">
                    <i class="fa-solid fa-arrow-left mr-2"></i>Back to Templates
                </a>
                <a href="{% url 'billmanagement:preview_template' template.id %}" class="bg-purple-600 text-white px-4 py-2 rounded">
                    <i class="fa-solid fa-eye mr-2"></i>Preview Template
                </a>
            </div>
        </div>

        <!-- Template Form -->
        <div class="bg-white p-4 rounded shadow-md">
            <form method="post" enctype="multipart/form-data">
                {% csrf_token %}
                <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                    <!-- Left Column - Basic Information -->
                    <div>
                        <h4 class="text-lg font-semibold mb-4">Template Information</h4>
                        
                        <div class="mb-4">
                            <label class="block text-sm font-medium mb-1">Template Name*</label>
                            <input type="text" name="name" class="border w-full p-3 rounded bg-slate-100" value="{{ template.name }}" required>
                        </div>
                        
                        <div class="mb-4">
                            <label class="block text-sm font-medium mb-1">Language*</label>
                            <select name="language" class="border w-full p-3 rounded bg-slate-100" required>
                                {% for lang_code, lang_name in languages %}
                                <option value="{{ lang_code }}" {% if template.language == lang_code %}selected{% endif %}>{{ lang_name }}</option>
                                {% endfor %}
                            </select>
                        </div>
                        
                        <div class="mb-4">
                            <label class="block text-sm font-medium mb-1">Header Text</label>
                            <input type="text" name="header_text" class="border w-full p-3 rounded bg-slate-100" value="{{ template.header_text }}">
                        </div>
                        
                        <div class="mb-4">
                            <label class="block text-sm font-medium mb-1">Subheader Text</label>
                            <input type="text" name="subheader_text" class="border w-full p-3 rounded bg-slate-100" value="{{ template.subheader_text }}">
                        </div>
                        
                        <div class="mb-4">
                            <label class="block text-sm font-medium mb-1">Footer Text</label>
                            <input type="text" name="footer_text" class="border w-full p-3 rounded bg-slate-100" value="{{ template.footer_text }}">
                        </div>
                        
                        <div class="mb-4">
                            <label class="block text-sm font-medium mb-1">Company Logo</label>
                            {% if template.company_logo %}
                            <div class="mb-2">
                                <img src="{{ template.company_logo.url }}" alt="Current Logo" class="h-12">
                                <p class="text-sm text-gray-600">Current logo</p>
                            </div>
                            {% endif %}
                            <input type="file" name="company_logo" class="border w-full p-3 rounded bg-slate-100">
                            <p class="text-sm text-gray-600 mt-1">Leave empty to keep current logo</p>
                        </div>
                    </div>
                    
                    <!-- Right Column - Styling -->
                    <div>
                        <h4 class="text-lg font-semibold mb-4">Styling Options</h4>
                        
                        <div class="mb-4">
                            <label class="block text-sm font-medium mb-1">Background Color</label>
                            <input type="color" name="background_color" class="border w-full p-1 h-10 rounded bg-slate-100" value="{{ template.background_color }}">
                        </div>
                        
                        <div class="mb-4">
                            <label class="block text-sm font-medium mb-1">Text Color</label>
                            <input type="color" name="text_color" class="border w-full p-1 h-10 rounded bg-slate-100" value="{{ template.text_color }}">
                        </div>
                        
                        <div class="mb-4">
                            <label class="block text-sm font-medium mb-1">Accent Color</label>
                            <input type="color" name="accent_color" class="border w-full p-1 h-10 rounded bg-slate-100" value="{{ template.accent_color }}">
                        </div>
                        
                        <div class="mb-4">
                            <label class="block text-sm font-medium mb-1">Custom CSS (Optional)</label>
                            <textarea name="custom_css" class="border w-full p-3 rounded bg-slate-100" rows="4">{{ template.custom_css }}</textarea>
                        </div>
                        
                        <div class="mb-4">
                            <div class="flex items-center">
                                <input type="checkbox" name="is_default" id="is_default" class="mr-2" {% if template.is_default %}checked{% endif %}>
                                <label for="is_default">Set as Default Template</label>
                            </div>
                        </div>
                        
                        <div class="mb-4">
                            <div class="flex items-center">
                                <input type="checkbox" name="show_company_info" id="show_company_info" class="mr-2" {% if template.show_company_info %}checked{% endif %}>
                                <label for="show_company_info">Show Company Information</label>
                            </div>
                        </div>
                        
                        <div class="mb-4">
                            <div class="flex items-center">
                                <input type="checkbox" name="show_signatures" id="show_signatures" class="mr-2" {% if template.show_signatures %}checked{% endif %}>
                                <label for="show_signatures">Show Signature Lines</label>
                            </div>
                        </div>
                    </div>
                </div>
                
                <div class="mt-6 flex justify-end space-x-3">
                    <a href="{% url 'billmanagement:template_list' %}" class="bg-gray-500 text-white px-6 py-2 rounded">Cancel</a>
                    <button type="submit" class="bg-blue-900 text-white px-6 py-2 rounded">Update Template</button>
                </div>
            </form>
        </div>
    </div>
</div>
{% endblock %}
