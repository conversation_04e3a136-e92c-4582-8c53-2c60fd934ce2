/* Dashboard Report Cards Styles */

.report-cards-container {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 1.5rem;
    margin-bottom: 2rem;
    width: 100%;
    min-height: 200px; /* Ensure container has minimum height for debugging */
}

/* Fallback for browsers that don't support CSS Grid */
@supports not (display: grid) {
    .report-cards-container {
        display: flex;
        flex-wrap: wrap;
        gap: 1.5rem;
    }

    .report-card {
        flex: 1 1 300px;
        min-width: 300px;
    }
}

.report-card {
    background: white;
    border-radius: 0.75rem;
    padding: 1.5rem;
    box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
    transition: all 0.3s ease;
    cursor: pointer;
    border-left: 4px solid transparent;
    position: relative;
    overflow: hidden;
}

.report-card:hover {
    transform: translateY(-2px);
    box-shadow: 0 10px 25px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
}

.report-card.income {
    border-left-color: #3b82f6;
}

.report-card.expense {
    border-left-color: #ef4444;
}

.report-card.balance {
    border-left-color: #10b981;
}

.report-card.overview {
    border-left-color: #8b5cf6;
}

.report-card.paypervisit {
    border-left-color: #f59e0b;
}

.report-card.product {
    border-left-color: #06b6d4;
}

.report-card-header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    margin-bottom: 1rem;
}

.report-card-title {
    font-size: 1.125rem;
    font-weight: 600;
    color: #374151;
    margin: 0;
}

.report-card-icon {
    width: 2.5rem;
    height: 2.5rem;
    border-radius: 0.5rem;
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-size: 1.25rem;
}

.report-card-icon.income {
    background-color: #3b82f6;
}

.report-card-icon.expense {
    background-color: #ef4444;
}

.report-card-icon.balance {
    background-color: #10b981;
}

.report-card-icon.overview {
    background-color: #8b5cf6;
}

.report-card-icon.paypervisit {
    background-color: #f59e0b;
}

.report-card-icon.product {
    background-color: #06b6d4;
}

.report-card-content {
    margin-bottom: 1rem;
}

.report-card-main-value {
    font-size: 2rem;
    font-weight: 700;
    color: #111827;
    margin-bottom: 0.25rem;
    line-height: 1.2;
}

.report-card-subtitle {
    font-size: 0.875rem;
    color: #6b7280;
    margin-bottom: 0.75rem;
}

.report-card-metrics {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(120px, 1fr));
    gap: 0.75rem;
    margin-top: 1rem;
}

.metric-item {
    text-align: center;
    padding: 0.5rem;
    background-color: #f9fafb;
    border-radius: 0.5rem;
}

.metric-value {
    font-size: 1.125rem;
    font-weight: 600;
    color: #374151;
    margin-bottom: 0.25rem;
}

.metric-label {
    font-size: 0.75rem;
    color: #6b7280;
    text-transform: uppercase;
    letter-spacing: 0.05em;
}

.report-card-footer {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding-top: 1rem;
    border-top: 1px solid #e5e7eb;
}

.period-selector {
    display: flex;
    background-color: #f3f4f6;
    border-radius: 0.375rem;
    padding: 0.125rem;
}

.period-btn {
    padding: 0.25rem 0.75rem;
    font-size: 0.75rem;
    font-weight: 500;
    border: none;
    background: transparent;
    color: #6b7280;
    border-radius: 0.25rem;
    cursor: pointer;
    transition: all 0.2s ease;
}

.period-btn.active {
    background-color: white;
    color: #374151;
    box-shadow: 0 1px 2px 0 rgba(0, 0, 0, 0.05);
}

.period-btn:hover:not(.active) {
    color: #374151;
}

.trend-indicator {
    display: flex;
    align-items: center;
    font-size: 0.875rem;
    font-weight: 500;
}

.trend-indicator.positive {
    color: #10b981;
}

.trend-indicator.negative {
    color: #ef4444;
}

.trend-indicator.neutral {
    color: #6b7280;
}

.trend-indicator i {
    margin-right: 0.25rem;
    font-size: 0.75rem;
}

.loading-state {
    display: flex;
    align-items: center;
    justify-content: center;
    min-height: 120px;
    color: #6b7280;
}

.loading-spinner {
    width: 1.5rem;
    height: 1.5rem;
    border: 2px solid #e5e7eb;
    border-top: 2px solid #3b82f6;
    border-radius: 50%;
    animation: spin 1s linear infinite;
    margin-right: 0.5rem;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

.error-state {
    color: #ef4444;
    text-align: center;
    padding: 1rem;
    font-size: 0.875rem;
}

/* Top products list */
.top-products-list {
    margin-top: 0.75rem;
}

.product-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 0.5rem 0;
    border-bottom: 1px solid #f3f4f6;
}

.product-item:last-child {
    border-bottom: none;
}

.product-name {
    font-size: 0.875rem;
    font-weight: 500;
    color: #374151;
}

.product-quantity {
    font-size: 0.75rem;
    color: #6b7280;
}

/* Responsive design */
@media (max-width: 768px) {
    .report-cards-container {
        grid-template-columns: 1fr;
        gap: 1rem;
    }

    .report-card {
        padding: 1rem;
    }

    .report-card-main-value {
        font-size: 1.5rem;
    }

    .report-card-metrics {
        grid-template-columns: repeat(2, 1fr);
        gap: 0.5rem;
    }

    .report-card-footer {
        flex-direction: column;
        gap: 0.75rem;
        align-items: stretch;
    }

    .period-selector {
        justify-content: center;
    }
}

@media (max-width: 480px) {
    .report-card-header {
        flex-direction: column;
        align-items: flex-start;
        gap: 0.75rem;
    }

    .report-card-metrics {
        grid-template-columns: 1fr;
    }
}

/* Animation for value updates */
.value-update {
    animation: valueChange 0.3s ease-in-out;
}

@keyframes valueChange {
    0% { transform: scale(1); }
    50% { transform: scale(1.05); }
    100% { transform: scale(1); }
}

/* Accessibility improvements */
.report-card:focus {
    outline: 2px solid #3b82f6;
    outline-offset: 2px;
}

.period-btn:focus {
    outline: 2px solid #3b82f6;
    outline-offset: 1px;
}

/* Quick Actions Styles */
.quick-actions-container {
    display: flex;
    flex-direction: column;
    gap: 1rem;
    margin-bottom: 1rem;
}

.quick-action-btn {
    display: flex;
    align-items: center;
    padding: 1.25rem;
    background: white;
    border-radius: 0.75rem;
    box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
    transition: all 0.3s ease;
    text-decoration: none;
    color: inherit;
    border-left: 4px solid transparent;
    position: relative;
    overflow: hidden;
    cursor: pointer;
}

.quick-action-btn:hover {
    transform: translateY(-2px);
    box-shadow: 0 10px 25px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
    text-decoration: none;
    color: inherit;
}

.quick-action-btn:focus {
    outline: 2px solid #3b82f6;
    outline-offset: 2px;
}

/* Quick action button variants */
.quick-action-btn.paypervisit {
    border-left-color: #f59e0b;
}

.quick-action-btn.pos {
    border-left-color: #06b6d4;
}

.quick-action-btn.payment {
    border-left-color: #3b82f6;
}

.quick-action-btn.deposit {
    border-left-color: #3b82f6;
}

.quick-action-btn.withdraw {
    border-left-color: #ef4444;
}

.quick-action-icon {
    width: 3.5rem;
    height: 3.5rem;
    border-radius: 0.5rem;
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-size: 1.5rem;
    margin-right: 1.25rem;
    flex-shrink: 0;
}

.quick-action-icon.paypervisit {
    background-color: #f59e0b;
}

.quick-action-icon.pos {
    background-color: #06b6d4;
}

.quick-action-icon.payment {
    background-color: #3b82f6;
}

.quick-action-icon.deposit {
    background-color: #3b82f6;
}

.quick-action-icon.withdraw {
    background-color: #ef4444;
}

.quick-action-content {
    flex: 1;
    min-width: 0;
}

.quick-action-title {
    font-size: 1.125rem;
    font-weight: 600;
    color: #374151;
    margin: 0 0 0.25rem 0;
    line-height: 1.4;
}

.quick-action-subtitle {
    font-size: 0.875rem;
    color: #6b7280;
    margin: 0;
    line-height: 1.4;
}

.quick-action-arrow {
    color: #9ca3af;
    font-size: 1.125rem;
    margin-left: 1rem;
    transition: all 0.3s ease;
}

.quick-action-btn:hover .quick-action-arrow {
    color: #374151;
    transform: translateX(2px);
}

/* Responsive design for quick actions */
@media (max-width: 1024px) {
    .quick-actions-container {
        gap: 0.75rem;
    }

    .quick-action-btn {
        padding: 1rem;
    }

    .quick-action-icon {
        width: 3rem;
        height: 3rem;
        font-size: 1.25rem;
        margin-right: 1rem;
    }

    .quick-action-title {
        font-size: 1rem;
    }

    .quick-action-subtitle {
        font-size: 0.8125rem;
    }

    .quick-action-arrow {
        font-size: 1rem;
    }
}

@media (max-width: 768px) {
    /* Stack quick actions vertically on mobile */
    .grid.grid-cols-1.lg\\:grid-cols-2,
    .grid-cols-1.lg\\:grid-cols-2 {
        grid-template-columns: 1fr !important;
        gap: 1.5rem;
    }

    .quick-actions-container {
        gap: 0.75rem;
    }

    .quick-action-btn {
        padding: 1rem;
    }

    .quick-action-icon {
        width: 2.75rem;
        height: 2.75rem;
        font-size: 1.125rem;
        margin-right: 0.875rem;
    }

    .quick-action-title {
        font-size: 1rem;
    }

    .quick-action-subtitle {
        font-size: 0.8125rem;
    }

    .quick-action-arrow {
        font-size: 1rem;
        margin-left: 0.75rem;
    }
}

@media (max-width: 480px) {
    .quick-action-btn {
        padding: 0.875rem;
    }

    .quick-action-icon {
        width: 2.5rem;
        height: 2.5rem;
        font-size: 1rem;
        margin-right: 0.75rem;
    }

    .quick-action-title {
        font-size: 0.9375rem;
    }

    .quick-action-subtitle {
        font-size: 0.75rem;
    }

    .quick-action-arrow {
        font-size: 0.9375rem;
        margin-left: 0.5rem;
    }
}

/* Animation for quick action buttons */
.quick-actions-container .quick-action-btn {
    animation: fadeInUp 0.3s ease-out;
}

.quick-actions-container .quick-action-btn:nth-child(1) {
    animation-delay: 0.1s;
}

.quick-actions-container .quick-action-btn:nth-child(2) {
    animation-delay: 0.2s;
}

.quick-actions-container .quick-action-btn:nth-child(3) {
    animation-delay: 0.3s;
}

@keyframes fadeInUp {
    0% {
        opacity: 0;
        transform: translateY(10px);
    }
    100% {
        opacity: 1;
        transform: translateY(0);
    }
}

/* Print styles */
@media print {
    .report-cards-container {
        grid-template-columns: repeat(2, 1fr);
        gap: 1rem;
    }

    .report-card {
        break-inside: avoid;
        box-shadow: none;
        border: 1px solid #e5e7eb;
    }

    .period-selector,
    .trend-indicator {
        display: none;
    }

    /* Hide quick actions in print */
    .quick-actions-container {
        display: none;
    }
}

/* Additional Quick Actions Enhancements */
.quick-action-btn:active {
    transform: translateY(0);
    box-shadow: 0 2px 4px -1px rgba(0, 0, 0, 0.1);
}

/* Improve visual feedback for different states */
.quick-action-btn:hover .quick-action-icon {
    transform: scale(1.05);
}

.quick-action-icon {
    transition: transform 0.3s ease;
}

/* Better spacing for the Quick Actions sections */
.quick-actions-container + .quick-actions-container {
    margin-top: 0;
}

/* Ensure consistent height for Quick Actions sections */
.quick-actions-container {
    min-height: 200px;
    display: flex;
    flex-direction: column;
    justify-content: flex-start;
}

/* Improve accessibility */
.quick-action-btn:focus-visible {
    outline: 2px solid #3b82f6;
    outline-offset: 2px;
    box-shadow: 0 0 0 4px rgba(59, 130, 246, 0.1);
}

/* Loading state for quick actions (if needed) */
.quick-actions-loading {
    display: flex;
    align-items: center;
    justify-content: center;
    min-height: 200px;
    color: #6b7280;
    font-size: 0.875rem;
}

.quick-actions-loading .loading-spinner {
    width: 1.5rem;
    height: 1.5rem;
    border: 2px solid #e5e7eb;
    border-top: 2px solid #3b82f6;
    border-radius: 50%;
    animation: spin 1s linear infinite;
    margin-right: 0.5rem;
}
