from django.shortcuts import render, redirect, get_object_or_404
from django.http import JsonResponse, HttpResponse
from django.contrib.auth.decorators import login_required
from django.db.models import Sum, Count
from django.utils import timezone
from django.template.loader import get_template
from datetime import datetime, timedelta
import json
import csv
import os
from io import BytesIO
from xhtml2pdf import pisa

# Import models from other apps
from payment.models import Payment
from product.models import Sale, Purchase
from paypervisit.models import PayPerVisit
from payroll.models import SalaryPayment
from billmanagement.models import Bill
from finance.models import Transaction
from core.decorators import module_permission_required

# Import local models
from .models import ReportTemplate

# Helper function to get filters from request
def get_filters(request):
    filter_type = request.GET.get('filter', 'month')
    today = timezone.now().date()

    # Get date range
    if filter_type == 'today':
        start_date = today
        end_date = today
    elif filter_type == 'week':
        start_date = today - timedelta(days=today.weekday())
        end_date = start_date + timedelta(days=6)
    elif filter_type == 'month':
        start_date = today.replace(day=1)
        # Get last day of month
        if today.month == 12:
            end_date = today.replace(year=today.year + 1, month=1, day=1) - timedelta(days=1)
        else:
            end_date = today.replace(month=today.month + 1, day=1) - timedelta(days=1)
    elif filter_type == 'year':
        start_date = today.replace(month=1, day=1)
        end_date = today.replace(month=12, day=31)
    else:  # Custom range
        start_date_str = request.GET.get('start_date')
        end_date_str = request.GET.get('end_date')

        if start_date_str and end_date_str:
            start_date = datetime.strptime(start_date_str, '%Y-%m-%d').date()
            end_date = datetime.strptime(end_date_str, '%Y-%m-%d').date()
        else:
            # Default to current month if no custom range provided
            start_date = today.replace(day=1)
            if today.month == 12:
                end_date = today.replace(year=today.year + 1, month=1, day=1) - timedelta(days=1)
            else:
                end_date = today.replace(month=today.month + 1, day=1) - timedelta(days=1)

    return {
        'start_date': start_date,
        'end_date': end_date,
        'filter_type': filter_type
    }

# Helper function to get date range based on filter (for backward compatibility)
def get_date_range(request):
    filters = get_filters(request)
    return filters['start_date'], filters['end_date']

# Helper function to get income data
def get_income_data(start_date, end_date, payment_method=None, operator=None):
    # Build base filters for date range
    membership_filters = {
        'payment_date__date__gte': start_date,
        'payment_date__date__lte': end_date
    }

    product_filters = {
        'date__date__gte': start_date,
        'date__date__lte': end_date
    }

    paypervisit_filters = {
        'date__date__gte': start_date,
        'date__date__lte': end_date
    }

    deposit_filters = {
        'transaction_date__date__gte': start_date,
        'transaction_date__date__lte': end_date,
        'transaction_type': 'deposit'
    }

    # Add payment method filter if provided
    if payment_method:
        membership_filters['payment_method'] = payment_method
        product_filters['payment_method'] = payment_method
        paypervisit_filters['payment_method'] = payment_method
        deposit_filters['payment_method'] = payment_method

    # Add operator filter if provided
    if operator:
        membership_filters['collector__username'] = operator
        product_filters['sold_by__username'] = operator
        paypervisit_filters['received_by__username'] = operator
        deposit_filters['staff__username'] = operator

    # Get membership payments
    membership_payments = Payment.objects.filter(**membership_filters).aggregate(
        total=Sum('amount_khr'),
        count=Count('id')
    )

    # Get product sales
    product_sales = Sale.objects.filter(**product_filters).aggregate(
        total=Sum('total_amount'),
        count=Count('id')
    )

    # Get pay-per-visit revenue
    paypervisit_revenue = PayPerVisit.objects.filter(**paypervisit_filters).aggregate(
        total=Sum('amount'),
        count=Count('id')
    )

    # Get deposits from finance module
    deposits = Transaction.objects.filter(**deposit_filters).aggregate(
        total=Sum('amount_khr'),
        count=Count('id')
    )

    # Calculate totals
    total_income = (
        (membership_payments['total'] or 0) +
        (product_sales['total'] or 0) +
        (paypervisit_revenue['total'] or 0) +
        (deposits['total'] or 0)
    )

    # Prepare data for return
    income_data = {
        'total': total_income,
        'sources': {
            'membership': {
                'total': membership_payments['total'] or 0,
                'count': membership_payments['count'] or 0,
                'percentage': round((membership_payments['total'] or 0) / total_income * 100, 1) if total_income > 0 else 0
            },
            'product_sales': {
                'total': product_sales['total'] or 0,
                'count': product_sales['count'] or 0,
                'percentage': round((product_sales['total'] or 0) / total_income * 100, 1) if total_income > 0 else 0
            },
            'paypervisit': {
                'total': paypervisit_revenue['total'] or 0,
                'count': paypervisit_revenue['count'] or 0,
                'percentage': round((paypervisit_revenue['total'] or 0) / total_income * 100, 1) if total_income > 0 else 0
            },
            'deposits': {
                'total': deposits['total'] or 0,
                'count': deposits['count'] or 0,
                'percentage': round((deposits['total'] or 0) / total_income * 100, 1) if total_income > 0 else 0
            }
        }
    }

    return income_data

# Helper function to get expense data
def get_expense_data(start_date, end_date, payment_method=None, operator=None):
    # Build base filters for date range
    salary_filters = {
        'payment_date__date__gte': start_date,
        'payment_date__date__lte': end_date,
        'payment_status': 'paid'
    }

    bill_filters = {
        'payment_date__date__gte': start_date,
        'payment_date__date__lte': end_date,
        'payment_status': 'paid'
    }

    purchase_filters = {
        'date__date__gte': start_date,
        'date__date__lte': end_date
    }

    withdrawal_filters = {
        'transaction_date__date__gte': start_date,
        'transaction_date__date__lte': end_date,
        'transaction_type': 'withdrawal'
    }

    # Add payment method filter if provided
    if payment_method:
        # Note: SalaryPayment might not have payment_method field
        bill_filters['payment_method'] = payment_method
        withdrawal_filters['payment_method'] = payment_method

    # Add operator filter if provided
    if operator:
        bill_filters['paid_by__username'] = operator
        purchase_filters['created_by__username'] = operator
        withdrawal_filters['staff__username'] = operator

    # Get salary payments
    salary_payments = SalaryPayment.objects.filter(**salary_filters).aggregate(
        total=Sum('final_pay'),
        count=Count('id')
    )

    # Get bill payments
    bill_payments = Bill.objects.filter(**bill_filters).aggregate(
        total=Sum('amount_khr'),
        count=Count('id')
    )

    # Get product purchases
    product_purchases = Purchase.objects.filter(**purchase_filters).aggregate(
        total=Sum('total_amount'),
        count=Count('id')
    )

    # Get withdrawals from finance module
    withdrawals = Transaction.objects.filter(**withdrawal_filters).aggregate(
        total=Sum('amount_khr'),
        count=Count('id')
    )

    # Calculate totals
    total_expenses = (
        (salary_payments['total'] or 0) +
        (bill_payments['total'] or 0) +
        (product_purchases['total'] or 0) +
        (withdrawals['total'] or 0)
    )

    # Prepare data for return
    expense_data = {
        'total': total_expenses,
        'categories': {
            'salaries': {
                'total': salary_payments['total'] or 0,
                'count': salary_payments['count'] or 0,
                'percentage': round((salary_payments['total'] or 0) / total_expenses * 100, 1) if total_expenses > 0 else 0
            },
            'bills': {
                'total': bill_payments['total'] or 0,
                'count': bill_payments['count'] or 0,
                'percentage': round((bill_payments['total'] or 0) / total_expenses * 100, 1) if total_expenses > 0 else 0
            },
            'purchases': {
                'total': product_purchases['total'] or 0,
                'count': product_purchases['count'] or 0,
                'percentage': round((product_purchases['total'] or 0) / total_expenses * 100, 1) if total_expenses > 0 else 0
            },
            'withdrawals': {
                'total': withdrawals['total'] or 0,
                'count': withdrawals['count'] or 0,
                'percentage': round((withdrawals['total'] or 0) / total_expenses * 100, 1) if total_expenses > 0 else 0
            }
        }
    }

    return expense_data

# Main dashboard view
@login_required
@module_permission_required(module='financialreport', required_level='view')
def index(request):
    # Get all filters
    filters = get_filters(request)
    start_date = filters['start_date']
    end_date = filters['end_date']

    # Get income and expense data
    income_data = get_income_data(start_date, end_date)
    expense_data = get_expense_data(start_date, end_date)

    # Calculate balance
    balance = income_data['total'] - expense_data['total']

    # Format date range for display
    if start_date == end_date:
        date_range = start_date.strftime('%d %b %Y')
    else:
        date_range = f"{start_date.strftime('%d %b %Y')} - {end_date.strftime('%d %b %Y')}"

    context = {
        'title': 'Financial Report Dashboard',
        'date_range': date_range,
        'filter': filters['filter_type'],
        'start_date': start_date.strftime('%Y-%m-%d'),
        'end_date': end_date.strftime('%Y-%m-%d'),
        'income_total': income_data['total'],
        'expense_total': expense_data['total'],
        'balance': balance,
        'income_sources': income_data['sources'],
        'expense_categories': expense_data['categories'],
    }

    return render(request, 'financialreport/index.html', context)

# Income report view
@login_required
@module_permission_required(module='financialreport', required_level='view')
def income_report(request):
    # Get all filters
    filters = get_filters(request)
    start_date = filters['start_date']
    end_date = filters['end_date']

    # Build base filters for date range
    membership_filters = {
        'payment_date__date__gte': start_date,
        'payment_date__date__lte': end_date
    }

    product_filters = {
        'date__date__gte': start_date,
        'date__date__lte': end_date
    }

    paypervisit_filters = {
        'date__date__gte': start_date,
        'date__date__lte': end_date
    }

    deposit_filters = {
        'transaction_date__date__gte': start_date,
        'transaction_date__date__lte': end_date,
        'transaction_type': 'deposit'
    }

    # Get detailed income data
    # Membership payments
    membership_payments = Payment.objects.filter(**membership_filters).order_by('-payment_date')
    membership_total = membership_payments.aggregate(total=Sum('amount_khr'))['total'] or 0

    # Product sales
    product_sales = Sale.objects.filter(**product_filters).order_by('-date')
    product_total = product_sales.aggregate(total=Sum('total_amount'))['total'] or 0

    # Pay-per-visit revenue
    paypervisit_revenue = PayPerVisit.objects.filter(**paypervisit_filters).order_by('-date')
    paypervisit_total = paypervisit_revenue.aggregate(total=Sum('amount'))['total'] or 0

    # Get deposits from finance module
    deposits = Transaction.objects.filter(**deposit_filters).order_by('-transaction_date')
    deposit_total = deposits.aggregate(total=Sum('amount_khr'))['total'] or 0

    # Calculate total income
    total_income = membership_total + product_total + paypervisit_total + deposit_total

    # Get available templates
    templates = ReportTemplate.objects.filter(template_type='income').order_by('-is_default', 'name')

    # Format date range for display
    if start_date == end_date:
        date_range = start_date.strftime('%d %b %Y')
    else:
        date_range = f"{start_date.strftime('%d %b %Y')} - {end_date.strftime('%d %b %Y')}"

    context = {
        'title': 'Income Report',
        'date_range': date_range,
        'filter': filters['filter_type'],
        'start_date': start_date.strftime('%Y-%m-%d'),
        'end_date': end_date.strftime('%Y-%m-%d'),
        'membership_payments': membership_payments,
        'product_sales': product_sales,
        'paypervisit_revenue': paypervisit_revenue,
        'deposits': deposits,
        'membership_total': membership_total,
        'product_total': product_total,
        'paypervisit_total': paypervisit_total,
        'deposit_total': deposit_total,
        'total_income': total_income,
        'templates': templates,
    }

    return render(request, 'financialreport/income_report.html', context)

# Expense report view
@login_required
@module_permission_required(module='financialreport', required_level='view')
def expense_report(request):
    # Get all filters
    filters = get_filters(request)
    start_date = filters['start_date']
    end_date = filters['end_date']

    # Build base filters for date range
    salary_filters = {
        'payment_date__date__gte': start_date,
        'payment_date__date__lte': end_date,
        'payment_status': 'paid'
    }

    bill_filters = {
        'payment_date__date__gte': start_date,
        'payment_date__date__lte': end_date,
        'payment_status': 'paid'
    }

    purchase_filters = {
        'date__date__gte': start_date,
        'date__date__lte': end_date
    }

    withdrawal_filters = {
        'transaction_date__date__gte': start_date,
        'transaction_date__date__lte': end_date,
        'transaction_type': 'withdrawal'
    }

    # Get detailed expense data
    # Salary payments
    salary_payments = SalaryPayment.objects.filter(**salary_filters).order_by('-payment_date')
    salary_total = salary_payments.aggregate(total=Sum('final_pay'))['total'] or 0

    # Bill payments
    bill_payments = Bill.objects.filter(**bill_filters).order_by('-payment_date')
    bill_total = bill_payments.aggregate(total=Sum('amount_khr'))['total'] or 0

    # Product purchases
    product_purchases = Purchase.objects.filter(**purchase_filters).order_by('-date')
    purchase_total = product_purchases.aggregate(total=Sum('total_amount'))['total'] or 0

    # Get withdrawals from finance module
    withdrawals = Transaction.objects.filter(**withdrawal_filters).order_by('-transaction_date')
    withdrawal_total = withdrawals.aggregate(total=Sum('amount_khr'))['total'] or 0

    # Calculate total expenses
    total_expenses = salary_total + bill_total + purchase_total + withdrawal_total

    # Get available templates
    templates = ReportTemplate.objects.filter(template_type='expense').order_by('-is_default', 'name')

    # Format date range for display
    if start_date == end_date:
        date_range = start_date.strftime('%d %b %Y')
    else:
        date_range = f"{start_date.strftime('%d %b %Y')} - {end_date.strftime('%d %b %Y')}"

    context = {
        'title': 'Expense Report',
        'date_range': date_range,
        'filter': filters['filter_type'],
        'start_date': start_date.strftime('%Y-%m-%d'),
        'end_date': end_date.strftime('%Y-%m-%d'),
        'salary_payments': salary_payments,
        'bill_payments': bill_payments,
        'product_purchases': product_purchases,
        'withdrawals': withdrawals,
        'salary_total': salary_total,
        'bill_total': bill_total,
        'purchase_total': purchase_total,
        'withdrawal_total': withdrawal_total,
        'total_expenses': total_expenses,
        'templates': templates,
    }

    return render(request, 'financialreport/expense_report.html', context)

# Balance report view
@login_required
@module_permission_required(module='financialreport', required_level='view')
def balance_report(request):
    # Get all filters
    filters = get_filters(request)
    start_date = filters['start_date']
    end_date = filters['end_date']

    # Get income and expense data
    income_data = get_income_data(start_date, end_date)
    expense_data = get_expense_data(start_date, end_date)

    # Calculate balance
    balance = income_data['total'] - expense_data['total']

    # Format date range for display
    if start_date == end_date:
        date_range = start_date.strftime('%d %b %Y')
    else:
        date_range = f"{start_date.strftime('%d %b %Y')} - {end_date.strftime('%d %b %Y')}"

    # Calculate balance percentage
    balance_percentage = 0
    if income_data['total'] > 0:
        balance_percentage = round((balance / income_data['total']) * 100, 1)

    # Get available templates
    templates = ReportTemplate.objects.filter(template_type='balance').order_by('-is_default', 'name')

    # Format date range for display
    if start_date == end_date:
        date_range = start_date.strftime('%d %b %Y')
    else:
        date_range = f"{start_date.strftime('%d %b %Y')} - {end_date.strftime('%d %b %Y')}"

    context = {
        'title': 'Balance Report',
        'date_range': date_range,
        'filter': filters['filter_type'],
        'start_date': start_date.strftime('%Y-%m-%d'),
        'end_date': end_date.strftime('%Y-%m-%d'),
        'income_total': income_data['total'],
        'expense_total': expense_data['total'],
        'balance': balance,
        'balance_percentage': balance_percentage,
        'income_sources': income_data['sources'],
        'expense_categories': expense_data['categories'],
        'templates': templates,
    }

    return render(request, 'financialreport/balance_report.html', context)

# API endpoints for charts and data
@login_required
@module_permission_required(module='financialreport', required_level='view')
def api_summary(request):
    # Get all filters
    filters = get_filters(request)
    start_date = filters['start_date']
    end_date = filters['end_date']

    # Get income and expense data
    income_data = get_income_data(start_date, end_date)
    expense_data = get_expense_data(start_date, end_date)

    # Calculate balance
    balance = income_data['total'] - expense_data['total']

    data = {
        'income_total': income_data['total'],
        'expense_total': expense_data['total'],
        'balance': balance,
        'income_sources': income_data['sources'],
        'expense_categories': expense_data['categories'],
    }

    return JsonResponse(data)

@login_required
@module_permission_required(module='financialreport', required_level='view')
def api_income_data(request):
    # Get all filters
    filters = get_filters(request)
    start_date = filters['start_date']
    end_date = filters['end_date']

    income_data = get_income_data(start_date, end_date)
    return JsonResponse(income_data)

@login_required
@module_permission_required(module='financialreport', required_level='view')
def api_expense_data(request):
    # Get all filters
    filters = get_filters(request)
    start_date = filters['start_date']
    end_date = filters['end_date']

    expense_data = get_expense_data(start_date, end_date)
    return JsonResponse(expense_data)

@login_required
@module_permission_required(module='financialreport', required_level='view')
def api_monthly_data(request):
    # Get year from request or use current year
    year = int(request.GET.get('year', timezone.now().year))

    # Initialize data structure for monthly data
    monthly_data = {
        'labels': ['Jan', 'Feb', 'Mar', 'Apr', 'May', 'Jun', 'Jul', 'Aug', 'Sep', 'Oct', 'Nov', 'Dec'],
        'income': [0] * 12,
        'expense': [0] * 12,
        'balance': [0] * 12
    }

    # Get monthly income and expense data
    for month in range(1, 13):
        # Set date range for the month
        if month == 12:
            start_date = datetime(year, month, 1).date()
            end_date = datetime(year + 1, 1, 1).date() - timedelta(days=1)
        else:
            start_date = datetime(year, month, 1).date()
            end_date = datetime(year, month + 1, 1).date() - timedelta(days=1)

        # Get income and expense data for the month
        income_data = get_income_data(start_date, end_date)
        expense_data = get_expense_data(start_date, end_date)

        # Calculate balance
        balance = income_data['total'] - expense_data['total']

        # Update monthly data
        monthly_data['income'][month - 1] = income_data['total']
        monthly_data['expense'][month - 1] = expense_data['total']
        monthly_data['balance'][month - 1] = balance

    return JsonResponse(monthly_data)

# Utility function to render HTML to PDF
def render_to_pdf(template_src, context_dict={}):
    template = get_template(template_src)
    html = template.render(context_dict)
    result = BytesIO()
    pdf = pisa.pisaDocument(BytesIO(html.encode("UTF-8")), result)
    if not pdf.err:
        return HttpResponse(result.getvalue(), content_type='application/pdf')
    return HttpResponse('Error generating PDF', status=400)

# Utility function to render the PDF template as HTML for printing
def render_for_print(template_src, context_dict={}):
    template = get_template(template_src)
    html = template.render(context_dict)
    return HttpResponse(html)

# Template management views
@login_required
@module_permission_required(module='financialreport', required_level='view')
def template_list(request):
    """
    List all report templates
    """
    templates = ReportTemplate.objects.all().order_by('template_type', '-is_default', 'name')

    # Get template counts by type
    income_templates_count = ReportTemplate.objects.filter(template_type='income').count()
    expense_templates_count = ReportTemplate.objects.filter(template_type='expense').count()
    balance_templates_count = ReportTemplate.objects.filter(template_type='balance').count()

    context = {
        'title': 'Report Templates',
        'templates': templates,
        'income_templates_count': income_templates_count,
        'expense_templates_count': expense_templates_count,
        'balance_templates_count': balance_templates_count,
    }
    return render(request, 'financialreport/template_list.html', context)

@login_required
@module_permission_required(module='financialreport', required_level='edit')
def create_template(request):
    """
    Create a new report template
    """
    # Get template type from URL parameter
    initial_type = request.GET.get('type', 'income')

    if request.method == 'POST':
        name = request.POST.get('name')
        template_type = request.POST.get('template_type')
        is_default = request.POST.get('is_default') == 'on'

        # Styling options
        header_color = request.POST.get('header_color', '#2563eb')
        text_color = request.POST.get('text_color', '#333333')
        accent_color = request.POST.get('accent_color', '#2563eb')
        background_color = request.POST.get('background_color', '#ffffff')
        table_header_color = request.POST.get('table_header_color', '#f8f9fa')

        # Content options
        show_logo = request.POST.get('show_logo') == 'on'
        show_footer = request.POST.get('show_footer') == 'on'
        footer_text = request.POST.get('footer_text', 'Legend Fitness Club')

        # Create the template
        ReportTemplate.objects.create(
            name=name,
            template_type=template_type,
            is_default=is_default,
            header_color=header_color,
            text_color=text_color,
            accent_color=accent_color,
            background_color=background_color,
            table_header_color=table_header_color,
            show_logo=show_logo,
            show_footer=show_footer,
            footer_text=footer_text
        )

        return redirect('financialreport:template_list')

    # Set default colors based on template type
    default_colors = {
        'income': {
            'header_color': '#2563eb',
            'accent_color': '#2563eb',
        },
        'expense': {
            'header_color': '#e11d48',
            'accent_color': '#e11d48',
        },
        'balance': {
            'header_color': '#16a34a',
            'accent_color': '#16a34a',
        }
    }

    context = {
        'title': 'Create Report Template',
        'initial_type': initial_type,
        'default_colors': default_colors[initial_type],
    }
    return render(request, 'financialreport/template_form.html', context)

@login_required
@module_permission_required(module='financialreport', required_level='edit')
def edit_template(request, pk):
    """
    Edit an existing report template
    """
    template = get_object_or_404(ReportTemplate, pk=pk)

    if request.method == 'POST':
        template.name = request.POST.get('name')
        template.template_type = request.POST.get('template_type')
        template.is_default = request.POST.get('is_default') == 'on'

        # Styling options
        template.header_color = request.POST.get('header_color', '#2563eb')
        template.text_color = request.POST.get('text_color', '#333333')
        template.accent_color = request.POST.get('accent_color', '#2563eb')
        template.background_color = request.POST.get('background_color', '#ffffff')
        template.table_header_color = request.POST.get('table_header_color', '#f8f9fa')

        # Content options
        template.show_logo = request.POST.get('show_logo') == 'on'
        template.show_footer = request.POST.get('show_footer') == 'on'
        template.footer_text = request.POST.get('footer_text', 'Legend Fitness Club')

        template.save()

        return redirect('financialreport:template_list')

    context = {
        'title': 'Edit Report Template',
        'template': template,
    }
    return render(request, 'financialreport/template_form.html', context)

@login_required
@module_permission_required(module='financialreport', required_level='full')
def delete_template(request, pk):
    """
    Delete a report template
    """
    template = get_object_or_404(ReportTemplate, pk=pk)

    if request.method == 'POST':
        template.delete()
        return redirect('financialreport:template_list')

    context = {
        'title': 'Delete Report Template',
        'template': template,
    }
    return render(request, 'financialreport/template_confirm_delete.html', context)

@login_required
@module_permission_required(module='financialreport', required_level='view')
def preview_template(request, pk):
    """
    Preview a report template
    """
    template = get_object_or_404(ReportTemplate, pk=pk)

    # Get sample data for preview
    start_date = timezone.now().date() - timedelta(days=30)
    end_date = timezone.now().date()

    # Create sample data for preview
    membership_payments = []
    for i in range(1, 6):
        membership_payments.append({
            'payment_date': timezone.now() - timedelta(days=i*3),
            'member': {'name': f'Member {i}'},
            'invoice_no': f'INV-{2025}0{i}',
            'amount_khr': 250000 * i,
            'collector': {'username': 'admin'}
        })

    product_sales = []
    for i in range(1, 4):
        product_sales.append({
            'date': timezone.now() - timedelta(days=i*2),
            'trxId': f'TRX-{2025}0{i}',
            'total_amount': 150000 * i,
            'payment_method': 'cash',
            'get_payment_method_display': 'Cash',
            'sold_by': {'username': 'cashier'}
        })

    paypervisit_revenue = []
    for i in range(1, 3):
        paypervisit_revenue.append({
            'date': timezone.now() - timedelta(days=i),
            'trxId': f'PPV-{2025}0{i}',
            'num_people': i * 5,
            'amount': 20000 * i,
            'received_by': {'username': 'cashier'}
        })

    if template.template_type == 'income':
        # Sample income data
        membership_total = 1500000
        product_total = 750000
        paypervisit_total = 250000
        total_income = membership_total + product_total + paypervisit_total

        context = {
            'title': 'Income Report',
            'date_range': f"{start_date.strftime('%d %b %Y')} - {end_date.strftime('%d %b %Y')}",
            'membership_payments': membership_payments,
            'product_sales': product_sales,
            'paypervisit_revenue': paypervisit_revenue,
            'membership_total': membership_total,
            'product_total': product_total,
            'paypervisit_total': paypervisit_total,
            'total_income': total_income,
            'template': template,
            'now': timezone.now(),
            'is_preview': True,
        }
        return render(request, 'financialreport/pdf/income_report_pdf.html', context)

    elif template.template_type == 'expense':
        # Create sample expense data
        salary_payments = []
        for i in range(1, 4):
            salary_payments.append({
                'payment_date': timezone.now() - timedelta(days=i*5),
                'employee': {'name': f'Employee {i}'},
                'payroll_id': f'PAY-{2025}0{i}',
                'final_pay': 300000 + (i * 50000),
                'payment_status': 'paid',
                'get_payment_status_display': 'Paid'
            })

        bill_payments = []
        for i in range(1, 3):
            bill_payments.append({
                'payment_date': timezone.now() - timedelta(days=i*7),
                'provider': f'Provider {i}',
                'bill_id': f'BILL-{2025}0{i}',
                'amount_khr': 250000 * i,
                'category': 'electricity' if i == 1 else 'water',
                'get_category_display': 'Electricity' if i == 1 else 'Water',
                'payment_status': 'paid',
                'get_payment_status_display': 'Paid',
                'paid_by': {'username': 'admin'}
            })

        product_purchases = []
        for i in range(1, 3):
            product_purchases.append({
                'date': timezone.now() - timedelta(days=i*3),
                'purchase_id': f'PUR-{2025}0{i}',
                'supplier': f'Supplier {i}',
                'total_amount': 150000 * i,
                'purchased_by': {'username': 'manager'}
            })

        # Calculate totals
        salary_total = 1000000
        bill_total = 500000
        purchase_total = 300000
        total_expenses = salary_total + bill_total + purchase_total

        context = {
            'title': 'Expense Report',
            'date_range': f"{start_date.strftime('%d %b %Y')} - {end_date.strftime('%d %b %Y')}",
            'salary_payments': salary_payments,
            'bill_payments': bill_payments,
            'product_purchases': product_purchases,
            'salary_total': salary_total,
            'bill_total': bill_total,
            'purchase_total': purchase_total,
            'total_expenses': total_expenses,
            'template': template,
            'now': timezone.now(),
            'is_preview': True,
        }
        return render(request, 'financialreport/pdf/expense_report_pdf.html', context)

    elif template.template_type == 'balance':
        # Sample balance data
        income_total = 3000000
        expense_total = 2100000
        balance = income_total - expense_total

        context = {
            'title': 'Balance Report',
            'date_range': f"{start_date.strftime('%d %b %Y')} - {end_date.strftime('%d %b %Y')}",
            'income_total': income_total,
            'expense_total': expense_total,
            'balance': balance,
            'balance_percentage': round((balance / income_total) * 100, 1),
            'income_sources': {
                'membership': {'total': 1500000, 'percentage': 50},
                'product_sales': {'total': 750000, 'percentage': 25},
                'paypervisit': {'total': 250000, 'percentage': 8.3},
                'deposits': {'total': 500000, 'percentage': 16.7},
            },
            'expense_categories': {
                'salaries': {'total': 1000000, 'percentage': 47.6},
                'bills': {'total': 500000, 'percentage': 23.8},
                'purchases': {'total': 300000, 'percentage': 14.3},
                'withdrawals': {'total': 300000, 'percentage': 14.3},
            },
            'template': template,
            'now': timezone.now(),
            'is_preview': True,
        }
        return render(request, 'financialreport/pdf/balance_report_pdf.html', context)

# Export functionality
@login_required
@module_permission_required(module='financialreport', required_level='view')
def export_pdf(request, report_type):
    # Get all filters
    filters = get_filters(request)
    start_date = filters['start_date']
    end_date = filters['end_date']

    # Get the template to use
    template_id = request.GET.get('template')
    report_template = None

    if template_id:
        report_template = ReportTemplate.objects.filter(id=template_id, template_type=report_type).first()

    if not report_template:
        # Use default template for this report type
        report_template = ReportTemplate.objects.filter(template_type=report_type, is_default=True).first()

    if not report_template:
        # If no template exists, create a default one
        report_template = ReportTemplate.objects.create(
            name=f"Default {report_type.title()} Template",
            template_type=report_type,
            is_default=True
        )

    # Format date range for display
    if start_date == end_date:
        date_range = start_date.strftime('%d %b %Y')
    else:
        date_range = f"{start_date.strftime('%d %b %Y')} - {end_date.strftime('%d %b %Y')}"

    # Set filename for the PDF
    filename = f"{report_type}_report_{start_date.strftime('%Y%m%d')}_{end_date.strftime('%Y%m%d')}.pdf"

    if report_type == 'income':
        # Build base filters for date range
        membership_filters = {
            'payment_date__date__gte': start_date,
            'payment_date__date__lte': end_date
        }

        product_filters = {
            'date__date__gte': start_date,
            'date__date__lte': end_date
        }

        paypervisit_filters = {
            'date__date__gte': start_date,
            'date__date__lte': end_date
        }

        deposit_filters = {
            'transaction_date__date__gte': start_date,
            'transaction_date__date__lte': end_date,
            'transaction_type': 'deposit'
        }



        # Get income data with filters
        membership_payments = Payment.objects.filter(**membership_filters).order_by('-payment_date')
        membership_total = membership_payments.aggregate(total=Sum('amount_khr'))['total'] or 0

        product_sales = Sale.objects.filter(**product_filters).order_by('-date')
        product_total = product_sales.aggregate(total=Sum('total_amount'))['total'] or 0

        paypervisit_revenue = PayPerVisit.objects.filter(**paypervisit_filters).order_by('-date')
        paypervisit_total = paypervisit_revenue.aggregate(total=Sum('amount'))['total'] or 0

        # Get deposits from finance module
        deposits = Transaction.objects.filter(**deposit_filters).order_by('-transaction_date')
        deposit_total = deposits.aggregate(total=Sum('amount_khr'))['total'] or 0

        total_income = membership_total + product_total + paypervisit_total + deposit_total

        # No additional filter information
        filter_info = ""

        context = {
            'title': 'Income Report',
            'date_range': date_range + filter_info,
            'membership_payments': membership_payments,
            'product_sales': product_sales,
            'paypervisit_revenue': paypervisit_revenue,
            'deposits': deposits,
            'membership_total': membership_total,
            'product_total': product_total,
            'paypervisit_total': paypervisit_total,
            'deposit_total': deposit_total,
            'total_income': total_income,
            'is_pdf': True,
            'now': timezone.now(),
            'template': report_template,
        }

        # Generate PDF
        pdf = render_to_pdf('financialreport/pdf/income_report_pdf.html', context)

    elif report_type == 'expense':
        # Build base filters for date range
        salary_filters = {
            'payment_date__date__gte': start_date,
            'payment_date__date__lte': end_date,
            'payment_status': 'paid'
        }

        bill_filters = {
            'payment_date__date__gte': start_date,
            'payment_date__date__lte': end_date,
            'payment_status': 'paid'
        }

        purchase_filters = {
            'date__date__gte': start_date,
            'date__date__lte': end_date
        }

        withdrawal_filters = {
            'transaction_date__date__gte': start_date,
            'transaction_date__date__lte': end_date,
            'transaction_type': 'withdrawal'
        }



        # Get expense data with filters
        salary_payments = SalaryPayment.objects.filter(**salary_filters).order_by('-payment_date')
        salary_total = salary_payments.aggregate(total=Sum('final_pay'))['total'] or 0

        bill_payments = Bill.objects.filter(**bill_filters).order_by('-payment_date')
        bill_total = bill_payments.aggregate(total=Sum('amount_khr'))['total'] or 0

        product_purchases = Purchase.objects.filter(**purchase_filters).order_by('-date')
        purchase_total = product_purchases.aggregate(total=Sum('total_amount'))['total'] or 0

        # Get withdrawals from finance module
        withdrawals = Transaction.objects.filter(**withdrawal_filters).order_by('-transaction_date')
        withdrawal_total = withdrawals.aggregate(total=Sum('amount_khr'))['total'] or 0

        total_expenses = salary_total + bill_total + purchase_total + withdrawal_total

        # No additional filter information
        filter_info = ""

        context = {
            'title': 'Expense Report',
            'date_range': date_range + filter_info,
            'salary_payments': salary_payments,
            'bill_payments': bill_payments,
            'product_purchases': product_purchases,
            'withdrawals': withdrawals,
            'salary_total': salary_total,
            'bill_total': bill_total,
            'purchase_total': purchase_total,
            'withdrawal_total': withdrawal_total,
            'total_expenses': total_expenses,
            'is_pdf': True,
            'now': timezone.now(),
            'template': report_template,
        }

        # Generate PDF
        pdf = render_to_pdf('financialreport/pdf/expense_report_pdf.html', context)

    elif report_type == 'balance':
        # Get income and expense data
        income_data = get_income_data(start_date, end_date)
        expense_data = get_expense_data(start_date, end_date)

        # Calculate balance
        balance = income_data['total'] - expense_data['total']

        # Calculate balance percentage
        balance_percentage = 0
        if income_data['total'] > 0:
            balance_percentage = round((balance / income_data['total']) * 100, 1)

        # No additional filter information
        filter_info = ""

        context = {
            'title': 'Balance Report',
            'date_range': date_range + filter_info,
            'income_total': income_data['total'],
            'expense_total': expense_data['total'],
            'balance': balance,
            'balance_percentage': balance_percentage,
            'income_sources': income_data['sources'],
            'expense_categories': expense_data['categories'],
            'is_pdf': True,
            'now': timezone.now(),
            'template': report_template,
        }

        # Generate PDF
        pdf = render_to_pdf('financialreport/pdf/balance_report_pdf.html', context)

    else:
        return HttpResponse('Invalid report type', status=400)

    # Set content disposition header for download
    if pdf:
        response = HttpResponse(pdf, content_type='application/pdf')
        content_disposition = f'attachment; filename="{filename}"'
        response['Content-Disposition'] = content_disposition
        return response

    return HttpResponse('Error generating PDF', status=400)

# Preview functionality
@login_required
@module_permission_required(module='financialreport', required_level='view')
def preview_report(request, report_type, format_type):
    """
    Preview a report before exporting or printing
    """
    # Get all filters
    filters = get_filters(request)
    start_date = filters['start_date']
    end_date = filters['end_date']

    # Define empty variables for backward compatibility
    payment_method = ''
    operator = ''

    # Get the template to use
    template_id = request.GET.get('template')
    report_template = None

    if template_id:
        report_template = ReportTemplate.objects.filter(id=template_id, template_type=report_type).first()

    if not report_template:
        # Use default template for this report type
        report_template = ReportTemplate.objects.filter(template_type=report_type, is_default=True).first()

    if not report_template:
        # If no template exists, create a default one
        report_template = ReportTemplate.objects.create(
            name=f"Default {report_type.title()} Template",
            template_type=report_type,
            is_default=True
        )

    # Format date range for display
    if start_date == end_date:
        date_range = start_date.strftime('%d %b %Y')
    else:
        date_range = f"{start_date.strftime('%d %b %Y')} - {end_date.strftime('%d %b %Y')}"

    # Add filter information to date range
    filter_info = ""
    if payment_method:
        filter_info += f" | Payment Method: {dict(Payment.PAYMENT_METHOD_CHOICES).get(payment_method, payment_method)}"
    if operator:
        filter_info += f" | Operator: {operator}"

    date_range += filter_info

    if report_type == 'income':
        # Build base filters for date range
        membership_filters = {
            'payment_date__date__gte': start_date,
            'payment_date__date__lte': end_date
        }

        product_filters = {
            'date__date__gte': start_date,
            'date__date__lte': end_date
        }

        paypervisit_filters = {
            'date__date__gte': start_date,
            'date__date__lte': end_date
        }

        deposit_filters = {
            'transaction_date__date__gte': start_date,
            'transaction_date__date__lte': end_date,
            'transaction_type': 'deposit'
        }

        # Add payment method filter if provided
        if payment_method:
            membership_filters['payment_method'] = payment_method
            product_filters['payment_method'] = payment_method
            paypervisit_filters['payment_method'] = payment_method
            deposit_filters['payment_method'] = payment_method

        # Add operator filter if provided
        if operator:
            membership_filters['collector__username'] = operator
            product_filters['sold_by__username'] = operator
            paypervisit_filters['received_by__username'] = operator
            deposit_filters['staff__username'] = operator

        # Get income data with filters
        membership_payments = Payment.objects.filter(**membership_filters).order_by('-payment_date')
        membership_total = membership_payments.aggregate(total=Sum('amount_khr'))['total'] or 0

        product_sales = Sale.objects.filter(**product_filters).order_by('-date')
        product_total = product_sales.aggregate(total=Sum('total_amount'))['total'] or 0

        paypervisit_revenue = PayPerVisit.objects.filter(**paypervisit_filters).order_by('-date')
        paypervisit_total = paypervisit_revenue.aggregate(total=Sum('amount'))['total'] or 0

        # Get deposits from finance module
        deposits = Transaction.objects.filter(**deposit_filters).order_by('-transaction_date')
        deposit_total = deposits.aggregate(total=Sum('amount_khr'))['total'] or 0

        total_income = membership_total + product_total + paypervisit_total + deposit_total

        context = {
            'title': 'Income Report',
            'date_range': date_range,
            'membership_payments': membership_payments,
            'product_sales': product_sales,
            'paypervisit_revenue': paypervisit_revenue,
            'deposits': deposits,
            'membership_total': membership_total,
            'product_total': product_total,
            'paypervisit_total': paypervisit_total,
            'deposit_total': deposit_total,
            'total_income': total_income,
            'is_preview': True,
            'format_type': format_type,
            'now': timezone.now(),
            'template': report_template,
            'payment_method': payment_method,
            'operator': operator,
        }

        # Render the template for preview
        return render(request, 'financialreport/pdf/income_report_pdf.html', context)

    elif report_type == 'expense':
        # Build base filters for date range
        salary_filters = {
            'payment_date__date__gte': start_date,
            'payment_date__date__lte': end_date,
            'payment_status': 'paid'
        }

        bill_filters = {
            'payment_date__date__gte': start_date,
            'payment_date__date__lte': end_date,
            'payment_status': 'paid'
        }

        purchase_filters = {
            'date__date__gte': start_date,
            'date__date__lte': end_date
        }

        withdrawal_filters = {
            'transaction_date__date__gte': start_date,
            'transaction_date__date__lte': end_date,
            'transaction_type': 'withdrawal'
        }

        # Add payment method filter if provided
        if payment_method:
            # Note: SalaryPayment might not have payment_method field
            bill_filters['payment_method'] = payment_method
            withdrawal_filters['payment_method'] = payment_method

        # Add operator filter if provided
        if operator:
            bill_filters['paid_by__username'] = operator
            purchase_filters['created_by__username'] = operator
            withdrawal_filters['staff__username'] = operator

        # Get expense data with filters
        salary_payments = SalaryPayment.objects.filter(**salary_filters).order_by('-payment_date')
        salary_total = salary_payments.aggregate(total=Sum('final_pay'))['total'] or 0

        bill_payments = Bill.objects.filter(**bill_filters).order_by('-payment_date')
        bill_total = bill_payments.aggregate(total=Sum('amount_khr'))['total'] or 0

        product_purchases = Purchase.objects.filter(**purchase_filters).order_by('-date')
        purchase_total = product_purchases.aggregate(total=Sum('total_amount'))['total'] or 0

        # Get withdrawals from finance module
        withdrawals = Transaction.objects.filter(**withdrawal_filters).order_by('-transaction_date')
        withdrawal_total = withdrawals.aggregate(total=Sum('amount_khr'))['total'] or 0

        total_expenses = salary_total + bill_total + purchase_total + withdrawal_total

        context = {
            'title': 'Expense Report',
            'date_range': date_range,
            'salary_payments': salary_payments,
            'bill_payments': bill_payments,
            'product_purchases': product_purchases,
            'withdrawals': withdrawals,
            'salary_total': salary_total,
            'bill_total': bill_total,
            'purchase_total': purchase_total,
            'withdrawal_total': withdrawal_total,
            'total_expenses': total_expenses,
            'is_preview': True,
            'format_type': format_type,
            'now': timezone.now(),
            'template': report_template,
            'payment_method': payment_method,
            'operator': operator,
        }

        # Render the template for preview
        return render(request, 'financialreport/pdf/expense_report_pdf.html', context)

    elif report_type == 'balance':
        # Get income and expense data with filters
        income_data = get_income_data(start_date, end_date, payment_method, operator)
        expense_data = get_expense_data(start_date, end_date, payment_method, operator)

        # Calculate balance
        balance = income_data['total'] - expense_data['total']

        # Calculate balance percentage
        balance_percentage = 0
        if income_data['total'] > 0:
            balance_percentage = round((balance / income_data['total']) * 100, 1)

        context = {
            'title': 'Balance Report',
            'date_range': date_range,
            'income_total': income_data['total'],
            'expense_total': expense_data['total'],
            'balance': balance,
            'balance_percentage': balance_percentage,
            'income_sources': income_data['sources'],
            'expense_categories': expense_data['categories'],
            'is_preview': True,
            'format_type': format_type,
            'now': timezone.now(),
            'template': report_template,
            'payment_method': payment_method,
            'operator': operator,
        }

        # Render the template for preview
        return render(request, 'financialreport/pdf/balance_report_pdf.html', context)

    else:
        return HttpResponse('Invalid report type', status=400)

# Print functionality
@login_required
@module_permission_required(module='financialreport', required_level='view')
def print_report(request, report_type):
    # Get all filters
    filters = get_filters(request)
    start_date = filters['start_date']
    end_date = filters['end_date']

    # Define empty variables for backward compatibility
    payment_method = ''
    operator = ''

    # Get the template to use
    template_id = request.GET.get('template')
    report_template = None

    if template_id:
        report_template = ReportTemplate.objects.filter(id=template_id, template_type=report_type).first()

    if not report_template:
        # Use default template for this report type
        report_template = ReportTemplate.objects.filter(template_type=report_type, is_default=True).first()

    if not report_template:
        # If no template exists, create a default one
        report_template = ReportTemplate.objects.create(
            name=f"Default {report_type.title()} Template",
            template_type=report_type,
            is_default=True
        )

    # Format date range for display
    if start_date == end_date:
        date_range = start_date.strftime('%d %b %Y')
    else:
        date_range = f"{start_date.strftime('%d %b %Y')} - {end_date.strftime('%d %b %Y')}"

    # Add filter information to date range
    filter_info = ""
    if payment_method:
        filter_info += f" | Payment Method: {dict(Payment.PAYMENT_METHOD_CHOICES).get(payment_method, payment_method)}"
    if operator:
        filter_info += f" | Operator: {operator}"

    date_range += filter_info

    if report_type == 'income':
        # Build base filters for date range
        membership_filters = {
            'payment_date__date__gte': start_date,
            'payment_date__date__lte': end_date
        }

        product_filters = {
            'date__date__gte': start_date,
            'date__date__lte': end_date
        }

        paypervisit_filters = {
            'date__date__gte': start_date,
            'date__date__lte': end_date
        }

        deposit_filters = {
            'transaction_date__date__gte': start_date,
            'transaction_date__date__lte': end_date,
            'transaction_type': 'deposit'
        }

        # Add payment method filter if provided
        if payment_method:
            membership_filters['payment_method'] = payment_method
            product_filters['payment_method'] = payment_method
            paypervisit_filters['payment_method'] = payment_method
            deposit_filters['payment_method'] = payment_method

        # Add operator filter if provided
        if operator:
            membership_filters['collector__username'] = operator
            product_filters['sold_by__username'] = operator
            paypervisit_filters['received_by__username'] = operator
            deposit_filters['staff__username'] = operator

        # Get income data with filters
        membership_payments = Payment.objects.filter(**membership_filters).order_by('-payment_date')
        membership_total = membership_payments.aggregate(total=Sum('amount_khr'))['total'] or 0

        product_sales = Sale.objects.filter(**product_filters).order_by('-date')
        product_total = product_sales.aggregate(total=Sum('total_amount'))['total'] or 0

        paypervisit_revenue = PayPerVisit.objects.filter(**paypervisit_filters).order_by('-date')
        paypervisit_total = paypervisit_revenue.aggregate(total=Sum('amount'))['total'] or 0

        # Get deposits from finance module
        deposits = Transaction.objects.filter(**deposit_filters).order_by('-transaction_date')
        deposit_total = deposits.aggregate(total=Sum('amount_khr'))['total'] or 0

        total_income = membership_total + product_total + paypervisit_total + deposit_total

        context = {
            'title': 'Income Report',
            'date_range': date_range,
            'membership_payments': membership_payments,
            'product_sales': product_sales,
            'paypervisit_revenue': paypervisit_revenue,
            'deposits': deposits,
            'membership_total': membership_total,
            'product_total': product_total,
            'paypervisit_total': paypervisit_total,
            'deposit_total': deposit_total,
            'total_income': total_income,
            'is_print': True,
            'now': timezone.now(),
            'template': report_template,
            'payment_method': payment_method,
            'operator': operator,
        }

        # Render the template for printing
        return render_for_print('financialreport/pdf/income_report_pdf.html', context)

    elif report_type == 'expense':
        # Build base filters for date range
        salary_filters = {
            'payment_date__date__gte': start_date,
            'payment_date__date__lte': end_date,
            'payment_status': 'paid'
        }

        bill_filters = {
            'payment_date__date__gte': start_date,
            'payment_date__date__lte': end_date,
            'payment_status': 'paid'
        }

        purchase_filters = {
            'date__date__gte': start_date,
            'date__date__lte': end_date
        }

        withdrawal_filters = {
            'transaction_date__date__gte': start_date,
            'transaction_date__date__lte': end_date,
            'transaction_type': 'withdrawal'
        }

        # Add payment method filter if provided
        if payment_method:
            # Note: SalaryPayment might not have payment_method field
            bill_filters['payment_method'] = payment_method
            withdrawal_filters['payment_method'] = payment_method

        # Add operator filter if provided
        if operator:
            bill_filters['paid_by__username'] = operator
            purchase_filters['created_by__username'] = operator
            withdrawal_filters['staff__username'] = operator

        # Get expense data with filters
        salary_payments = SalaryPayment.objects.filter(**salary_filters).order_by('-payment_date')
        salary_total = salary_payments.aggregate(total=Sum('final_pay'))['total'] or 0

        bill_payments = Bill.objects.filter(**bill_filters).order_by('-payment_date')
        bill_total = bill_payments.aggregate(total=Sum('amount_khr'))['total'] or 0

        product_purchases = Purchase.objects.filter(**purchase_filters).order_by('-date')
        purchase_total = product_purchases.aggregate(total=Sum('total_amount'))['total'] or 0

        # Get withdrawals from finance module
        withdrawals = Transaction.objects.filter(**withdrawal_filters).order_by('-transaction_date')
        withdrawal_total = withdrawals.aggregate(total=Sum('amount_khr'))['total'] or 0

        total_expenses = salary_total + bill_total + purchase_total + withdrawal_total

        context = {
            'title': 'Expense Report',
            'date_range': date_range,
            'salary_payments': salary_payments,
            'bill_payments': bill_payments,
            'product_purchases': product_purchases,
            'withdrawals': withdrawals,
            'salary_total': salary_total,
            'bill_total': bill_total,
            'purchase_total': purchase_total,
            'withdrawal_total': withdrawal_total,
            'total_expenses': total_expenses,
            'is_print': True,
            'now': timezone.now(),
            'template': report_template,
            'payment_method': payment_method,
            'operator': operator,
        }

        # Render the template for printing
        return render_for_print('financialreport/pdf/expense_report_pdf.html', context)

    elif report_type == 'balance':
        # Get income and expense data with filters
        income_data = get_income_data(start_date, end_date, payment_method, operator)
        expense_data = get_expense_data(start_date, end_date, payment_method, operator)

        # Calculate balance
        balance = income_data['total'] - expense_data['total']

        # Calculate balance percentage
        balance_percentage = 0
        if income_data['total'] > 0:
            balance_percentage = round((balance / income_data['total']) * 100, 1)

        context = {
            'title': 'Balance Report',
            'date_range': date_range,
            'income_total': income_data['total'],
            'expense_total': expense_data['total'],
            'balance': balance,
            'balance_percentage': balance_percentage,
            'income_sources': income_data['sources'],
            'expense_categories': expense_data['categories'],
            'is_print': True,
            'now': timezone.now(),
            'template': report_template,
            'payment_method': payment_method,
            'operator': operator,
        }

        # Render the template for printing
        return render_for_print('financialreport/pdf/balance_report_pdf.html', context)

    else:
        return HttpResponse('Invalid report type', status=400)

@login_required
@module_permission_required(module='financialreport', required_level='view')
def export_csv(request, report_type):
    # Get all filters
    filters = get_filters(request)
    start_date = filters['start_date']
    end_date = filters['end_date']

    # Define empty variables for backward compatibility
    payment_method = ''
    operator = ''

    # Get the template to use
    template_id = request.GET.get('template')
    report_template = None

    if template_id:
        report_template = ReportTemplate.objects.filter(id=template_id, template_type=report_type).first()

    if not report_template:
        # Use default template for this report type
        report_template = ReportTemplate.objects.filter(template_type=report_type, is_default=True).first()

    if not report_template:
        # If no template exists, create a default one
        report_template = ReportTemplate.objects.create(
            name=f"Default {report_type.title()} Template",
            template_type=report_type,
            is_default=True
        )

    # Format date for filename
    filename = f"{report_type}_report_{start_date.strftime('%Y-%m-%d')}_to_{end_date.strftime('%Y-%m-%d')}.csv"

    # Add filter info to filename if filters are applied
    if payment_method:
        filename = f"{report_type}_report_{payment_method}_{start_date.strftime('%Y-%m-%d')}_to_{end_date.strftime('%Y-%m-%d')}.csv"
    if operator:
        filename = f"{report_type}_report_{operator}_{start_date.strftime('%Y-%m-%d')}_to_{end_date.strftime('%Y-%m-%d')}.csv"
    if payment_method and operator:
        filename = f"{report_type}_report_{payment_method}_{operator}_{start_date.strftime('%Y-%m-%d')}_to_{end_date.strftime('%Y-%m-%d')}.csv"

    response = HttpResponse(content_type='text/csv')
    response['Content-Disposition'] = f'attachment; filename="{filename}"'

    writer = csv.writer(response)

    if report_type == 'income':
        # Export income data
        writer.writerow(['Date', 'Source', 'Description', 'Amount (KHR)', 'Received By', 'Payment Method'])

        # Build base filters for date range
        membership_filters = {
            'payment_date__date__gte': start_date,
            'payment_date__date__lte': end_date
        }

        product_filters = {
            'date__date__gte': start_date,
            'date__date__lte': end_date
        }

        paypervisit_filters = {
            'date__date__gte': start_date,
            'date__date__lte': end_date
        }

        deposit_filters = {
            'transaction_date__date__gte': start_date,
            'transaction_date__date__lte': end_date,
            'transaction_type': 'deposit'
        }

        # Add payment method filter if provided
        if payment_method:
            membership_filters['payment_method'] = payment_method
            product_filters['payment_method'] = payment_method
            paypervisit_filters['payment_method'] = payment_method
            deposit_filters['payment_method'] = payment_method

        # Add operator filter if provided
        if operator:
            membership_filters['collector__username'] = operator
            product_filters['sold_by__username'] = operator
            paypervisit_filters['received_by__username'] = operator
            deposit_filters['staff__username'] = operator

        # Membership payments
        membership_payments = Payment.objects.filter(**membership_filters).order_by('-payment_date')

        for payment in membership_payments:
            writer.writerow([
                payment.payment_date.strftime('%Y-%m-%d'),
                'Membership Fee',
                f"{payment.member.name} - {payment.invoice_no}",
                payment.amount_khr,
                payment.collector.username if payment.collector else 'N/A',
                payment.get_payment_method_display()
            ])

        # Product sales
        product_sales = Sale.objects.filter(**product_filters).order_by('-date')

        for sale in product_sales:
            writer.writerow([
                sale.date.strftime('%Y-%m-%d'),
                'Product Sale',
                sale.trxId,
                sale.total_amount,
                sale.sold_by.username if sale.sold_by else 'N/A',
                sale.get_payment_method_display()
            ])

        # Pay-per-visit revenue
        paypervisit_revenue = PayPerVisit.objects.filter(**paypervisit_filters).order_by('-date')

        for visit in paypervisit_revenue:
            writer.writerow([
                visit.date.strftime('%Y-%m-%d'),
                'Pay-per-visit',
                f"{visit.num_people} people - {visit.trxId}",
                visit.amount,
                visit.received_by.username if visit.received_by else 'N/A',
                visit.get_payment_method_display()
            ])

        # Deposits
        deposits = Transaction.objects.filter(**deposit_filters).order_by('-transaction_date')

        for deposit in deposits:
            writer.writerow([
                deposit.transaction_date.strftime('%Y-%m-%d'),
                'Deposit',
                deposit.description or 'Cash Deposit',
                deposit.amount_khr,
                deposit.staff.username if deposit.staff else 'N/A',
                deposit.get_payment_method_display()
            ])

    elif report_type == 'expense':
        # Export expense data
        writer.writerow(['Date', 'Category', 'Description', 'Amount (KHR)', 'Paid By', 'Payment Method'])

        # Build base filters for date range
        salary_filters = {
            'payment_date__date__gte': start_date,
            'payment_date__date__lte': end_date,
            'payment_status': 'paid'
        }

        bill_filters = {
            'payment_date__date__gte': start_date,
            'payment_date__date__lte': end_date,
            'payment_status': 'paid'
        }

        purchase_filters = {
            'date__date__gte': start_date,
            'date__date__lte': end_date
        }

        withdrawal_filters = {
            'transaction_date__date__gte': start_date,
            'transaction_date__date__lte': end_date,
            'transaction_type': 'withdrawal'
        }

        # Add payment method filter if provided
        if payment_method:
            # Note: SalaryPayment might not have payment_method field
            bill_filters['payment_method'] = payment_method
            withdrawal_filters['payment_method'] = payment_method

        # Add operator filter if provided
        if operator:
            bill_filters['paid_by__username'] = operator
            purchase_filters['created_by__username'] = operator
            withdrawal_filters['staff__username'] = operator

        # Salary payments
        salary_payments = SalaryPayment.objects.filter(**salary_filters).order_by('-payment_date')

        for payment in salary_payments:
            writer.writerow([
                payment.payment_date.strftime('%Y-%m-%d'),
                'Salary',
                f"{payment.employee.name} - {payment.payroll_id}",
                payment.final_pay,
                'Admin',  # Assuming admin makes salary payments
                'N/A'  # Assuming salary payments don't have payment method
            ])

        # Bill payments
        bill_payments = Bill.objects.filter(**bill_filters).order_by('-payment_date')

        for bill in bill_payments:
            writer.writerow([
                bill.payment_date.strftime('%Y-%m-%d'),
                bill.get_category_display(),
                f"{bill.provider} - {bill.bill_id}",
                bill.amount_khr,
                bill.paid_by.username if bill.paid_by else 'N/A',
                bill.get_payment_method_display()
            ])

        # Product purchases
        product_purchases = Purchase.objects.filter(**purchase_filters).order_by('-date')

        for purchase in product_purchases:
            writer.writerow([
                purchase.date.strftime('%Y-%m-%d'),
                'Product Purchase',
                f"From {purchase.supplier.name if purchase.supplier else 'Unknown'} - {purchase.trxId}",
                purchase.total_amount,
                purchase.created_by.username if purchase.created_by else 'N/A',
                'N/A'  # Assuming purchases don't have payment method
            ])

        # Withdrawals
        withdrawals = Transaction.objects.filter(**withdrawal_filters).order_by('-transaction_date')

        for withdrawal in withdrawals:
            writer.writerow([
                withdrawal.transaction_date.strftime('%Y-%m-%d'),
                'Withdrawal',
                withdrawal.description or 'Cash Withdrawal',
                withdrawal.amount_khr,
                withdrawal.staff.username if withdrawal.staff else 'N/A',
                withdrawal.get_payment_method_display()
            ])

    elif report_type == 'balance':
        # Export balance summary with filters
        income_data = get_income_data(start_date, end_date, payment_method, operator)
        expense_data = get_expense_data(start_date, end_date, payment_method, operator)
        balance = income_data['total'] - expense_data['total']

        # Add filter information to header
        filter_info = []
        if payment_method:
            filter_info.append(f"Payment Method: {dict(Payment.PAYMENT_METHOD_CHOICES).get(payment_method, payment_method)}")
        if operator:
            filter_info.append(f"Operator: {operator}")

        if filter_info:
            writer.writerow(['Filters Applied'])
            for info in filter_info:
                writer.writerow([info])
            writer.writerow([])

        writer.writerow(['Category', 'Amount (KHR)'])
        writer.writerow(['Total Income', income_data['total']])
        writer.writerow(['Total Expenses', expense_data['total']])
        writer.writerow(['Balance', balance])
        writer.writerow([])

        # Income breakdown
        writer.writerow(['Income Sources', 'Amount (KHR)', 'Percentage'])
        for source, data in income_data['sources'].items():
            writer.writerow([source.replace('_', ' ').title(), data['total'], f"{data['percentage']}%"])

        writer.writerow([])

        # Expense breakdown
        writer.writerow(['Expense Categories', 'Amount (KHR)', 'Percentage'])
        for category, data in expense_data['categories'].items():
            writer.writerow([category.replace('_', ' ').title(), data['total'], f"{data['percentage']}%"])

    return response
