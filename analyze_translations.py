import polib
import os
from collections import defaultdict

def analyze_po_file(po_path):
    po = polib.pofile(po_path)
    
    # Basic statistics
    total = len(po)
    translated = len(po.translated_entries())
    untranslated = len(po.untranslated_entries())
    fuzzy = len(po.fuzzy_entries())
    
    print(f"Translation Statistics:")
    print(f"Total entries: {total}")
    print(f"Translated: {translated} ({translated/total*100:.1f}%)")
    print(f"Untranslated: {untranslated} ({untranslated/total*100:.1f}%)")
    print(f"Fuzzy (needs review): {fuzzy} ({fuzzy/total*100:.1f}%)")
    print()
    
    # Analyze template files referenced
    template_files = defaultdict(int)
    for entry in po:
        for source, _ in entry.occurrences:
            if 'templates' in source:
                template_files[source] += 1
    
    print(f"Template files referenced: {len(template_files)}")
    for file, count in sorted(template_files.items(), key=lambda x: x[1], reverse=True):
        print(f"- {file}: {count} strings")
    print()
    
    # List some untranslated strings
    if untranslated > 0:
        print(f"Sample of untranslated strings (showing first 10):")
        for i, entry in enumerate(po.untranslated_entries()):
            if i >= 10:
                break
            print(f"- \"{entry.msgid}\"")
        print()
    
    # Check for HTML files in templates that might not be referenced
    template_dir = os.path.join(os.path.dirname(po_path), '..', '..', '..', 'templates')
    if os.path.exists(template_dir):
        all_html_files = []
        for root, _, files in os.walk(template_dir):
            for file in files:
                if file.endswith('.html'):
                    rel_path = os.path.join(root, file).replace('\\', '/')
                    all_html_files.append(rel_path)
        
        referenced_files = set(template_files.keys())
        unreferenced_files = []
        
        for html_file in all_html_files:
            found = False
            for ref_file in referenced_files:
                if html_file.endswith(ref_file) or ref_file.endswith(html_file):
                    found = True
                    break
            if not found:
                unreferenced_files.append(html_file)
        
        if unreferenced_files:
            print(f"Potentially unreferenced HTML files: {len(unreferenced_files)}")
            for file in sorted(unreferenced_files):
                print(f"- {file}")
        else:
            print("All HTML files appear to be referenced in translations.")

if __name__ == "__main__":
    po_path = "locale/km/LC_MESSAGES/django.po"
    analyze_po_file(po_path)
