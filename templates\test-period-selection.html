<!DOCTYPE html>
<html>
<head>
    <title>Test Period Selection</title>
    <link rel="stylesheet" href="/static/css/dashboard-cards.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 20px;
            background-color: #f5f5f5;
        }
        .test-container {
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
            margin-bottom: 20px;
        }
        .test-info {
            background: #e3f2fd;
            padding: 15px;
            border-radius: 8px;
            margin-bottom: 20px;
            border-left: 4px solid #2196f3;
        }
        .test-info h3 {
            margin: 0 0 10px 0;
            color: #1976d2;
        }
        .test-info p {
            margin: 5px 0;
            color: #424242;
        }
    </style>
</head>
<body>
    <div class="test-container">
        <h1>Period Selection Test</h1>
        
        <div class="test-info">
            <h3>Test Instructions:</h3>
            <p>1. Open browser developer tools (F12) and go to Console tab</p>
            <p>2. Click on different period buttons (Day, Week, Month) in the cards below</p>
            <p>3. Watch the console for debugging messages</p>
            <p>4. Notice the visual changes: button active state, border flash, period indicator in subtitle</p>
            <p>5. Check Network tab to see API calls being made with correct period parameters</p>
        </div>
        
        <div class="report-cards-container">
            <!-- Test Pay-Per-Visit Card -->
            <div class="report-card paypervisit" data-report-type="paypervisit">
                <div class="report-card-header">
                    <h4 class="report-card-title">Pay-Per-Visit Report</h4>
                    <div class="report-card-icon paypervisit">
                        <i class="fas fa-walking"></i>
                    </div>
                </div>
                <div class="report-card-content">
                    <div class="report-card-main-value">50,000៛</div>
                    <div class="report-card-subtitle">Total Revenue</div>
                    <div class="report-card-metrics">
                        <div class="metric-item">
                            <div class="metric-value">25</div>
                            <div class="metric-label">Transactions</div>
                        </div>
                        <div class="metric-item">
                            <div class="metric-value">30</div>
                            <div class="metric-label">Visitors</div>
                        </div>
                    </div>
                </div>
                <div class="report-card-footer">
                    <div class="period-selector">
                        <button class="period-btn" data-period="day">Day</button>
                        <button class="period-btn" data-period="week">Week</button>
                        <button class="period-btn active" data-period="month">Month</button>
                    </div>
                </div>
            </div>

            <!-- Test Product Sales Card -->
            <div class="report-card product" data-report-type="product">
                <div class="report-card-header">
                    <h4 class="report-card-title">Product Sales Report</h4>
                    <div class="report-card-icon product">
                        <i class="fas fa-shopping-cart"></i>
                    </div>
                </div>
                <div class="report-card-content">
                    <div class="report-card-main-value">150,000៛</div>
                    <div class="report-card-subtitle">Total Sales</div>
                    <div class="report-card-metrics">
                        <div class="metric-item">
                            <div class="metric-value">15</div>
                            <div class="metric-label">Transactions</div>
                        </div>
                        <div class="metric-item">
                            <div class="metric-value">45</div>
                            <div class="metric-label">Items Sold</div>
                        </div>
                    </div>
                </div>
                <div class="report-card-footer">
                    <div class="period-selector">
                        <button class="period-btn" data-period="day">Day</button>
                        <button class="period-btn" data-period="week">Week</button>
                        <button class="period-btn active" data-period="month">Month</button>
                    </div>
                </div>
            </div>

            <!-- Test Overview Card -->
            <div class="report-card overview" data-report-type="overview">
                <div class="report-card-header">
                    <h4 class="report-card-title">Overview Report</h4>
                    <div class="report-card-icon overview">
                        <i class="fas fa-chart-pie"></i>
                    </div>
                </div>
                <div class="report-card-content">
                    <div class="report-card-main-value">125</div>
                    <div class="report-card-subtitle">Total Members</div>
                    <div class="report-card-metrics">
                        <div class="metric-item">
                            <div class="metric-value">120</div>
                            <div class="metric-label">Active</div>
                        </div>
                        <div class="metric-item">
                            <div class="metric-value">5</div>
                            <div class="metric-label">New</div>
                        </div>
                    </div>
                </div>
                <div class="report-card-footer">
                    <div class="period-selector">
                        <button class="period-btn" data-period="day">Day</button>
                        <button class="period-btn" data-period="week">Week</button>
                        <button class="period-btn active" data-period="month">Month</button>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script src="/static/js/dashboard-reports.js"></script>
    
    <script>
        // Additional test logging
        console.log('Period Selection Test Page Loaded');
        console.log('Dashboard Reports instance:', window.dashboardReports);
        
        // Add click event listener to log all clicks
        document.addEventListener('click', (e) => {
            console.log('Click detected on:', e.target);
            console.log('Target classes:', e.target.className);
            console.log('Target dataset:', e.target.dataset);
        });
    </script>
</body>
</html>
