from django.core.management.base import BaseCommand
from financialreport.models import ReportTemplate


class Command(BaseCommand):
    help = 'Creates default templates for financial reports'

    def handle(self, *args, **options):
        # Define the templates
        templates = [
            # Income Report Template
            {
                'name': 'Standard Income Report',
                'template_type': 'income',
                'is_default': True,
                'header_color': '#2563eb',  # Blue
                'text_color': '#333333',
                'accent_color': '#2563eb',  # Blue
                'background_color': '#ffffff',
                'table_header_color': '#f8f9fa',
                'show_logo': True,
                'show_footer': True,
                'footer_text': 'Legend Fitness Club'
            },
            # Expense Report Template
            {
                'name': 'Standard Expense Report',
                'template_type': 'expense',
                'is_default': True,
                'header_color': '#e11d48',  # Red
                'text_color': '#333333',
                'accent_color': '#e11d48',  # Red
                'background_color': '#ffffff',
                'table_header_color': '#f8f9fa',
                'show_logo': True,
                'show_footer': True,
                'footer_text': 'Legend Fitness Club'
            },
            # Balance Report Template
            {
                'name': 'Standard Balance Report',
                'template_type': 'balance',
                'is_default': True,
                'header_color': '#16a34a',  # Green
                'text_color': '#333333',
                'accent_color': '#16a34a',  # Green
                'background_color': '#ffffff',
                'table_header_color': '#f8f9fa',
                'show_logo': True,
                'show_footer': True,
                'footer_text': 'Legend Fitness Club'
            }
        ]

        # Create templates
        created_count = 0
        for template_data in templates:
            template_type = template_data['template_type']
            
            # Check if a default template of this type already exists
            existing_default = ReportTemplate.objects.filter(
                template_type=template_type, 
                is_default=True
            ).first()
            
            if existing_default:
                self.stdout.write(
                    self.style.WARNING(
                        f"Default template for {template_type} already exists: {existing_default.name}"
                    )
                )
                continue
                
            # Create the template
            template = ReportTemplate.objects.create(**template_data)
            created_count += 1
            self.stdout.write(
                self.style.SUCCESS(
                    f"Created default template: {template.name} (Type: {template.template_type})"
                )
            )
            
        if created_count == 0:
            self.stdout.write(self.style.WARNING("No new templates were created."))
        else:
            self.stdout.write(
                self.style.SUCCESS(f"Successfully created {created_count} default templates.")
            )
