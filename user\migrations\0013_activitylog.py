# Generated by Django 5.0.2 on 2025-05-16 22:12

import django.db.models.deletion
from django.conf import settings
from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('user', '0012_user_email_verified_user_last_activity_and_more'),
    ]

    operations = [
        migrations.CreateModel(
            name='ActivityLog',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('timestamp', models.DateTimeField(auto_now_add=True, verbose_name='Timestamp')),
                ('action_type', models.CharField(choices=[('login', 'Login'), ('logout', 'Logout'), ('profile_update', 'Profile Update'), ('password_change', 'Password Change'), ('user_create', 'User Created'), ('user_update', 'User Updated'), ('user_delete', 'User Deleted'), ('user_activate', 'User Activated'), ('user_deactivate', 'User Deactivated'), ('role_change', 'Role Changed'), ('permission_change', 'Permission Changed'), ('system_access_grant', 'System Access Granted'), ('system_access_revoke', 'System Access Revoked'), ('bulk_import', 'Bulk Import'), ('bulk_export', 'Bulk Export'), ('custom_role_create', 'Custom Role Created'), ('custom_role_update', 'Custom Role Updated'), ('custom_role_delete', 'Custom Role Deleted'), ('other', 'Other')], max_length=50, verbose_name='Action Type')),
                ('ip_address', models.GenericIPAddressField(blank=True, null=True, verbose_name='IP Address')),
                ('user_agent', models.TextField(blank=True, null=True, verbose_name='User Agent')),
                ('details', models.TextField(blank=True, null=True, verbose_name='Details')),
                ('target_user', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='targeted_activities', to=settings.AUTH_USER_MODEL)),
                ('user', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='activities', to=settings.AUTH_USER_MODEL)),
            ],
            options={
                'verbose_name': 'Activity Log',
                'verbose_name_plural': 'Activity Logs',
                'ordering': ['-timestamp'],
            },
        ),
    ]
