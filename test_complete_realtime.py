#!/usr/bin/env python
"""
Complete test for real-time permission updates
This script will:
1. Change permissions for a coach user
2. Verify WebSocket notifications are sent
3. Check if the changes are reflected in the system
"""

import os
import sys
import django
import time

# Setup Django environment
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'core.settings')
django.setup()

from settings.models import RolePermission
from settings.cache_manager import PermissionCacheManager
from user.models import User

def test_complete_realtime_flow():
    """Complete test of real-time permission system"""
    
    print("🚀 Complete Real-Time Permission System Test")
    print("=" * 60)
    
    # Step 1: Find or create a coach user
    try:
        coach_user = User.objects.filter(role='coach').first()
        if not coach_user:
            print("❌ No coach user found. Please create a coach user first.")
            return False
        
        print(f"✅ Found coach user: {coach_user.username} (ID: {coach_user.id})")
    except Exception as e:
        print(f"❌ Error finding coach user: {e}")
        return False
    
    # Step 2: Test multiple permission changes
    modules_to_test = ['product', 'member', 'payment', 'finance']
    
    for module in modules_to_test:
        print(f"\n🔄 Testing {module} module permissions...")
        
        try:
            # Get or create permission
            permission, created = RolePermission.objects.get_or_create(
                role='coach',
                module=module,
                defaults={'permission_level': 'none'}
            )
            
            # Get current level
            current_level = permission.permission_level
            print(f"📋 Current permission for coach/{module}: {current_level}")
            
            # Cycle through permission levels
            levels = ['none', 'view', 'edit', 'full']
            current_index = levels.index(current_level) if current_level in levels else 0
            new_index = (current_index + 1) % len(levels)
            new_level = levels[new_index]
            
            print(f"🔄 Changing permission to: {new_level}")
            
            # Update permission (this should trigger WebSocket notification)
            permission.permission_level = new_level
            permission.save()
            
            print(f"✅ Permission updated successfully!")
            print(f"📡 WebSocket notification sent to all coach users")
            
            # Verify cache invalidation
            cached_permissions = PermissionCacheManager.get_user_permissions_cached(coach_user)
            cached_level = cached_permissions.get(module, 'none')
            print(f"💾 Cached permission: {cached_level}")
            
            if cached_level == new_level:
                print(f"✅ Cache updated correctly")
            else:
                print(f"⚠️  Cache mismatch: expected {new_level}, got {cached_level}")
            
            # Small delay between tests
            time.sleep(1)
            
        except Exception as e:
            print(f"❌ Error testing {module}: {e}")
            return False
    
    # Step 3: Test direct notification
    print(f"\n🔔 Testing direct WebSocket notification...")
    
    try:
        changes = {
            'test_module': {
                'old': 'none',
                'new': 'full'
            }
        }
        
        PermissionCacheManager.notify_permission_change(
            role='coach',
            changes=changes,
            affected_users=[coach_user]
        )
        
        print(f"✅ Direct WebSocket notification sent successfully")
        
    except Exception as e:
        print(f"❌ Error sending direct notification: {e}")
        return False
    
    # Step 4: Summary
    print(f"\n📊 Test Summary")
    print("=" * 30)
    print(f"✅ Coach user found: {coach_user.username}")
    print(f"✅ Tested {len(modules_to_test)} permission modules")
    print(f"✅ WebSocket notifications sent")
    print(f"✅ Cache invalidation working")
    print(f"✅ Direct notifications working")
    
    print(f"\n💡 Next Steps:")
    print(f"1. Open browser to http://127.0.0.1:8000/employeeDashboard/")
    print(f"2. Login as coach user: {coach_user.username}")
    print(f"3. Open browser console (F12)")
    print(f"4. Run this script again to see real-time updates")
    print(f"5. Watch for automatic page reload when permissions change")
    
    return True

def show_current_permissions():
    """Show current permissions for all coach users"""
    
    print(f"\n📋 Current Coach Permissions")
    print("=" * 40)
    
    coach_users = User.objects.filter(role='coach')
    
    if not coach_users.exists():
        print("❌ No coach users found")
        return
    
    for coach in coach_users:
        print(f"\n👤 Coach: {coach.username} (ID: {coach.id})")
        permissions = PermissionCacheManager.get_user_permissions_cached(coach)
        
        for module, level in permissions.items():
            if level != 'none':
                print(f"   📦 {module}: {level}")
        
        if not any(level != 'none' for level in permissions.values()):
            print(f"   🚫 No permissions granted")

def main():
    """Main test function"""
    
    print("🎯 Legend Fitness Club - Complete Real-Time Test")
    print("=" * 60)
    
    # Show current state
    show_current_permissions()
    
    # Run the complete test
    success = test_complete_realtime_flow()
    
    if success:
        print(f"\n🎉 All tests completed successfully!")
        print(f"🌐 Check browser console for WebSocket messages")
        print(f"🔄 Pages should auto-reload when permissions change")
    else:
        print(f"\n❌ Some tests failed. Check the error messages above.")

if __name__ == "__main__":
    main()
