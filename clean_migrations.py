import os
import shutil

# Path to your Django project
project_path = '.'

# Walk through all directories
for root, dirs, files in os.walk(project_path):
    # Check if this is a migrations directory
    if os.path.basename(root) == 'migrations' and not root.startswith('./venv'):
        print(f"Processing migrations directory: {root}")
        # Remove all Python files except __init__.py
        for file in files:
            if file.endswith('.py') and file != '__init__.py':
                file_path = os.path.join(root, file)
                try:
                    os.remove(file_path)
                    print(f"Removed: {file_path}")
                except Exception as e:
                    print(f"Error removing {file_path}: {e}")

        # Remove __pycache__ directory if it exists
        pycache_dir = os.path.join(root, '__pycache__')
        if os.path.exists(pycache_dir):
            try:
                shutil.rmtree(pycache_dir)
                print(f"Removed: {pycache_dir}")
            except Exception as e:
                print(f"Error removing {pycache_dir}: {e}")

print("Migration cleanup completed!")
