{% extends 'base.html' %}
{% load static %}



{% block body %}
<div class="conponentSection p-5 bg-gray-200">
    <div class="componentWrapper">
        <!-- Header with Back Button -->
        <div class="flex items-center mb-4">
            <a href="{% url 'settings:dashboard' %}" class="bg-blue-900 text-white p-2 rounded-full hover:bg-blue-800 mr-3">
                <i class="fas fa-arrow-left"></i>
            </a>
            <h2 class="text-2xl font-bold">System Settings</h2>
        </div>

        <!-- Warning Banner -->
        <div class="bg-red-100 border-l-4 border-red-500 text-red-700 p-4 mb-6">
            <div class="flex items-center">
                <i class="fas fa-exclamation-triangle text-2xl mr-3"></i>
                <div>
                    <p class="font-bold">Warning: Admin Only Area</p>
                    <p>This section contains system-level operations that can permanently delete data. Proceed with caution.</p>
                </div>
            </div>
        </div>

        <!-- Data Cleaning Section -->
        <div class="bg-white rounded-lg shadow-md overflow-hidden mb-6">
            <div class="bg-red-600 text-white p-4">
                <div class="flex items-center">
                    <i class="fas fa-trash-alt text-2xl mr-3"></i>
                    <h3 class="text-xl font-bold">Data Cleaning</h3>
                </div>
            </div>

            <div class="p-6">
                <p class="mb-4 text-gray-700">Use these options to clean data from the system. All operations are permanent and cannot be undone.</p>

                <!-- Member Data Section -->
                <div class="bg-white border border-gray-200 p-4 rounded shadow-sm mb-4">
                    <h4 class="text-lg font-semibold mb-2">Member Data</h4>
                    <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                        <!-- Clean Members -->
                        <div class="bg-white border border-gray-200 p-4 rounded shadow-sm">
                            <h5 class="text-md font-semibold mb-2">Delete All Members</h5>
                            <p class="mb-4 text-sm">This will delete all members from the database.</p>
                            <form method="post" onsubmit="return confirm('Are you sure you want to delete ALL members? This action cannot be undone.')">
                                {% csrf_token %}
                                <input type="hidden" name="action" value="clean_members">
                                <button type="submit" class="bg-red-600 hover:bg-red-700 text-white font-bold py-2 px-4 rounded">Delete All Members ({{ data_counts.members }})</button>
                            </form>
                        </div>

                        <!-- Clean Packages -->
                        <div class="bg-white border border-gray-200 p-4 rounded shadow-sm">
                            <h5 class="text-md font-semibold mb-2">Delete All Packages</h5>
                            <p class="mb-4 text-sm">This will delete all membership packages from the database.</p>
                            <form method="post" onsubmit="return confirm('Are you sure you want to delete ALL packages? This action cannot be undone.')">
                                {% csrf_token %}
                                <input type="hidden" name="action" value="clean_packages">
                                <button type="submit" class="bg-red-600 hover:bg-red-700 text-white font-bold py-2 px-4 rounded">Delete All Packages ({{ data_counts.packages }})</button>
                            </form>
                        </div>
                    </div>
                </div>

                <!-- Financial Data Section -->
                <div class="bg-white border border-gray-200 p-4 rounded shadow-sm mb-4">
                    <h4 class="text-lg font-semibold mb-2">Financial Data</h4>
                    <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                        <!-- Clean Payments -->
                        <div class="bg-white border border-gray-200 p-4 rounded shadow-sm">
                            <h5 class="text-md font-semibold mb-2">Delete All Payments</h5>
                            <p class="mb-4 text-sm">This will delete all payment records from the database.</p>
                            <form method="post" onsubmit="return confirm('Are you sure you want to delete ALL payments? This action cannot be undone.')">
                                {% csrf_token %}
                                <input type="hidden" name="action" value="clean_payments">
                                <button type="submit" class="bg-red-600 hover:bg-red-700 text-white font-bold py-2 px-4 rounded">Delete All Payments ({{ data_counts.payments }})</button>
                            </form>
                        </div>

                        <!-- Clean Salary Payments -->
                        <div class="bg-white border border-gray-200 p-4 rounded shadow-sm">
                            <h5 class="text-md font-semibold mb-2">Delete All Salary Payments</h5>
                            <p class="mb-4 text-sm">This will delete all salary payment records from the database.</p>
                            <form method="post" onsubmit="return confirm('Are you sure you want to delete ALL salary payments? This action cannot be undone.')">
                                {% csrf_token %}
                                <input type="hidden" name="action" value="clean_salary_payments">
                                <button type="submit" class="bg-red-600 hover:bg-red-700 text-white font-bold py-2 px-4 rounded">Delete All Salary Payments ({{ data_counts.salary_payments }})</button>
                            </form>
                        </div>

                        <!-- Clean Bills -->
                        <div class="bg-white border border-gray-200 p-4 rounded shadow-sm">
                            <h5 class="text-md font-semibold mb-2">Delete All Bills</h5>
                            <p class="mb-4 text-sm">This will delete all bill records from the database.</p>
                            <form method="post" onsubmit="return confirm('Are you sure you want to delete ALL bills? This action cannot be undone.')">
                                {% csrf_token %}
                                <input type="hidden" name="action" value="clean_bills">
                                <button type="submit" class="bg-red-600 hover:bg-red-700 text-white font-bold py-2 px-4 rounded">Delete All Bills ({{ data_counts.bills }})</button>
                            </form>
                        </div>

                        <!-- Clean Finance Data -->
                        <div class="bg-white border border-gray-200 p-4 rounded shadow-sm">
                            <h5 class="text-md font-semibold mb-2">Delete All Finance Transactions</h5>
                            <p class="mb-4 text-sm">This will delete all finance transactions from the database and reset the balance to zero.</p>
                            <form method="post" onsubmit="return confirm('Are you sure you want to delete ALL finance transactions? This will reset your balance to zero. This action cannot be undone.')">
                                {% csrf_token %}
                                <input type="hidden" name="action" value="clean_finance">
                                <button type="submit" class="bg-red-600 hover:bg-red-700 text-white font-bold py-2 px-4 rounded">Delete All Finance Transactions ({{ data_counts.finance_transactions }})</button>
                            </form>
                        </div>
                    </div>
                </div>

                <!-- Product Data Section -->
                <div class="bg-white border border-gray-200 p-4 rounded shadow-sm mb-4">
                    <h4 class="text-lg font-semibold mb-2">Product Data</h4>
                    <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                        <!-- Clean Products -->
                        <div class="bg-white border border-gray-200 p-4 rounded shadow-sm">
                            <h5 class="text-md font-semibold mb-2">Delete All Products</h5>
                            <p class="mb-4 text-sm">This will delete all products from the database.</p>
                            <form method="post" onsubmit="return confirm('Are you sure you want to delete ALL products? This action cannot be undone.')">
                                {% csrf_token %}
                                <input type="hidden" name="action" value="clean_products">
                                <button type="submit" class="bg-red-600 hover:bg-red-700 text-white font-bold py-2 px-4 rounded">Delete All Products ({{ data_counts.products }})</button>
                            </form>
                        </div>

                        <!-- Clean Categories -->
                        <div class="bg-white border border-gray-200 p-4 rounded shadow-sm">
                            <h5 class="text-md font-semibold mb-2">Delete All Categories</h5>
                            <p class="mb-4 text-sm">This will delete all product categories from the database.</p>
                            <form method="post" onsubmit="return confirm('Are you sure you want to delete ALL categories? This action cannot be undone.')">
                                {% csrf_token %}
                                <input type="hidden" name="action" value="clean_categories">
                                <button type="submit" class="bg-red-600 hover:bg-red-700 text-white font-bold py-2 px-4 rounded">Delete All Categories ({{ data_counts.categories }})</button>
                            </form>
                        </div>

                        <!-- Clean Purchases -->
                        <div class="bg-white border border-gray-200 p-4 rounded shadow-sm">
                            <h5 class="text-md font-semibold mb-2">Delete All Purchases</h5>
                            <p class="mb-4 text-sm">This will delete all purchase records from the database.</p>
                            <form method="post" onsubmit="return confirm('Are you sure you want to delete ALL purchases? This action cannot be undone.')">
                                {% csrf_token %}
                                <input type="hidden" name="action" value="clean_purchases">
                                <button type="submit" class="bg-red-600 hover:bg-red-700 text-white font-bold py-2 px-4 rounded">Delete All Purchases ({{ data_counts.purchases }})</button>
                            </form>
                        </div>

                        <!-- Clean Sales -->
                        <div class="bg-white border border-gray-200 p-4 rounded shadow-sm">
                            <h5 class="text-md font-semibold mb-2">Delete All Sales</h5>
                            <p class="mb-4 text-sm">This will delete all sale records from the database.</p>
                            <form method="post" onsubmit="return confirm('Are you sure you want to delete ALL sales? This action cannot be undone.')">
                                {% csrf_token %}
                                <input type="hidden" name="action" value="clean_sales">
                                <button type="submit" class="bg-red-600 hover:bg-red-700 text-white font-bold py-2 px-4 rounded">Delete All Sales ({{ data_counts.sales }})</button>
                            </form>
                        </div>
                    </div>
                </div>

                <!-- Other Data Section -->
                <div class="bg-white border border-gray-200 p-4 rounded shadow-sm mb-4">
                    <h4 class="text-lg font-semibold mb-2">Other Data</h4>
                    <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                        <!-- Clean Pay-per-visit -->
                        <div class="bg-white border border-gray-200 p-4 rounded shadow-sm">
                            <h5 class="text-md font-semibold mb-2">Delete All Pay-per-visit Records</h5>
                            <p class="mb-4 text-sm">This will delete all pay-per-visit records from the database.</p>
                            <form method="post" onsubmit="return confirm('Are you sure you want to delete ALL pay-per-visit records? This action cannot be undone.')">
                                {% csrf_token %}
                                <input type="hidden" name="action" value="clean_paypervisit">
                                <button type="submit" class="bg-red-600 hover:bg-red-700 text-white font-bold py-2 px-4 rounded">Delete All Pay-per-visit Records ({{ data_counts.paypervisit }})</button>
                            </form>
                        </div>

                        <!-- Clean Users -->
                        <div class="bg-white border border-gray-200 p-4 rounded shadow-sm">
                            <h5 class="text-md font-semibold mb-2">Delete All Non-Admin Users</h5>
                            <p class="mb-4 text-sm">This will delete all non-admin users from the database.</p>
                            <form method="post" onsubmit="return confirm('Are you sure you want to delete ALL non-admin users? This action cannot be undone.')">
                                {% csrf_token %}
                                <input type="hidden" name="action" value="clean_users">
                                <button type="submit" class="bg-red-600 hover:bg-red-700 text-white font-bold py-2 px-4 rounded">Delete All Non-Admin Users ({{ data_counts.users }})</button>
                            </form>
                        </div>
                    </div>
                </div>

                <!-- Clean All Data Section -->
                <div class="bg-white border border-gray-200 p-4 rounded shadow-md mb-4">
                    <h4 class="text-lg font-semibold mb-2 flex items-center">
                        <i class="fas fa-exclamation-triangle text-red-600 mr-2"></i>
                        Clean All Data
                    </h4>
                    <p class="mb-4 text-sm">This will delete <strong>ALL</strong> data from the system, including members, packages, payments, products, sales, and more. Use with extreme caution.</p>

                    <div class="bg-red-50 border-l-4 border-red-600 p-4 mb-4">
                        <div class="flex">
                            <div class="flex-shrink-0">
                                <i class="fas fa-exclamation-circle text-red-600"></i>
                            </div>
                            <div class="ml-3">
                                <p class="text-sm text-red-700">
                                    <strong>WARNING:</strong> This action cannot be undone. All data will be permanently deleted.
                                </p>
                            </div>
                        </div>
                    </div>

                    <form method="post" action="{% url 'settings:clean_all_data' %}" onsubmit="return confirm('Are you absolutely sure you want to delete ALL data from the system? This includes all members, packages, payments, products, sales, and more. This action CANNOT be undone!')">
                        {% csrf_token %}
                        <button type="submit" class="bg-red-600 hover:bg-red-700 text-white font-bold py-3 px-6 rounded w-full flex items-center justify-center">
                            <i class="fas fa-trash-alt mr-2"></i> Delete All Data
                        </button>
                    </form>

                    <div class="mt-4 bg-yellow-50 p-3 rounded">
                        <p class="text-sm text-yellow-800">
                            <i class="fas fa-lightbulb mr-1"></i>
                            <strong>TIP:</strong> Create a backup before using this feature to ensure you can restore your data if needed.
                        </p>
                    </div>
                </div>

                <!-- Backup and Restore Section -->
                <div class="bg-white border border-gray-200 p-4 rounded shadow-md mb-4">
                    <h4 class="text-lg font-semibold mb-2">Database Backup and Restore</h4>
                    <p class="mb-4">Create backups of your database or restore from a previous backup.</p>

                    <div class="flex flex-col md:flex-row gap-4">
                        <!-- Create Backup -->
                        <div class="flex-1 bg-white border border-gray-200 p-4 rounded shadow-sm">
                            <h5 class="text-md font-semibold mb-2">Create Backup</h5>
                            <p class="mb-4 text-sm">Create a backup of your current database. This will save all your data.</p>
                            <form method="post" action="{% url 'settings:backup_database' %}">
                                {% csrf_token %}
                                <button type="submit" class="bg-blue-600 hover:bg-blue-700 text-white font-bold py-2 px-4 rounded"><i class="fas fa-download mr-2"></i> Create Backup</button>
                            </form>
                        </div>

                        <!-- Manage Backups -->
                        <div class="flex-1 bg-white border border-gray-200 p-4 rounded shadow-sm">
                            <h5 class="text-md font-semibold mb-2">Manage Backups</h5>
                            <p class="mb-4 text-sm">View, download, or restore from previous backups.</p>
                            <a href="{% url 'settings:backup_list' %}" class="bg-green-600 hover:bg-green-700 text-white font-bold py-2 px-4 rounded inline-block">
                                <i class="fas fa-database mr-2"></i> Manage Backups
                            </a>
                        </div>
                    </div>

                    <div class="mt-4 bg-blue-50 p-3 rounded">
                        <p class="text-sm text-blue-800"><i class="fas fa-info-circle mr-1"></i>
                            <strong>TIP:</strong> Create a backup before making major changes to your system or cleaning data."</p>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}
