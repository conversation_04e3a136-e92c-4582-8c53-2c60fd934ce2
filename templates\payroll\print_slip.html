{% extends 'base.html' %}
{% load custom_filters %}



{% block head %}
<style>
    /* Additional print styles specific to this template */
    @media print {
        /* Make the print section visible */
        .print-section, .print-section * {
            visibility: visible !important;
            display: block !important;
        }

        /* Hide everything else */
        body > *:not(.main-content),
        .main-content > *:not(.conponentSection),
        .conponentSection > *:not(.componentWrapper),
        .componentWrapper > *:not(.bg-white),
        .bg-white > *:not(.print-section) {
            display: none !important;
        }

        /* Ensure proper positioning */
        body {
            margin: 0 !important;
            padding: 0 !important;
        }

        /* Remove any background colors or borders */
        .receipt-border {
            border: 1px solid #ccc !important;
            background-color: white !important;
            margin: 0 auto !important;
            width: 100% !important;
            max-width: 800px !important;
        }
    }

    .receipt-border {
        border: 1px solid #ccc;
        padding: 20px;
        border-radius: 5px;
    }

    .khmer-font {
        font-family: 'Khmer OS', 'Khmer OS System', sans-serif;
    }

    {% if template %}
    /* Custom template styles */
    .salary-slip {
        background-color: {{ template.background_color }};
        color: {{ template.text_color }};
    }

    .salary-slip h1, .salary-slip h2, .salary-slip h3 {
        color: {{ template.accent_color }};
    }

    .salary-slip .border-t, .salary-slip .border-b, .salary-slip .border-t-2 {
        border-color: {{ template.accent_color }};
    }

    /* Custom CSS from template */
    {{ template.custom_css|safe }}
    {% endif %}
</style>
{% endblock %}

{% block body %}
<div class="conponentSection p-5 bg-gray-200">
    <div class="componentWrapper">
        <div class="bg-white p-8 rounded shadow-md max-w-2xl mx-auto">
            <!-- Template Selector -->
            <div class="no-print mb-4">
                <form method="get" class="flex items-center space-x-2">
                    <label class="text-sm font-medium">Template:</label>
                    <select name="template" class="border p-2 rounded" onchange="this.form.submit()">
                        {% for t in all_templates %}
                        <option value="{{ t.id }}" {% if template.id == t.id %}selected{% endif %}>
                            {{ t.name }} {% if t.is_default %}(Default){% endif %}
                        </option>
                        {% endfor %}
                    </select>
                    <a href="{% url 'payroll:template_list' %}" class="text-blue-600 hover:underline">Manage Templates</a>
                </form>
            </div>

            <div class="print-section receipt-border salary-slip">
                <!-- Receipt Header -->
                <div class="text-center mb-6">
                    {% if template.company_logo %}
                    <img src="{{ template.company_logo.url }}" alt="Company Logo" class="h-16 mx-auto mb-2">
                    {% endif %}
                    <h1 class="text-3xl font-bold">{{ template.header_text }}</h1>
                    <p class="text-lg">{{ template.subheader_text }}</p>
                </div>

                <!-- Receipt Content -->
                <div class="border-t border-b py-4 mb-4">
                    <div class="grid grid-cols-2 gap-4">
                        <div>
                            <p><strong>Payroll ID:</strong> {{ payment.payroll_id }}</p>
                            <p><strong>Employee ID:</strong> {{ payment.employee.emp_id }}</p>
                            <p><strong>Employee Name:</strong> {{ payment.employee.name }}</p>
                            <p><strong>Role:</strong> {{ payment.employee.role|title }}</p>
                        </div>
                        <div>
                            <p><strong>Month:</strong> {{ payment.month|date:"F Y" }}</p>
                            <p><strong>Payment Date:</strong> {{ payment.payment_date|date:"d-M-Y" }}</p>
                            <p><strong>Payment Method:</strong> {{ payment.get_payment_method_display }}</p>
                            <p><strong>Employment Type:</strong> {{ payment.get_employment_type_display }}</p>
                        </div>
                    </div>
                </div>

                <!-- Salary Breakdown -->
                <div class="mb-6">
                    <h2 class="text-xl font-bold mb-3">Salary Breakdown</h2>
                    <table class="w-full">
                        <tr class="border-b">
                            <td class="py-2">Base Salary</td>
                            <td class="py-2 text-right">{{ payment.base_salary|format_khr }}</td>
                        </tr>
                        {% if payment.bonus > 0 %}
                        <tr class="border-b">
                            <td class="py-2">Bonus</td>
                            <td class="py-2 text-right text-green-600">+ {{ payment.bonus|format_khr }}</td>
                        </tr>
                        {% endif %}
                        {% if payment.overtime_hours > 0 %}
                        <tr class="border-b">
                            <td class="py-2">Overtime ({{ payment.overtime_hours }} hours)</td>
                            <td class="py-2 text-right text-green-600">+ 0៛</td>
                        </tr>
                        {% endif %}
                        {% if payment.deduction > 0 %}
                        <tr class="border-b">
                            <td class="py-2">Deductions</td>
                            <td class="py-2 text-right text-red-600">- {{ payment.deduction|format_khr }}</td>
                        </tr>
                        {% endif %}
                        <tr class="border-t-2 font-bold">
                            <td class="py-3">Final Pay</td>
                            <td class="py-3 text-right text-lg">{{ payment.final_pay|format_khr }}</td>
                        </tr>
                    </table>
                </div>

                {% if payment.notes %}
                <div class="mb-6">
                    <h2 class="text-xl font-bold mb-2">Notes</h2>
                    <p class="bg-gray-50 p-3 rounded">{{ payment.notes }}</p>
                </div>
                {% endif %}

                <!-- Receipt Footer -->
                <div class="text-center text-sm mt-8 {% if template.language == 'km' or template.language == 'both' %}khmer-font{% endif %}">
                    {% if template.show_signatures %}
                    <div class="grid grid-cols-2 gap-8 mb-8">
                        <div class="text-center">
                            <p class="mb-12">_________________________</p>
                            <p>Employee Signature</p>
                            {% if template.language == 'km' or template.language == 'both' %}
                            <p>ហត្ថលេខានិយោជិត</p>
                            {% endif %}
                        </div>
                        <div class="text-center">
                            <p class="mb-12">_________________________</p>
                            <p>Manager Signature</p>
                            {% if template.language == 'km' or template.language == 'both' %}
                            <p>ហត្ថលេខាអ្នកគ្រប់គ្រង</p>
                            {% endif %}
                        </div>
                    </div>
                    {% endif %}
                    <p class="mt-8">{{ template.footer_text }}</p>
                    {% if template.language == 'km' or template.language == 'both' %}
                    <p class="mt-2">សូមអរគុណសម្រាប់ការបម្រើការងាររបស់អ្នក!</p>
                    {% endif %}
                </div>
            </div>

            <!-- Print Button -->
            <div class="mt-6 text-center no-print">
                <button onclick="window.print()" class="bg-blue-900 text-white font-bold py-2 px-6 rounded">Print Salary Slip</button>
                <a href="{% url 'payroll:view' payment.id %}" class="bg-gray-500 text-white font-bold py-2 px-6 rounded ml-2">Back</a>
            </div>
        </div>
    </div>
</div>
{% endblock %}
