# Redis 7.4.3 Upgrade Script for Legend Fitness Club
# Run this script as Administrator in PowerShell

Write-Host "Redis 7.4.3 Upgrade for Legend Fitness Club" -ForegroundColor Green
Write-Host "===========================================" -ForegroundColor Green

# Check if running as administrator
if (-NOT ([Security.Principal.WindowsPrincipal] [Security.Principal.WindowsIdentity]::GetCurrent()).IsInRole([Security.Principal.WindowsBuiltInRole] "Administrator")) {
    Write-Host "This script must be run as Administrator!" -ForegroundColor Red
    Write-Host "Right-click PowerShell and select 'Run as Administrator'" -ForegroundColor Yellow
    Read-Host "Press Enter to exit"
    exit 1
}

Write-Host "Running as Administrator - proceeding..." -ForegroundColor Green

# Step 1: Stop current Redis 3.0.504 service
Write-Host "`nStep 1: Stopping Redis 3.0.504 service..." -ForegroundColor Yellow
try {
    Stop-Service -Name "redis" -Force -ErrorAction Stop
    Write-Host "✓ Redis 3.0.504 service stopped successfully" -ForegroundColor Green
    Start-Sleep -Seconds 3
} catch {
    Write-Host "⚠ Redis service may not be running or already stopped: $($_.Exception.Message)" -ForegroundColor Yellow
}

# Step 2: Verify Redis 7.4.3 installation
Write-Host "`nStep 2: Verifying Redis 7.4.3 installation..." -ForegroundColor Yellow
$redis743Path = "C:\Redis-7.4.3"

if (!(Test-Path $redis743Path)) {
    Write-Host "✗ Redis 7.4.3 directory not found at $redis743Path" -ForegroundColor Red
    Read-Host "Press Enter to exit"
    exit 1
}

# Check for essential files
$requiredFiles = @("redis-server.exe", "redis-cli.exe", "RedisService.exe")
$missingFiles = @()

foreach ($file in $requiredFiles) {
    if (!(Test-Path "$redis743Path\$file")) {
        $missingFiles += $file
    }
}

if ($missingFiles.Count -gt 0) {
    Write-Host "✗ Missing required files: $($missingFiles -join ', ')" -ForegroundColor Red
    Read-Host "Press Enter to exit"
    exit 1
}

Write-Host "✓ Redis 7.4.3 found at $redis743Path with all required files" -ForegroundColor Green

# Step 3: Create Redis 7.4.3 configuration
Write-Host "`nStep 3: Creating Redis 7.4.3 configuration..." -ForegroundColor Yellow

$redisConfig = @"
# Redis 7.4.3 Configuration for Legend Fitness Club
# Basic server configuration
port 6379
bind 127.0.0.1
timeout 0
tcp-keepalive 60
tcp-backlog 511

# Logging
loglevel notice
logfile "redis-server.log"

# Database configuration
databases 16

# Persistence configuration
save 900 1
save 300 10
save 60 10000
stop-writes-on-bgsave-error yes
rdbcompression yes
rdbchecksum yes
dbfilename dump.rdb
dir ./

# Memory management
maxmemory 1gb
maxmemory-policy allkeys-lru

# Network and performance
tcp-keepalive 300

# Enable keyspace notifications for Django Channels
notify-keyspace-events Ex

# Redis 7.4.3 optimizations
lazyfree-lazy-eviction yes
lazyfree-lazy-expire yes
lazyfree-lazy-server-del yes
replica-lazy-flush yes

# Security (optional)
# requirepass your_password_here
"@

$configPath = "$redis743Path\redis.windows-service.conf"
$redisConfig | Out-File -FilePath $configPath -Encoding UTF8
Write-Host "✓ Redis 7.4.3 configuration file created: $configPath" -ForegroundColor Green

# Step 4: Install Redis 7.4.3 as Windows service
Write-Host "`nStep 4: Installing Redis 7.4.3 as Windows service..." -ForegroundColor Yellow

Set-Location $redis743Path

try {
    # Remove old Redis services if they exist
    & .\RedisService.exe -uninstall 2>$null
    
    # Use the provided service installer if available
    if (Test-Path ".\install_redis_service.bat") {
        Write-Host "Using provided service installer..." -ForegroundColor Cyan
        & cmd /c "install_redis_service.bat"
    } else {
        # Manual service installation
        & .\RedisService.exe -install -servicename Redis743 -port 6379 -configfile redis.windows-service.conf
    }
    
    Write-Host "✓ Redis 7.4.3 service installation completed" -ForegroundColor Green
    
    # Start the service
    try {
        Start-Service -Name "Redis743" -ErrorAction SilentlyContinue
        Write-Host "✓ Redis 7.4.3 service started successfully!" -ForegroundColor Green
    } catch {
        Write-Host "⚠ Service start failed, trying manual start..." -ForegroundColor Yellow
        Start-Process -FilePath ".\redis-server.exe" -ArgumentList "redis.windows-service.conf" -WindowStyle Minimized
    }
    
} catch {
    Write-Host "⚠ Service installation failed: $($_.Exception.Message)" -ForegroundColor Yellow
    Write-Host "Trying manual start..." -ForegroundColor Yellow
    Start-Process -FilePath ".\redis-server.exe" -ArgumentList "redis.windows-service.conf" -WindowStyle Minimized
}

# Step 5: Test Redis 7.4.3
Write-Host "`nStep 5: Testing Redis 7.4.3 installation..." -ForegroundColor Yellow

Start-Sleep -Seconds 5

try {
    $pingResult = & .\redis-cli.exe ping 2>$null
    if ($pingResult -eq "PONG") {
        Write-Host "✓ Redis 7.4.3 is responding to ping!" -ForegroundColor Green
        
        # Get Redis version
        $versionInfo = & .\redis-cli.exe info server 2>$null | Select-String "redis_version"
        Write-Host "✓ $versionInfo" -ForegroundColor Green
        
        # Test basic operations
        & .\redis-cli.exe set test "Redis 7.4.3 Working" >$null 2>&1
        $testResult = & .\redis-cli.exe get test 2>$null
        & .\redis-cli.exe del test >$null 2>&1
        
        if ($testResult -eq "Redis 7.4.3 Working") {
            Write-Host "✓ Redis 7.4.3 basic operations working!" -ForegroundColor Green
        }
        
        # Test Redis 7.4.3 specific features
        $memoryInfo = & .\redis-cli.exe info memory 2>$null | Select-String "used_memory_human"
        Write-Host "✓ Memory usage: $memoryInfo" -ForegroundColor Green
        
        # Test keyspace notifications
        $notifications = & .\redis-cli.exe config get notify-keyspace-events 2>$null
        Write-Host "✓ Keyspace notifications: $($notifications[1])" -ForegroundColor Green
        
    } else {
        Write-Host "✗ Redis 7.4.3 is not responding" -ForegroundColor Red
    }
} catch {
    Write-Host "✗ Redis 7.4.3 test failed: $($_.Exception.Message)" -ForegroundColor Red
}

# Step 6: Update system PATH
Write-Host "`nStep 6: Updating system PATH..." -ForegroundColor Yellow

$currentPath = [Environment]::GetEnvironmentVariable("PATH", "Machine")
if ($currentPath -notlike "*$redis743Path*") {
    $newPath = "$currentPath;$redis743Path"
    [Environment]::SetEnvironmentVariable("PATH", $newPath, "Machine")
    Write-Host "✓ Redis 7.4.3 added to system PATH" -ForegroundColor Green
} else {
    Write-Host "✓ Redis 7.4.3 already in system PATH" -ForegroundColor Green
}

# Final summary
Write-Host "`n" + "="*60 -ForegroundColor Green
Write-Host "Redis 7.4.3 Upgrade Completed Successfully!" -ForegroundColor Green
Write-Host "="*60 -ForegroundColor Green
Write-Host "Redis 7.4.3 Location: $redis743Path" -ForegroundColor Yellow
Write-Host "Configuration File: $configPath" -ForegroundColor Yellow
Write-Host "Service Name: Redis743" -ForegroundColor Yellow
Write-Host "Port: 6379" -ForegroundColor Yellow
Write-Host ""
Write-Host "Next Steps:" -ForegroundColor Cyan
Write-Host "1. Restart your Django server" -ForegroundColor White
Write-Host "2. Run: python manage.py test_redis --detailed" -ForegroundColor White
Write-Host "3. Check for Redis 7.4.3 detection in Django logs" -ForegroundColor White
Write-Host "4. Test real-time WebSocket functionality" -ForegroundColor White
Write-Host ""
Write-Host "Expected Django output:" -ForegroundColor Cyan
Write-Host "✓ Redis 7.4.3 detected - using Redis channel layer" -ForegroundColor Green
Write-Host "✓ Redis detected - using Redis cache backend" -ForegroundColor Green
Write-Host "✓ Channel Backend: channels_redis.core.RedisChannelLayer" -ForegroundColor Green

Read-Host "`nPress Enter to exit"
