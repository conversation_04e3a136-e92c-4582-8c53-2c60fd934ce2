{% extends "../base.html" %}
{% load custom_filters %}



{% block body %}
<div class="conponentSection p-5 bg-gray-200">
    <div class="componentWrapper">
        <!-- Sales Overview -->
        <div class="bg-white p-4 rounded shadow-md mb-4">
            <div class="flex justify-between items-center mb-4">
                <h3 class="text-2xl font-bold">{{ title }}</h3>
                <div class="flex space-x-2">
                    <a href="{% url 'report:export_csv' 'sales' %}?start_date={{ start_date }}&end_date={{ end_date }}" class="bg-green-600 text-white px-4 py-2 rounded hover:bg-green-700">
                        <i class="fa-solid fa-file-csv mr-2"></i> Export CSV
                    </a>
                    <a href="{% url 'report:export_pdf' 'sales' %}?start_date={{ start_date }}&end_date={{ end_date }}" class="bg-red-600 text-white px-4 py-2 rounded hover:bg-red-700">
                        <i class="fa-solid fa-file-pdf mr-2"></i> Export PDF
                    </a>
                </div>
            </div>

            <!-- Date Filter -->
            <div class="mb-4">
                <form method="get" class="flex flex-wrap items-center space-x-2">
                    <div class="flex items-center">
                        <label for="start_date" class="mr-2">From:</label>
                        <input type="date" id="start_date" name="start_date" value="{{ start_date }}" class="border rounded p-2">
                    </div>
                    <div class="flex items-center">
                        <label for="end_date" class="mr-2">To:</label>
                        <input type="date" id="end_date" name="end_date" value="{{ end_date }}" class="border rounded p-2">
                    </div>
                    <button type="submit" class="bg-blue-600 text-white px-4 py-2 rounded hover:bg-blue-700"><i class="fa-solid fa-filter mr-2"></i> Filter</button>
                    {% if request.GET.start_date or request.GET.end_date %}
                    <a href="{% url 'report:sales_report' %}" class="bg-gray-600 text-white px-4 py-2 rounded hover:bg-gray-700">
                        <i class="fa-solid fa-times mr-2"></i> Clear
                    </a>
                    {% endif %}
                </form>
            </div>

            <!-- Summary Cards -->
            <div class="grid grid-cols-1 md:grid-cols-3 gap-4 mb-4">
                <div class="bg-blue-100 p-4 rounded-lg">
                    <p class="text-sm text-blue-600">Total Sales</p>
                    <p class="text-2xl font-bold">{{ total_amount|format_khr }}</p>
                </div>
                <div class="bg-green-100 p-4 rounded-lg">
                    <p class="text-sm text-green-600">Total Transactions</p>
                    <p class="text-2xl font-bold">{{ total_transactions }}</p>
                </div>
                <div class="bg-purple-100 p-4 rounded-lg">
                    <p class="text-sm text-purple-600">Average Transaction</p>
                    <p class="text-2xl font-bold">{{ avg_transaction|format_khr }}</p>
                </div>
            </div>

            <!-- Sales Chart -->
            <div class="mb-6">
                <h4 class="text-lg font-semibold mb-2">Sales Trend</h4>
                <canvas id="salesChart" height="100"></canvas>
            </div>

            <!-- Sales by Product and Category -->
            <div class="grid grid-cols-1 md:grid-cols-2 gap-4 mb-6">
                <!-- Sales by Product -->
                <div>
                    <h4 class="text-lg font-semibold mb-2">Sales by Product</h4>
                    <div class="relative overflow-x-auto">
                        <table class="w-full text-sm text-left">
                            <thead class="text-sm uppercase bg-gray-200 text-gray-700"><tr>
                                    <th scope="col" class="px-6 py-3">Product</th>
                                    <th scope="col" class="px-6 py-3">Quantity Sold</th>
                                    <th scope="col" class="px-6 py-3">Total Amount</th>
                                </tr>
                            </thead>
                            <tbody>
                                {% for item in sales_by_product %}
                                <tr class="bg-white border">
                                    <td class="px-6 py-4">{{ item.product__name }}</td>
                                    <td class="px-6 py-4">{{ item.total_quantity }}</td>
                                    <td class="px-6 py-4">{{ item.total_amount|format_khr }}</td>
                                </tr>
                                {% empty %}
                                <tr class="bg-white border">
                                    <td colspan="3" class="px-6 py-4 text-center">No sales data available</td>
                                </tr>
                                {% endfor %}
                            </tbody>
                        </table>
                    </div>
                </div>

                <!-- Sales by Category -->
                <div>
                    <h4 class="text-lg font-semibold mb-2">Sales by Category</h4>
                    <div class="relative overflow-x-auto">
                        <table class="w-full text-sm text-left">
                            <thead class="text-sm uppercase bg-gray-200 text-gray-700"><tr>
                                    <th scope="col" class="px-6 py-3">Category</th>
                                    <th scope="col" class="px-6 py-3">Total Amount</th>
                                </tr>
                            </thead>
                            <tbody>
                                {% for item in sales_by_category %}
                                <tr class="bg-white border">
                                    <td class="px-6 py-4">{{ item.product__category__name|default:"Uncategorized" }}</td>
                                    <td class="px-6 py-4">{{ item.total_amount|format_khr }}</td>
                                </tr>
                                {% empty %}
                                <tr class="bg-white border">
                                    <td colspan="2" class="px-6 py-4 text-center">No category data available</td>
                                </tr>
                                {% endfor %}
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>

            <!-- Sales by Payment Method and Employee -->
            <div class="grid grid-cols-1 md:grid-cols-2 gap-4 mb-6">
                <!-- Sales by Payment Method -->
                <div>
                    <h4 class="text-lg font-semibold mb-2">Sales by Payment Method</h4>
                    <div class="relative overflow-x-auto">
                        <table class="w-full text-sm text-left">
                            <thead class="text-sm uppercase bg-gray-200 text-gray-700"><tr>
                                    <th scope="col" class="px-6 py-3">Payment Method</th>
                                    <th scope="col" class="px-6 py-3">Transactions</th>
                                    <th scope="col" class="px-6 py-3">Total Amount</th>
                                </tr>
                            </thead>
                            <tbody>
                                {% for item in sales_by_payment %}
                                <tr class="bg-white border">
                                    <td class="px-6 py-4">
                                        {% if item.payment_method == 'cash' %}Cash
                                        {% elif item.payment_method == 'card' %}Card
                                        {% else %}{{ item.payment_method }}
                                        {% endif %}
                                    </td>
                                    <td class="px-6 py-4">{{ item.count }}</td>
                                    <td class="px-6 py-4">{{ item.total_amount|format_khr }}</td>
                                </tr>
                                {% empty %}
                                <tr class="bg-white border">
                                    <td colspan="3" class="px-6 py-4 text-center">No payment method data available</td>
                                </tr>
                                {% endfor %}
                            </tbody>
                        </table>
                    </div>
                </div>

                <!-- Sales by Employee -->
                <div>
                    <h4 class="text-lg font-semibold mb-2">Sales by Employee</h4>
                    <div class="relative overflow-x-auto">
                        <table class="w-full text-sm text-left">
                            <thead class="text-sm uppercase bg-gray-200 text-gray-700"><tr>
                                    <th scope="col" class="px-6 py-3">Employee</th>
                                    <th scope="col" class="px-6 py-3">Transactions</th>
                                    <th scope="col" class="px-6 py-3">Total Amount</th>
                                </tr>
                            </thead>
                            <tbody>
                                {% for item in sales_by_employee %}
                                <tr class="bg-white border">
                                    <td class="px-6 py-4">{{ item.sold_by__username|default:"Unknown" }}</td>
                                    <td class="px-6 py-4">{{ item.count }}</td>
                                    <td class="px-6 py-4">{{ item.total_amount|format_khr }}</td>
                                </tr>
                                {% empty %}
                                <tr class="bg-white border">
                                    <td colspan="3" class="px-6 py-4 text-center">No employee data available</td>
                                </tr>
                                {% endfor %}
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>

            <!-- Sales Transactions -->
            <div>
                <h4 class="text-lg font-semibold mb-2">Sales Transactions</h4>
                <div class="relative overflow-x-auto">
                    <table class="w-full text-sm text-left">
                        <thead class="text-sm uppercase bg-blue-900 text-gray-50"><tr>
                                <th scope="col" class="px-6 py-3">Transaction ID</th>
                                <th scope="col" class="px-6 py-3">Date</th>
                                <th scope="col" class="px-6 py-3">Total Amount</th>
                                <th scope="col" class="px-6 py-3">Payment Method</th>
                                <th scope="col" class="px-6 py-3">Sold By</th>
                                <th scope="col" class="px-6 py-3">Details</th>
                            </tr>
                        </thead>
                        <tbody>
                            {% for sale in sales %}
                            <tr class="bg-white border">
                                <td class="px-6 py-4">{{ sale.trxId }}</td>
                                <td class="px-6 py-4">{{ sale.date }}</td>
                                <td class="px-6 py-4">{{ sale.total_amount|format_khr }}</td>
                                <td class="px-6 py-4">{{ sale.get_payment_method_display }}</td>
                                <td class="px-6 py-4">{{ sale.sold_by.username }}</td>
                                <td class="px-6 py-4">
                                    <button type="button" class="text-blue-600 hover:underline view-details" data-sale-id="{{ sale.id }}">View Details</button>
                                </td>
                            </tr>
                            <tr class="bg-blue-50 border-t-2 border-blue-200 hidden sale-details" id="details-{{ sale.id }}">
                                <td colspan="6" class="px-6 py-4">
                                    <div class="flex justify-between items-center mb-2">
                                        <h4 class="font-semibold">Sale Items:</h4>
                                        <button type="button" class="close-details text-red-600 hover:text-red-800 text-sm" data-sale-id="{{ sale.id }}">Close</button>
                                    </div>
                                    <table class="w-full text-xs">
                                        <thead class="bg-gray-200"><tr>
                                                <th class="px-2 py-1 text-left">Product</th>
                                                <th class="px-2 py-1 text-left">Quantity</th>
                                                <th class="px-2 py-1 text-left">Price</th>
                                                <th class="px-2 py-1 text-left">Total</th>
                                            </tr>
                                        </thead>
                                        <tbody>
                                            {% for item in sale.items.all %}
                                            <tr>
                                                <td class="px-2 py-1">{{ item.product.name }}</td>
                                                <td class="px-2 py-1">{{ item.display_quantity }}</td>
                                                <td class="px-2 py-1">{{ item.price|format_khr }}</td>
                                                <td class="px-2 py-1">{{ item.total_price|format_khr }}</td>
                                            </tr>
                                            {% empty %}
                                            <tr>
                                                <td colspan="4" class="px-2 py-1 text-center">No items found for this sale.</td>
                                            </tr>
                                            {% endfor %}
                                        </tbody>
                                    </table>
                                    {% if sale.notes %}
                                    <div class="mt-2">
                                        <strong>Notes:</strong> {{ sale.notes }}
                                    </div>
                                    {% endif %}
                                </td>
                            </tr>
                            {% empty %}
                            <tr class="bg-white border">
                                <td colspan="6" class="px-6 py-4 text-center">
                                    No sales found for the selected date range.
                                </td>
                            </tr>
                            {% endfor %}
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block js %}
<script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
<script>
    document.addEventListener('DOMContentLoaded', function() {
        // Sales trend chart
        const ctx = document.getElementById('salesChart').getContext('2d');
        const salesChart = new Chart(ctx, {
            type: 'line',
            data: {
                labels: {{ chart_labels|safe }},
                datasets: [{
                    label: 'Sales Amount',
                    data: {{ chart_data|safe }},
                    backgroundColor: 'rgba(59, 130, 246, 0.2)',
                    borderColor: 'rgba(59, 130, 246, 1)',
                    borderWidth: 2,
                    tension: 0.1,
                    fill: true
                }]
            },
            options: {
                responsive: true,
                plugins: {
                    legend: {
                        position: 'top',
                    },
                    title: {
                        display: true,
                        text: 'Daily Sales Trend'
                    }
                },
                scales: {
                    y: {
                        beginAtZero: true,
                        ticks: {
                            callback: function(value) {
                                return value + '៛';
                            }
                        }
                    }
                }
            }
        });

        // Toggle sale details
        const detailButtons = document.querySelectorAll('.view-details');
        detailButtons.forEach(button => {
            button.addEventListener('click', function() {
                const saleId = this.getAttribute('data-sale-id');
                const detailsRow = document.getElementById(`details-${saleId}`);
                detailsRow.classList.toggle('hidden');
            });
        });

        const closeButtons = document.querySelectorAll('.close-details');
        closeButtons.forEach(button => {
            button.addEventListener('click', function() {
                const saleId = this.getAttribute('data-sale-id');
                const detailsRow = document.getElementById(`details-${saleId}`);
                detailsRow.classList.add('hidden');
            });
        });
    });
</script>
{% endblock %}
