{% extends 'base.html' %}
{% load static %}
{% load i18n %}

{% block title %}{% trans "Admin Action Logs" %} | Legend Fitness Club{% endblock %}

{% block body %}
<div class="conponentSection p-5 bg-gray-200">
    <div class="componentWrapper">
        <!-- Header with Back Button -->
        <div class="flex justify-between items-center mb-4">
            <h3 class="text-2xl font-bold">{% trans "Admin Action Logs" %}</h3>
            <a href="{% url 'user:index' %}" class="bg-gray-500 hover:bg-gray-600 text-white font-bold py-2 px-4 rounded transition duration-200">
                <i class="fas fa-arrow-left mr-2"></i>{% trans "Back to Users" %}
            </a>
        </div>

        <!-- Security Notice -->
        <div class="bg-yellow-100 border-l-4 border-yellow-500 text-yellow-700 p-4 mb-6" role="alert">
            <p class="font-bold">{% trans "Security Notice" %}</p>
            <p>{% trans "This page displays a log of all critical admin actions. These logs cannot be modified and are used for security auditing." %}</p>
        </div>

        <!-- Filters Section -->
        <div class="bg-white p-4 rounded shadow-md mb-6">
            <h3 class="text-lg font-semibold mb-4">{% trans "Filter Logs" %}</h3>
            <form method="get" class="grid grid-cols-1 md:grid-cols-3 gap-4">
                <div>
                    <label for="action_type" class="block text-sm font-medium text-gray-700 mb-1">{% trans "Action Type" %}</label>
                    <select name="action_type" id="action_type" class="w-full border-gray-300 rounded-md shadow-sm focus:border-indigo-300 focus:ring focus:ring-indigo-200 focus:ring-opacity-50">
                        <option value="">{% trans "All Actions" %}</option>
                        {% for action_code, action_name in action_types %}
                        <option value="{{ action_code }}" {% if selected_action_type == action_code %}selected{% endif %}>{{ action_name }}</option>
                        {% endfor %}
                    </select>
                </div>
                <div>
                    <label for="user_id" class="block text-sm font-medium text-gray-700 mb-1">{% trans "Admin User" %}</label>
                    <select name="user_id" id="user_id" class="w-full border-gray-300 rounded-md shadow-sm focus:border-indigo-300 focus:ring focus:ring-indigo-200 focus:ring-opacity-50">
                        <option value="">{% trans "All Admins" %}</option>
                        {% for admin in admin_users %}
                        <option value="{{ admin.id }}" {% if selected_user_id == admin.id|stringformat:"i" %}selected{% endif %}>{{ admin.username }} ({{ admin.name }})</option>
                        {% endfor %}
                    </select>
                </div>
                <div class="flex items-end space-x-2">
                    <button type="submit" class="bg-blue-500 hover:bg-blue-600 text-white font-bold py-2 px-4 rounded">
                        <i class="fas fa-filter mr-2"></i>{% trans "Apply Filters" %}
                    </button>
                    <a href="{% url 'user:admin_action_logs' %}" class="bg-gray-300 hover:bg-gray-400 text-gray-800 font-bold py-2 px-4 rounded">
                        <i class="fas fa-times mr-2"></i>{% trans "Clear" %}
                    </a>
                </div>
            </form>
        </div>

        <!-- Mobile Card View (visible on small screens) -->
        <div class="block md:hidden">
            <div class="space-y-4">
                {% for log in logs %}
                <div class="bg-white shadow rounded-lg overflow-hidden {% if 'Failed attempt' in log.description %}border-l-4 border-red-500{% endif %}">
                    <div class="p-4">
                        <div class="flex justify-between items-start mb-2">
                            <span class="px-2 py-1 inline-flex text-xs leading-5 font-semibold rounded-full
                                {% if log.action_type == 'create_user' %}bg-green-100 text-green-800
                                {% elif log.action_type == 'delete_user' %}bg-red-100 text-red-800
                                {% elif log.action_type == 'deactivate_user' %}bg-yellow-100 text-yellow-800
                                {% elif log.action_type == 'activate_user' %}bg-blue-100 text-blue-800
                                {% else %}bg-gray-100 text-gray-800{% endif %}">
                                {{ log.get_action_type_display }}
                            </span>
                            <span class="text-xs text-gray-500">{{ log.action_time|date:"Y-m-d H:i:s" }}</span>
                        </div>

                        <div class="mb-2">
                            <div class="text-sm font-medium text-gray-900">{% trans "Admin User" %}</div>
                            <div class="text-sm text-gray-700">
                                {% if log.user %}
                                {{ log.user.username }} ({{ log.user.name }})
                                {% else %}
                                {% trans "Unknown" %}
                                {% endif %}
                            </div>
                        </div>

                        {% if log.target_user %}
                        <div class="mb-2">
                            <div class="text-sm font-medium text-gray-900">{% trans "Target User" %}</div>
                            <div class="text-sm text-gray-700">{{ log.target_user.username }} ({{ log.target_user.name }})</div>
                        </div>
                        {% endif %}

                        <div class="mb-2">
                            <div class="text-sm font-medium text-gray-900">{% trans "IP Address" %}</div>
                            <div class="text-sm text-gray-700">{{ log.ip_address|default:"Unknown" }}</div>
                        </div>

                        <div>
                            <div class="text-sm font-medium text-gray-900">{% trans "Description" %}</div>
                            <div class="text-sm text-gray-700">{{ log.description }}</div>
                        </div>
                    </div>
                </div>
                {% empty %}
                <div class="bg-white shadow rounded-lg p-4 text-center text-gray-500">
                    {% trans "No action logs found." %}
                </div>
                {% endfor %}
            </div>
        </div>

        <!-- Logs Table (hidden on small screens, visible on medium and larger) -->
        <div class="hidden md:block bg-white p-4 rounded shadow-md">
            <div class="relative overflow-x-auto">
                <table class="w-full text-sm text-left">
                    <thead class="text-sm uppercase bg-blue-900 text-gray-50">
                        <tr>
                            <th scope="col" class="px-6 py-3">{% trans "Time" %}</th>
                            <th scope="col" class="px-6 py-3">{% trans "Admin User" %}</th>
                            <th scope="col" class="px-6 py-3">{% trans "Action" %}</th>
                            <th scope="col" class="px-6 py-3">{% trans "Target User" %}</th>
                            <th scope="col" class="px-6 py-3">{% trans "IP Address" %}</th>
                            <th scope="col" class="px-6 py-3">{% trans "Description" %}</th>
                        </tr>
                    </thead>
                    <tbody>
                        {% for log in logs %}
                        <tr class="bg-white border {% if 'Failed attempt' in log.description %}bg-red-50{% endif %}">
                            <td class="px-6 py-4">{{ log.action_time|date:"Y-m-d H:i:s" }}</td>
                            <td class="px-6 py-4">
                                {% if log.user %}
                                <div class="font-medium">{{ log.user.username }}</div>
                                <div class="text-gray-500">{{ log.user.name }}</div>
                                {% else %}
                                <span class="text-gray-400">{% trans "Unknown" %}</span>
                                {% endif %}
                            </td>
                            <td class="px-6 py-4">
                                <span class="px-2 py-1 inline-flex text-xs leading-5 font-semibold rounded-full
                                    {% if log.action_type == 'create_user' %}bg-green-100 text-green-800
                                    {% elif log.action_type == 'delete_user' %}bg-red-100 text-red-800
                                    {% elif log.action_type == 'deactivate_user' %}bg-yellow-100 text-yellow-800
                                    {% elif log.action_type == 'activate_user' %}bg-blue-100 text-blue-800
                                    {% else %}bg-gray-100 text-gray-800{% endif %}">
                                    {{ log.get_action_type_display }}
                                </span>
                            </td>
                            <td class="px-6 py-4">
                                {% if log.target_user %}
                                <div class="font-medium">{{ log.target_user.username }}</div>
                                <div class="text-gray-500">{{ log.target_user.name }}</div>
                                {% else %}
                                <span class="text-gray-400">{% trans "N/A" %}</span>
                                {% endif %}
                            </td>
                            <td class="px-6 py-4">{{ log.ip_address|default:"Unknown" }}</td>
                            <td class="px-6 py-4">{{ log.description }}</td>
                        </tr>
                        {% empty %}
                        <tr class="bg-white border">
                            <td colspan="6" class="px-6 py-4 text-center">{% trans "No action logs found." %}</td>
                        </tr>
                        {% endfor %}
                    </tbody>
                </table>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block js %}
<script>
    document.addEventListener('DOMContentLoaded', function() {
        // Auto-submit form when filters change
        const filterForm = document.querySelector('form');
        const filterSelects = filterForm.querySelectorAll('select');

        filterSelects.forEach(select => {
            select.addEventListener('change', function() {
                filterForm.submit();
            });
        });
    });
</script>
{% endblock %}
