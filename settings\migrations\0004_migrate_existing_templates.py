# Generated manually to fix migration issues

from django.db import migrations
from django.utils import timezone

def migrate_existing_templates(apps, schema_editor):
    """
    Migrate templates from existing models to the new centralized PrintTemplate model
    """
    # Get the models
    PrintTemplate = apps.get_model('settings', 'PrintTemplate')
    
    # Try to get PaymentTemplate
    try:
        PaymentTemplate = apps.get_model('payment', 'PaymentTemplate')
        payment_templates = PaymentTemplate.objects.all()
    except:
        payment_templates = []
    
    # Try to get ReceiptTemplate
    try:
        ReceiptTemplate = apps.get_model('paypervisit', 'ReceiptTemplate')
        receipt_templates = ReceiptTemplate.objects.all()
    except:
        receipt_templates = []
    
    # Migrate payment templates
    for template in payment_templates:
        print_template = PrintTemplate(
            name=template.name,
            template_type='payment',
            is_default=template.is_default,
            language=template.language,
            header_text=template.header_text,
            subheader_text=template.subheader_text,
            footer_text=template.footer_text,
            background_color=getattr(template, 'background_color', '#ffffff'),
            text_color=getattr(template, 'text_color', '#000000'),
            accent_color=getattr(template, 'accent_color', '#2563eb'),
            custom_css=getattr(template, 'custom_css', ''),
            show_company_info=getattr(template, 'show_company_info', True),
            show_signatures=getattr(template, 'show_signatures', True),
            show_logo=getattr(template, 'show_logo', True),
            created_at=getattr(template, 'created_at', timezone.now()),
            updated_at=getattr(template, 'updated_at', timezone.now()),
        )
        print_template.save()
    
    # Migrate receipt templates
    for template in receipt_templates:
        print_template = PrintTemplate(
            name=template.name,
            template_type='paypervisit',
            is_default=template.is_default,
            language=template.language,
            header_text=template.header_text,
            subheader_text=getattr(template, 'subheader_text', 'Pay-per-visit Receipt'),
            footer_text=template.footer_text,
            background_color=getattr(template, 'background_color', '#ffffff'),
            text_color=getattr(template, 'text_color', '#000000'),
            accent_color=getattr(template, 'accent_color', '#2563eb'),
            custom_css=getattr(template, 'custom_css', ''),
            show_company_info=getattr(template, 'show_company_info', True),
            show_signatures=getattr(template, 'show_signatures', True),
            show_logo=getattr(template, 'show_logo', True),
            created_at=getattr(template, 'created_at', timezone.now()),
            updated_at=getattr(template, 'updated_at', timezone.now()),
        )
        print_template.save()

class Migration(migrations.Migration):

    dependencies = [
        ('settings', '0003_printtemplate'),
    ]

    operations = [
        migrations.RunPython(migrate_existing_templates),
    ]
