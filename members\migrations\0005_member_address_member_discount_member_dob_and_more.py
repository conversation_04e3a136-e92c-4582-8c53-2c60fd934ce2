# Generated by Django 5.0.2 on 2025-05-14 21:31

from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('members', '0004_remove_package_freeze_policy'),
    ]

    operations = [
        migrations.AddField(
            model_name='member',
            name='address',
            field=models.TextField(blank=True, null=True, verbose_name='Address'),
        ),
        migrations.AddField(
            model_name='member',
            name='discount',
            field=models.IntegerField(default=0, verbose_name='Discount Amount'),
        ),
        migrations.AddField(
            model_name='member',
            name='dob',
            field=models.DateField(blank=True, null=True, verbose_name='Date of Birth'),
        ),
        migrations.AlterField(
            model_name='member',
            name='due_payment',
            field=models.IntegerField(default=0, verbose_name='Due Payment'),
        ),
        migrations.AlterField(
            model_name='member',
            name='telegram',
            field=models.CharField(blank=True, max_length=100, null=True, verbose_name='Telegram/Messenger'),
        ),
    ]
