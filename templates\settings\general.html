{% extends 'base.html' %}
{% load static %}



{% block body %}
<div class="conponentSection p-5 bg-gray-200">
    <div class="componentWrapper">
        <!-- Header with Back Button -->
        <div class="flex items-center mb-4">
            <a href="{% url 'settings:dashboard' %}" class="bg-blue-900 text-white p-2 rounded-full hover:bg-blue-800 mr-3">
                <i class="fas fa-arrow-left"></i>
            </a>
            <h2 class="text-2xl font-bold">General Settings</h2>
        </div>

        <!-- Settings Card -->
        <div class="bg-white rounded-lg shadow-md overflow-hidden">
            <!-- Card Header -->
            <div class="bg-blue-900 text-white p-4">
                <div class="flex items-center">
                    <i class="fas fa-building text-2xl mr-3"></i>
                    <h3 class="text-xl font-bold">Gym Information</h3>
                </div>
            </div>

            <!-- Card Body -->
            <div class="p-6">
                <form method="post">
                    {% csrf_token %}

                    <div class="mb-6">
                        <label class="block text-sm font-medium mb-2">Gym Name*</label>
                        <input class="border w-full p-4 leading-tight bg-slate-100 rounded"
                               id="gym_name"
                               name="gym_name"
                               type="text"
                               placeholder="Enter gym name"
                               value="{{ settings.gym_name }}"
                               required />
                        <p class="text-sm text-gray-500 mt-2">This name will appear on receipts and reports</p>
                    </div>

                    <div class="mb-6">
                        <label class="block text-sm font-medium mb-2">Contact Email</label>
                        <input class="border w-full p-4 leading-tight bg-slate-100 rounded"
                               id="contact_email"
                               name="contact_email"
                               type="email"
                               placeholder="Enter contact email"
                               value="{{ settings.contact_email|default:'' }}" />
                        <p class="text-sm text-gray-500 mt-2">Email address for notifications and customer contact</p>
                    </div>

                    <div class="mb-6">
                        <label class="block text-sm font-medium mb-2">Contact Phone</label>
                        <input class="border w-full p-4 leading-tight bg-slate-100 rounded"
                               id="contact_phone"
                               name="contact_phone"
                               type="text"
                               placeholder="Enter contact phone"
                               value="{{ settings.contact_phone|default:'' }}" />
                        <p class="text-sm text-gray-500 mt-2">Phone number for customer inquiries</p>
                    </div>

                    <div class="mb-6">
                        <label class="block text-sm font-medium mb-2">Address</label>
                        <textarea class="border w-full p-4 leading-tight bg-slate-100 rounded"
                                  id="address"
                                  name="address"
                                  rows="3"
                                  placeholder="Enter gym address">{{ settings.address|default:'' }}</textarea>
                        <p class="text-sm text-gray-500 mt-2">Physical address of the gym</p>
                    </div>

                    <!-- Info Box -->
                    <div class="mt-3 p-3 bg-white rounded border border-blue-100 mb-6">
                        <p class="text-sm text-gray-700"><i class="fas fa-info-circle text-blue-600 mr-1"></i> These settings control the basic information about your gym that appears throughout the system.</p>
                    </div>

                    <div class="flex items-center justify-between">
                        <a href="{% url 'settings:dashboard' %}" class="bg-gray-300 hover:bg-gray-400 text-gray-800 font-bold py-3 px-6 rounded">Cancel</a>
                        <button class="bg-blue-900 hover:bg-blue-800 text-white font-bold py-3 px-6 rounded"
                                type="submit"><i class="fas fa-save mr-2"></i> Save Changes</button>
                    </div>
                </form>
            </div>

            <!-- Last Updated Info -->
            <div class="bg-gray-100 p-4 border-t">
                <p class="text-sm text-gray-600">
                    <i class="fas fa-clock mr-1"></i> Last updated: {{ settings.last_updated|date:"F j, Y H:i" }}
                </p>
            </div>
        </div>
    </div>
</div>
{% endblock %}
