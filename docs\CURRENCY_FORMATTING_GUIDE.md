# Currency Formatting Guide for Developers

This guide provides detailed instructions on how to implement and use the currency formatting system in the Legend Fitness Club project.

## Table of Contents

1. [Introduction](#introduction)
2. [Currency Formatting Standards](#currency-formatting-standards)
3. [Backend Implementation](#backend-implementation)
4. [Frontend Implementation](#frontend-implementation)
5. [Form Handling](#form-handling)
6. [Testing](#testing)
7. [Troubleshooting](#troubleshooting)

## Introduction

The Legend Fitness Club project uses a standardized currency formatting system to ensure consistent display of monetary values throughout the application. This system supports both Cambodian Riel (KHR) and US Dollar (USD) currencies.

## Currency Formatting Standards

All monetary values in the Legend Fitness Club project follow these formatting standards:

1. **Numbers include thousand separators using commas**
   - Example: "1,000" instead of "1000"
   - Example: "2,434,556" instead of "2434556"

2. **Currency symbols are consistently applied**
   - Cambodian Riel (KHR): ៛ (symbol appears after the number)
   - US Dollar (USD): $ (symbol appears before the number)

3. **Decimal places**
   - KHR values are displayed as whole numbers without decimal places
   - USD values are displayed with 2 decimal places

## Backend Implementation

### Django Template Filters

We've implemented three template filters for formatting currency values:

1. **`format_khr`**: Formats a number as Cambodian Riel with thousand separators
   ```django
   {{ value|format_khr }}  <!-- Example: 10000 -> 10,000៛ -->
   ```

2. **`format_usd`**: Formats a number as US Dollar with thousand separators
   ```django
   {{ value|format_usd }}  <!-- Example: 10000 -> $10,000.00 -->
   ```

3. **`format_number_with_commas`**: Formats a number with thousand separators without any currency symbol
   ```django
   {{ value|format_number_with_commas }}  <!-- Example: 10000 -> 10,000 -->
   ```

### How to Use in Templates

1. Load the currency filters at the top of your template:
   ```django
   {% load currency_filters %}
   ```

2. Apply the appropriate filter to your currency values:
   ```django
   <p>Price: {{ product.price|format_khr }}</p>
   <p>USD Price: {{ product.usd_price|format_usd }}</p>
   ```

3. For templates that might use both custom filters and currency filters:
   ```django
   {% load custom_filters %}
   {% load currency_filters %}
   ```

## Frontend Implementation

### JavaScript Utility Functions

For client-side formatting, we've implemented JavaScript utility functions in `static/js/currency-formatter.js`:

1. **`formatKHR(value)`**: Formats a number as Cambodian Riel with thousand separators
   ```javascript
   formatKHR(10000);  // Returns "10,000៛"
   ```

2. **`formatUSD(value)`**: Formats a number as US Dollar with thousand separators
   ```javascript
   formatUSD(10000);  // Returns "$10,000.00"
   ```

3. **`formatNumber(value, decimals = 0)`**: Formats a number with thousand separators without any currency symbol
   ```javascript
   formatNumber(10000);  // Returns "10,000"
   formatNumber(10000, 2);  // Returns "10,000.00"
   ```

### How to Use in JavaScript

1. Make sure the currency-formatter.js script is included in your template:
   ```html
   <script src="{% static 'js/currency-formatter.js' %}"></script>
   ```

2. Use the formatting functions in your JavaScript code:
   ```javascript
   const price = 10000;
   const formattedPrice = formatKHR(price);  // "10,000៛"
   ```

## Form Handling

### Input Field Formatting

HTML number inputs don't support thousand separators, so we need to use text inputs and handle the formatting:

1. **Change input type from "number" to "text"**:
   ```html
   <input type="text" id="amount_khr" name="amount_khr" class="..." value="{{ value }}">
   ```

2. **Use the `setupFormattedInput` function to handle formatting**:
   ```javascript
   const amountInput = document.getElementById('amount_khr');
   setupFormattedInput(amountInput, 'khr');
   ```

3. **Convert formatted values back to numeric values before form submission**:
   ```javascript
   const form = document.getElementById('my-form');
   form.addEventListener('submit', function(e) {
       // Get the current formatted value
       const formattedValue = amountInput.value;
       
       // Convert to numeric value for submission
       amountInput.value = formattedValue.replace(/[^\d.-]/g, '');
   });
   ```

### Complete Example

```html
<form id="payment-form" method="post">
    {% csrf_token %}
    <div class="form-group">
        <label for="amount_khr">Amount (KHR)</label>
        <input type="text" id="amount_khr" name="amount_khr" value="{{ amount }}">
    </div>
    <button type="submit">Submit</button>
</form>

<script>
    document.addEventListener('DOMContentLoaded', function() {
        const amountInput = document.getElementById('amount_khr');
        
        // Setup formatted input
        setupFormattedInput(amountInput, 'khr');
        
        // Handle form submission
        const form = document.getElementById('payment-form');
        form.addEventListener('submit', function(e) {
            // Convert formatted value back to numeric value
            amountInput.value = amountInput.value.replace(/[^\d.-]/g, '');
        });
    });
</script>
```

## Testing

### Running Django Tests

We've created unit tests for the currency formatting functions. To run these tests:

```bash
python manage.py test core.tests.test_currency_formatters
```

### Running JavaScript Tests

We've created a JavaScript test page that you can access at:

```
/test-js-currency-formatting/
```

This page allows you to:
1. Run automated tests for the JavaScript formatting functions
2. Manually test the formatting functions with different inputs

## Troubleshooting

### Common Issues

1. **Currency values not formatted correctly**:
   - Make sure you're loading the correct filter: `{% load currency_filters %}`
   - Check that you're using the correct filter for the currency type: `format_khr` for KHR, `format_usd` for USD

2. **Form submission issues with formatted values**:
   - Make sure you're converting formatted values back to numeric values before form submission
   - Check that you're using the correct regex to remove non-numeric characters: `.replace(/[^\d.-]/g, '')`

3. **JavaScript formatting not working**:
   - Make sure the currency-formatter.js script is included in your template
   - Check the browser console for any JavaScript errors

### Getting Help

If you encounter any issues with the currency formatting system, please:

1. Check the documentation in `docs/CURRENCY_FORMATTING.md`
2. Run the tests to ensure the formatting functions are working correctly
3. Contact the development team for assistance

## Best Practices

1. **Always use the provided filters and functions** for formatting currency values to maintain consistency.

2. **Change input types from "number" to "text"** when using formatted currency values, as HTML number inputs don't support thousand separators.

3. **Remember to convert formatted values back to numeric values** before form submission.

4. **Use the appropriate filter for each currency type**:
   - Use `format_khr` for Cambodian Riel values
   - Use `format_usd` for US Dollar values
   - Use `format_number_with_commas` for generic numeric values

5. **Include both currency filters in templates** that might display either currency:
   ```html
   {% load custom_filters %}
   {% load currency_filters %}
   ```
