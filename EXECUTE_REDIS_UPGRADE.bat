@echo off
echo ============================================================
echo Redis 7.4.3 Upgrade Execution for Legend Fitness Club
echo ============================================================
echo.

REM Check if running as administrator
net session >nul 2>&1
if %errorLevel% neq 0 (
    echo ERROR: This script must be run as Administrator!
    echo Right-click on this file and select "Run as administrator"
    pause
    exit /b 1
)

echo ✓ Running as Administrator - proceeding with upgrade...
echo.

echo Step 1: Checking current Redis status...
sc query redis
echo.

echo Step 2: Stopping Redis 3.0.504 service...
sc stop redis
if %errorlevel% equ 0 (
    echo ✓ Redis 3.0.504 service stop command sent
) else (
    echo ⚠ Redis service may not be running
)

echo Waiting for service to stop...
timeout /t 5 /nobreak >nul

echo Checking service status...
sc query redis
echo.

echo Step 3: Verifying Redis 7.4.3 installation...
if not exist "C:\Redis-7.4.3\redis-server.exe" (
    echo ERROR: Redis 7.4.3 not found at C:\Redis-7.4.3
    pause
    exit /b 1
)

if not exist "C:\Redis-7.4.3\redis-cli.exe" (
    echo ERROR: Redis CLI not found at C:\Redis-7.4.3
    pause
    exit /b 1
)

echo ✓ Redis 7.4.3 files found
echo.

echo Step 4: Setting up Redis 7.4.3 configuration...
if exist "c:\Final Project\legend_fitness_club-gym-ms\redis_7_4_3_config.conf" (
    copy "c:\Final Project\legend_fitness_club-gym-ms\redis_7_4_3_config.conf" "C:\Redis-7.4.3\redis.windows-service.conf"
    echo ✓ Configuration file copied
) else (
    echo ⚠ Creating default configuration...
    (
    echo port 6379
    echo bind 127.0.0.1
    echo timeout 0
    echo tcp-keepalive 60
    echo loglevel notice
    echo logfile "redis-server.log"
    echo databases 16
    echo save 900 1
    echo save 300 10
    echo save 60 10000
    echo maxmemory 1gb
    echo maxmemory-policy allkeys-lru
    echo notify-keyspace-events Ex
    echo lazyfree-lazy-eviction yes
    echo lazyfree-lazy-expire yes
    echo lazyfree-lazy-server-del yes
    echo replica-lazy-flush yes
    ) > "C:\Redis-7.4.3\redis.windows-service.conf"
    echo ✓ Default configuration created
)
echo.

echo Step 5: Starting Redis 7.4.3 server...
cd /d "C:\Redis-7.4.3"

echo Starting Redis 7.4.3 in background...
start "Redis 7.4.3" redis-server.exe redis.windows-service.conf

echo Waiting for Redis 7.4.3 to initialize...
timeout /t 5 /nobreak >nul
echo.

echo Step 6: Testing Redis 7.4.3 connectivity...
redis-cli.exe ping >nul 2>&1
if %errorlevel% equ 0 (
    echo ✓ Redis 7.4.3 is responding to ping!
    
    echo ✓ Redis version:
    redis-cli.exe info server | findstr redis_version
    
    echo Testing basic operations...
    redis-cli.exe set test "Redis 7.4.3 Working" >nul 2>&1
    redis-cli.exe get test
    redis-cli.exe del test >nul 2>&1
    
    echo ✓ Redis 7.4.3 basic operations successful
    
    echo Testing keyspace notifications...
    redis-cli.exe config get notify-keyspace-events
    
) else (
    echo ✗ Redis 7.4.3 is not responding
    echo Please check the Redis server window for errors
)
echo.

echo Step 7: Testing Django integration...
cd /d "c:\Final Project\legend_fitness_club-gym-ms"

echo Running Django Redis test...
python manage.py test_redis --detailed
echo.

echo ============================================================
echo Redis 7.4.3 Upgrade Execution Complete!
echo ============================================================
echo.
echo Next Steps:
echo 1. Start Django server: python manage.py runserver 8000
echo 2. Check for Redis 7.4.3 detection in startup logs
echo 3. Test WebSocket functionality in browser
echo 4. Verify real-time permission updates work
echo.
echo Expected Django startup messages:
echo "✓ Redis 7.4.3 detected - using Redis channel layer"
echo "✓ Redis detected - using Redis cache backend"
echo.

pause
