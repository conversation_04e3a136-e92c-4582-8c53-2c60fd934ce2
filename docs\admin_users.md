# Creating Admin Users in Legend Fitness Club System

This document explains how to create admin users in the Legend Fitness Club management system.

## Why Admin Users Are Special

Admin users have full access to all features of the system and should be created with care. Unlike regular employees (managers, cashiers, coaches, etc.), admin users should be created through Django's admin interface or command line, not through the regular employee forms.

## Method 1: Using Django Admin Interface

If you already have an admin user and can access the Django admin interface:

1. Log in to the Django admin interface at `http://your-domain/admin/`
2. Navigate to "Users" under the "Authentication and Authorization" section
3. Click "Add User"
4. Fill in the username and password
5. Click "Save and continue editing"
6. In the next screen, fill in additional details:
   - Personal info (name, email)
   - Set "Staff status" to "Active"
   - Set "Superuser status" to "Active"
   - Add to the "admin" group
7. Click "Save"

## Method 2: Using Custom Management Command (Recommended)

If you don't have an admin user yet or prefer using the command line:

1. Open a terminal/command prompt
2. Navigate to your project directory
3. Run the following command:

```
python manage.py creategymadmin
```

4. Follow the prompts to enter a username, email, password, and full name
5. The user will be created with superuser privileges and the admin role

You can also provide the parameters directly:

```
python manage.py creategymadmin --username=admin --email=<EMAIL> --password=secure_password --name="Admin Name"
```

## Method 3: Using Standard Superuser Command (Not Recommended)

You can use Django's built-in createsuperuser command, but you'll need to update the user afterward:

1. Run the command:

```
python manage.py createsuperuser
```

2. Follow the prompts to enter a username, email, and password
3. After creating the superuser, you'll need to update it with the required fields using the Django shell (see Method 4)

## Method 4: Using Django Shell

If you need more control over the user creation process:

1. Open a terminal/command prompt
2. Navigate to your project directory
3. Run the following command to open the Django shell:

```
python manage.py shell
```

4. In the shell, run the following commands:

```python
from user.models import User
from django.contrib.auth.models import Group

# Create the user
user = User.objects.create_user(
    username='admin_username',
    email='<EMAIL>',
    password='secure_password'
)

# Set admin attributes
user.is_staff = True
user.is_superuser = True
user.role = 'admin'
user.is_manager = True
user.name = 'Admin Name'
user.save()

# Add to admin group
admin_group, _ = Group.objects.get_or_create(name='admin')
user.groups.add(admin_group)
```

5. Exit the shell by typing `exit()`

## After Creating an Admin User

After creating an admin user, you can:

1. Log in to the system using the admin credentials
2. Access all features of the system
3. Create and manage other users
4. Configure system settings

## Security Considerations

- Keep admin credentials secure
- Use strong passwords for admin accounts
- Limit the number of admin users
- Regularly review the list of admin users
- Consider using two-factor authentication for admin accounts if available
