{% extends 'base.html' %}
{% load static %}



{% block body %}
<div class="conponentSection p-5 bg-gray-200">
    <div class="componentWrapper">
        <!-- Header with Back Button -->
        <div class="flex items-center mb-4">
            <a href="{% url 'settings:system' %}" class="bg-blue-900 text-white p-2 rounded-full hover:bg-blue-800 mr-3">
                <i class="fas fa-arrow-left"></i>
            </a>
            <h2 class="text-2xl font-bold">Database Backups</h2>
        </div>

        <!-- Warning Banner -->
        <div class="bg-blue-100 border-l-4 border-blue-500 text-blue-700 p-4 mb-6">
            <div class="flex items-center">
                <i class="fas fa-info-circle text-2xl mr-3"></i>
                <div>
                    <p class="font-bold">Backup Management</p>
                    <p>This page allows you to manage your database backups. You can create new backups, download existing ones, or restore your database from a previous backup.</p>
                </div>
            </div>
        </div>

        <!-- Create Backup Button -->
        <div class="mb-6">
            <form method="post" action="{% url 'settings:backup_database' %}">
                {% csrf_token %}
                <button type="submit" class="bg-blue-600 hover:bg-blue-700 text-white font-bold py-3 px-6 rounded"><i class="fas fa-download mr-2"></i> Create New Backup</button>
            </form>
        </div>

        <!-- Backup List -->
        <div class="bg-white rounded-lg shadow-md overflow-hidden mb-6">
            <div class="bg-blue-900 text-white p-4">
                <div class="flex items-center">
                    <i class="fas fa-database text-2xl mr-3"></i>
                    <h3 class="text-xl font-bold">Available Backups</h3>
                </div>
            </div>

            <div class="p-6">
                {% if backups %}
                    <div class="overflow-x-auto">
                        <table class="min-w-full bg-white">
                            <thead>"<tr class="bg-gray-100 text-gray-700 uppercase text-sm leading-normal">
                                    <th class="py-3 px-6 text-left">Filename</th>
                                    <th class="py-3 px-6 text-left">Date</th>
                                    <th class="py-3 px-6 text-left">Size</th>
                                    <th class="py-3 px-6 text-center">Actions</th>
                                </tr>
                            </thead>
                            <tbody class="text-gray-600 text-sm">
                                {% for backup in backups %}
                                <tr class="border-b border-gray-200 hover:bg-gray-50">
                                    <td class="py-3 px-6 text-left">
                                        <div class="flex items-center">
                                            <i class="fas fa-file-code text-blue-900 mr-2"></i>
                                            <span>{{ backup.filename }}</span>
                                        </div>
                                    </td>
                                    <td class="py-3 px-6 text-left">
                                        {{ backup.date|date:"F j, Y H:i" }}
                                    </td>
                                    <td class="py-3 px-6 text-left">
                                        {{ backup.size }}
                                    </td>
                                    <td class="py-3 px-6 text-center">
                                        <div class="flex item-center justify-center">
                                            <!-- Download Button -->
                                            <a href="/backups/{{ backup.filename }}" download class="w-8 h-8 mr-2 transform hover:text-blue-500 hover:scale-110 flex items-center justify-center" title="Download">
                                                <i class="fas fa-download"></i>
                                            </a>

                                            <!-- Restore Button -->
                                            <form method="post" action="{% url 'settings:restore_database' backup.filename %}" class="inline" onsubmit="return confirm('Are you sure you want to restore the database from this backup? This will overwrite your current data.')">
                                                {% csrf_token %}
                                                <button type="submit" class="w-8 h-8 mr-2 transform hover:text-green-500 hover:scale-110 flex items-center justify-center" title="Restore"><i class="fas fa-undo-alt"></i></button>
                                            </form>

                                            <!-- Delete Button -->
                                            <form method="post" action="{% url 'settings:delete_backup' backup.filename %}" class="inline" onsubmit="return confirm('Are you sure you want to delete this backup? This action cannot be undone.')">
                                                {% csrf_token %}
                                                <button type="submit" class="w-8 h-8 transform hover:text-red-500 hover:scale-110 flex items-center justify-center" title="Delete"><i class="fas fa-trash-alt"></i></button>
                                            </form>
                                        </div>
                                    </td>
                                </tr>
                                {% endfor %}
                            </tbody>
                        </table>
                    </div>
                {% else %}
                    <div class="text-center py-8">
                        <i class="fas fa-database text-gray-300 text-5xl mb-4"></i>
                        <p class="text-gray-500">No backups available. Create your first backup using the button above.</p>
                    </div>
                {% endif %}
            </div>
        </div>

        <!-- Backup Information -->
        <div class="bg-white rounded-lg shadow-md overflow-hidden">
            <div class="bg-blue-900 text-white p-4">
                <div class="flex items-center">
                    <i class="fas fa-info-circle text-2xl mr-3"></i>
                    <h3 class="text-xl font-bold">Backup Information</h3>
                </div>
            </div>

            <div class="p-6">
                <h4 class="text-lg font-semibold mb-2">About Database Backups</h4>
                <p class="mb-4">Backups are essential for protecting your data. Here's what you need to know:</p>

                <ul class="list-disc pl-6 mb-4 space-y-2">
                    <li>Backups contain a complete copy of your database at the time they were created.</li>
                    <li>Restoring a backup will replace your current database with the data from the backup.</li>
                    <li>It's recommended to create regular backups, especially before making significant changes.</li>
                    <li>You can download backups to store them externally for additional safety.</li>
                </ul>

                <div class="bg-yellow-50 border-l-4 border-yellow-500 text-yellow-700 p-4 mt-4">
                    <div class="flex">
                        <i class="fas fa-exclamation-triangle mr-2"></i>
                        <div>
                            <p class="font-bold">Important</p>
                            <p>Restoring a backup will overwrite all current data in your database. This action cannot be undone.</p>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}
