from django.shortcuts import render, redirect, get_object_or_404
from django.contrib.auth.hashers import make_password, check_password
from django.contrib import messages
from django.contrib.auth.decorators import login_required
from django.contrib.auth.models import Group
from django.core.files.storage import default_storage
from django.core.files.base import ContentFile
from django.db import connection, models
from django.utils import timezone
from datetime import datetime

from user.models import User, AdminActionLog, UserActionLog
from core.decorators import module_permission_required
from user.logging_utils import log_user_action
from user.middleware import SuspiciousActivityDetector

# Create your views here.
@login_required
@module_permission_required(module='user', required_level='view')
def index(request):
    # Get all users except those with admin role for the employees list
    employees = User.objects.exclude(role='admin')

    # Get system users (employees with system access)
    system_users = User.objects.filter(is_staff=True).exclude(role='admin')

    # Get admin users for the admin tab
    admin_users = User.objects.filter(role='admin')

    context = {
        'employees': employees,
        'system_users': system_users,
        'admin_users': admin_users,
    }
    return render(request, 'user/index.html', context)

@login_required
@module_permission_required(module='user', required_level='edit')
def add_employee(request):
    """
    Dedicated page for adding a new employee
    """
    if request.method == "POST":
        # Get form data
        name = request.POST.get("name")
        phone = request.POST.get("phone")

        # Parse date fields from datepicker format (mm/dd/yyyy) to Django format (yyyy-mm-dd)
        dob_str = request.POST.get("dob")
        join_date_str = request.POST.get("join_date")

        # Convert date strings to proper format
        try:
            if dob_str:
                dob = datetime.strptime(dob_str, '%m/%d/%Y').strftime('%Y-%m-%d')
            else:
                dob = None

            if join_date_str:
                join_date = datetime.strptime(join_date_str, '%m/%d/%Y').strftime('%Y-%m-%d')
            else:
                join_date = None
        except ValueError:
            # If date parsing fails, use the original strings (they might already be in the correct format)
            dob = dob_str
            join_date = join_date_str

        gender = request.POST.get("gender")
        role = request.POST.get("role")
        salary = request.POST.get("salary")
        address = request.POST.get("address")
        work_schedule = request.POST.get("schedule")
        email = request.POST.get("email", "")  # Optional email field

        # Validate form data
        if User.objects.filter(phone=phone).exists():
            messages.error(request, f"Phone number '{phone}' is already registered. Please use a different phone number.")
            return redirect('user:add_employee')

        try:
            # Get the last user ID
            last_user = User.objects.last()
            if last_user:
                lastId = last_user.id
            else:
                lastId = 0

            # Find the highest employee ID number to ensure uniqueness
            highest_emp_id = 0
            for emp in User.objects.filter(emp_id__isnull=False):
                if emp.emp_id and emp.emp_id.startswith('LFC-'):
                    try:
                        id_num = int(emp.emp_id.split('-')[1])
                        highest_emp_id = max(highest_emp_id, id_num)
                    except (ValueError, IndexError):
                        pass

            # Generate the next employee ID
            next_emp_id = highest_emp_id + 1

            # Create the employee (without system access)
            user = User.objects.create(
                username=f"emp_{lastId+1}",  # Generate a temporary username
                name=name,
                phone=phone,
                dob=dob,
                gender=gender,
                salary=salary,
                # Store both address and work schedule in the address field
                address=f"Address: {address}\n\nWork Schedule: {work_schedule}",
                password=make_password("defaultpassword"),  # Set a default password
                emp_id=f"LFC-{next_emp_id:03d}",  # Format: LFC-001, LFC-002, etc.
                join_date=join_date,  # Use the join date from the form
                is_manager=(role == 'admin'),  # Only admin users are managers now
                is_employee=True,
                is_staff=False,  # No system access by default
                email=email,  # Use the email from the form
                role=role  # Store the role for later use in user registration
            )

            # Handle photo upload if provided
            if 'photo' in request.FILES:
                photo = request.FILES['photo']
                if photo:
                    # Save the photo in media/employee_photos directory
                    path = f'employee_photos/{user.id}_{photo.name}'
                    default_storage.save(path, ContentFile(photo.read()))

                    # Store the photo path directly in the user object
                    user.photo = path
                    user.save()

            messages.success(request, f"Employee '{name}' created successfully. To give system access, use the User Registration form.")
            return redirect('user:index')  # Redirect to employee list after successful creation

        except Exception as e:
            messages.error(request, f"Error creating employee: {str(e)}")

    return render(request, 'user/add_employee.html')


@login_required
@module_permission_required(module='user', required_level='edit')
def user_registration(request):
    # Get employees without system access (excluding admin users)
    employees_without_access = User.objects.filter(is_staff=False).exclude(role='admin')

    # Get users with system access (excluding admin users)
    users_with_access = User.objects.filter(is_staff=True).exclude(role='admin')

    if request.method == "POST":
        # Get form data
        employee_id = request.POST.get("employee")
        username = request.POST.get("username")
        email = request.POST.get("email")
        role = request.POST.get("role")
        password = request.POST.get("password")
        confirm_password = request.POST.get("confirm_password")

        # Validate form data
        if User.objects.filter(username=username).exists():
            messages.error(request, f"Username '{username}' already exists. Please choose a different username.")
            return redirect('user:register')

        if password != confirm_password:
            messages.error(request, "Passwords do not match.")
            return redirect('user:register')

        try:
            # Get the employee
            employee = User.objects.get(id=employee_id)

            # Handle role assignment based on employee's current role
            if employee.role:
                # For roles that can have system access, enforce exact match
                if employee.role in ['cashier', 'coach']:
                    if employee.role != role:
                        messages.warning(request, f"Role must match the employee's role. Automatically setting role to {employee.role}.")
                        role = employee.role
                # For roles that can't have system access (cleaner, security), validate the selected role
                elif employee.role not in ['admin', 'cashier', 'coach']:
                    # Validate that the selected role can have system access
                    if role not in ['cashier', 'coach']:
                        messages.error(request, f"Role '{role}' cannot have system access. Please select a system access role (Cashier or Coach).")
                        return redirect("user:register")
                    else:
                        # Allow changing from a non-system access role to a system access role
                        messages.success(request, f"Employee role will be changed from '{employee.role}' to '{role}' to grant system access.")
            else:
                # If employee has no role, validate that the selected role can have system access
                if role not in ['cashier', 'coach']:
                    messages.error(request, f"Role '{role}' cannot have system access. Please select a system access role (Cashier or Coach).")
                    return redirect("user:register")

            # Prevent non-admin users from being registered as admin
            if role == 'admin':
                messages.error(request, "Admin users cannot be created through this form. Please use the command line.")
                return redirect('user:register')

            # Determine if this role should have system access
            has_system_access = role in ['admin', 'cashier', 'coach']

            # Update the employee with user credentials
            employee.username = username
            employee.email = email
            employee.password = make_password(password)
            employee.is_manager = (role == 'admin')  # Only admin users are managers now
            employee.is_staff = has_system_access  # Only give system access to roles that need it
            employee.role = role  # Update the role
            employee.save()

            # Assign to appropriate group based on role
            # Clear existing groups first
            employee.groups.clear()

            # Add to appropriate group
            if role == 'admin':
                admin_group, _ = Group.objects.get_or_create(name='admin')
                employee.groups.add(admin_group)
            elif role == 'cashier':
                cashier_group, _ = Group.objects.get_or_create(name='cashier')
                employee.groups.add(cashier_group)
            elif role == 'coach':
                coach_group, _ = Group.objects.get_or_create(name='coach')
                employee.groups.add(coach_group)

            messages.success(request, f"User account created successfully for employee '{employee.name}'.")
            return redirect('/user/?tab=system-users')

        except User.DoesNotExist:
            messages.error(request, "Employee not found.")
        except Exception as e:
            messages.error(request, f"Error creating user account: {str(e)}")

    context = {
        'employees_without_access': employees_without_access,
        'users_with_access': users_with_access,
    }
    return render(request, 'user/user_registration.html', context)

@login_required
@module_permission_required(module='user', required_level='edit')
def edit(request, pk):

    try:
        employee = User.objects.get(id = pk)
    except User.DoesNotExist:
        messages.error(request, "Employee not found")
        return redirect("/user/")

    # Schedule and salary payment functionality removed

    # Handle regular form submission for employee details
    if request.method == "POST" and 'name' in request.POST:
        try:
            name = request.POST.get("name")
            phone = request.POST.get("phone")
            salary = request.POST.get("salary")
            join_date = request.POST.get("join_date")
            address = request.POST.get("address")
            work_schedule = request.POST.get("schedule")
            role = request.POST.get("role")
            password = request.POST.get("password")
            email = request.POST.get("email")

            # Store the old role for comparison
            old_role = employee.role

            employee.name = name
            employee.phone = phone
            employee.salary = salary
            employee.join_date = join_date
            # Combine address and work schedule
            employee.address = f"Address: {address}\n\nWork Schedule: {work_schedule}"
            employee.role = role

            # Update email if provided
            if email:
                employee.email = email

            # Update is_manager flag based on role
            employee.is_manager = (role == 'admin')

            # Determine if this role should have system access
            has_system_access = role in ['admin', 'cashier', 'coach']

            # Update is_staff flag if the employee has system access
            if employee.is_staff:
                employee.is_staff = has_system_access

            if password != "":
                employee.password = make_password(password)

            # Handle photo upload if provided
            if 'photo' in request.FILES:
                photo = request.FILES['photo']
                if photo:
                    # Save the photo in media/employee_photos directory
                    path = f'employee_photos/{employee.id}_{photo.name}'
                    default_storage.save(path, ContentFile(photo.read()))

                    # Store the photo path directly in the employee object
                    employee.photo = path

            employee.save()

            # Update group assignments if the role has changed and the employee has system access
            if old_role != role and employee.is_staff:
                # Clear existing groups
                employee.groups.clear()

                # Add to appropriate group based on new role
                if role == 'admin':
                    admin_group, _ = Group.objects.get_or_create(name='admin')
                    employee.groups.add(admin_group)
                elif role == 'cashier':
                    cashier_group, _ = Group.objects.get_or_create(name='cashier')
                    employee.groups.add(cashier_group)
                elif role == 'coach':
                    coach_group, _ = Group.objects.get_or_create(name='coach')
                    employee.groups.add(coach_group)

                messages.success(request, f"Employee '{name}' updated successfully. Role changed from '{old_role}' to '{role}'.")
            else:
                messages.success(request, f"Employee '{name}' updated successfully.")
        except Exception as e:
            messages.error(request, f"Error updating employee: {str(e)}")

    context = {
        'employee': employee,
        'from_employee_list': True  # Flag to indicate we're editing from the Employees List
    }
    return render(request,"user/edit_empoloyee.html",context)



@login_required
@module_permission_required(module='user', required_level='edit')
def deactivate(request,pk):
    try:
        employee = User.objects.get(id = pk)

        # Protected usernames that cannot be deactivated
        PROTECTED_USERNAMES = ['developer', 'owner']

        # Check if the username is protected
        if employee.username in PROTECTED_USERNAMES:
            messages.error(request, f"User '{employee.name}' (username: {employee.username}) cannot be deactivated as it is a protected system account.")

            # Log the failed attempt
            AdminActionLog.log_action(
                user=request.user,
                action_type='deactivate_user',
                ip_address=get_client_ip(request),
                target_user=employee,
                description=f"Failed attempt to deactivate protected user {employee.username}"
            )

            return redirect("/user/")

        employee.is_active = False
        employee.save()

        # Log the successful deactivation
        AdminActionLog.log_action(
            user=request.user,
            action_type='deactivate_user',
            ip_address=get_client_ip(request),
            target_user=employee,
            description=f"Deactivated user {employee.username}"
        )

        messages.success(request, f"Employee '{employee.name}' deactivated successfully.")
    except Exception as e:
        messages.error(request, f"Error deactivating employee: {str(e)}")

    return redirect("/user/")

# Helper function to get client IP
def get_client_ip(request):
    x_forwarded_for = request.META.get('HTTP_X_FORWARDED_FOR')
    if x_forwarded_for:
        ip = x_forwarded_for.split(',')[0]
    else:
        ip = request.META.get('REMOTE_ADDR')
    return ip

@login_required
@module_permission_required(module='user', required_level='edit')
def active(request,pk):
    try:
        employee = User.objects.get(id = pk)
        employee.is_active = True
        employee.save()

        # Log the activation
        AdminActionLog.log_action(
            user=request.user,
            action_type='activate_user',
            ip_address=get_client_ip(request),
            target_user=employee,
            description=f"Activated user {employee.username}"
        )

        messages.success(request, f"Employee '{employee.name}' activated successfully.")
    except Exception as e:
        messages.error(request, f"Error activating employee: {str(e)}")

    return redirect("/user/")

@login_required
@module_permission_required(module='user', required_level='full')
def delete(request, pk):
    try:
        employee = User.objects.get(id=pk)
        name = employee.name
        username = employee.username  # Store username before deletion

        # Protected usernames that cannot be deleted
        PROTECTED_USERNAMES = ['developer', 'owner']

        # Check if the username is protected
        if employee.username in PROTECTED_USERNAMES:
            messages.error(request, f"User '{name}' (username: {employee.username}) cannot be deleted as it is a protected system account.")

            # Log the failed attempt
            AdminActionLog.log_action(
                user=request.user,
                action_type='delete_user',
                ip_address=get_client_ip(request),
                target_user=employee,
                description=f"Failed attempt to delete protected user {employee.username}"
            )

            return redirect("/user/")

        # First, check if there's a corresponding record in the employee_employee table
        # and delete it if it exists
        with connection.cursor() as cursor:
            # Check if the record exists
            cursor.execute("SELECT id FROM employee_employee WHERE user_id = %s", [pk])
            employee_record = cursor.fetchone()

            if employee_record:
                # Delete the employee record first
                cursor.execute("DELETE FROM employee_employee WHERE user_id = %s", [pk])

        # Log the deletion before actually deleting the user
        AdminActionLog.log_action(
            user=request.user,
            action_type='delete_user',
            ip_address=get_client_ip(request),
            target_user=None,  # Can't reference the user after deletion
            description=f"Deleted user {username} (ID: {pk}, Name: {name})"
        )

        # Now delete the user
        employee.delete()
        messages.success(request, f"Employee '{name}' deleted successfully.")
    except Exception as e:
        messages.error(request, f"Error deleting employee: {str(e)}")

    return redirect("/user/")

# Admin users management - now redirects to index with admin tab
@login_required
@module_permission_required(module='user', required_level='view')
def admin_users(request):
    # Redirect to index page with a query parameter to activate the admin tab
    messages.info(request, "Viewing admin users")
    return redirect('/user/?tab=admins')

# Edit system user (simplified form for users with system access)
@login_required
@module_permission_required(module='user', required_level='edit')
def edit_system_user(request, pk):
    try:
        employee = User.objects.get(id=pk)
    except User.DoesNotExist:
        messages.error(request, "User not found")
        return redirect("user:register")

    # Handle form submission
    if request.method == "POST":
        try:
            name = request.POST.get("name")
            role = request.POST.get("role")
            password = request.POST.get("password")
            email = request.POST.get("email")
            join_date = request.POST.get("join_date")

            # Validate that the role is one that should have system access
            if role not in ['admin', 'cashier', 'coach']:
                messages.error(request, f"Role '{role}' cannot have system access. Please select a valid role.")
                return redirect("user:edit_system_user", pk=pk)

            # If changing from a role that can't have system access to one that can, show a message
            if employee.role not in ['admin', 'cashier', 'coach'] and role in ['cashier', 'coach']:
                messages.success(request, f"Employee role will be changed from '{employee.role}' to '{role}' to grant system access.")

            # Store the old role for comparison
            old_role = employee.role

            # Update employee fields
            employee.name = name
            employee.role = role
            employee.join_date = join_date

            # Update email if provided
            if email:
                employee.email = email

            # Update is_manager flag based on role
            employee.is_manager = (role == 'admin')

            # Update password if provided
            if password != "":
                employee.password = make_password(password)

            # Save the employee
            employee.save()

            # Update group assignments if the role has changed
            if old_role != role:
                # Clear existing groups
                employee.groups.clear()

                # Add to appropriate group based on new role
                if role == 'admin':
                    admin_group, _ = Group.objects.get_or_create(name='admin')
                    employee.groups.add(admin_group)
                elif role == 'cashier':
                    cashier_group, _ = Group.objects.get_or_create(name='cashier')
                    employee.groups.add(cashier_group)
                elif role == 'coach':
                    coach_group, _ = Group.objects.get_or_create(name='coach')
                    employee.groups.add(coach_group)

                messages.success(request, f"System user '{name}' updated successfully. Role changed from '{old_role}' to '{role}'.")
            else:
                messages.success(request, f"System user '{name}' updated successfully.")

            return redirect("user:register")
        except Exception as e:
            messages.error(request, f"Error updating system user: {str(e)}")

    context = {
        'employee': employee,
    }
    return render(request, "user/edit_system_user.html", context)

# Edit admin user (simplified form for admin users)
@login_required
@module_permission_required(module='user', required_level='edit')
def edit_admin_user(request, pk):
    try:
        employee = User.objects.get(id=pk)
        if employee.role != 'admin':
            messages.error(request, "User is not an admin")
            return redirect("user:admin_users")
    except User.DoesNotExist:
        messages.error(request, "User not found")
        return redirect("user:admin_users")

    # Handle form submission
    if request.method == "POST":
        try:
            name = request.POST.get("name")
            email = request.POST.get("email")
            # We're not using create_day from the form since it's auto_now_add
            # and shouldn't be modified directly

            employee.name = name

            # Update email if provided
            if email:
                employee.email = email

            employee.save()

            # Add a success message
            messages.success(request, f"Admin user '{name}' updated successfully.")
            return redirect("user:admin_users")
        except Exception as e:
            messages.error(request, f"Error updating admin user: {str(e)}")

    context = {
        'employee': employee,
    }
    return render(request, "user/edit_admin_user.html", context)

# User profile view
@login_required
def profile(request):
    user = request.user

    # Update user's last activity timestamp
    user.last_activity = timezone.now()
    user.save(update_fields=['last_activity'])

    # Handle personal information form submission
    if request.method == "POST" and request.POST.get("form_type") == "personal_info":
        try:
            name = request.POST.get("name")
            email = request.POST.get("email")
            phone = request.POST.get("phone")
            dob = request.POST.get("dob") or None
            gender = request.POST.get("gender")

            # Update user fields
            user.name = name
            user.email = email
            user.phone = phone
            user.dob = dob
            user.gender = gender

            # Handle photo upload if provided
            if 'photo' in request.FILES:
                photo = request.FILES['photo']
                if photo:
                    # Save the photo in media/employee_photos directory
                    path = f'employee_photos/{user.id}_{photo.name}'
                    default_storage.save(path, ContentFile(photo.read()))

                    # Store the photo path directly in the user object
                    user.photo = path

            user.save()
            messages.success(request, "Personal information updated successfully.")

        except Exception as e:
            messages.error(request, f"Error updating personal information: {str(e)}")

    # Handle additional information form submission
    elif request.method == "POST" and request.POST.get("form_type") == "additional_info":
        try:
            address = request.POST.get("address")
            schedule = request.POST.get("schedule")
            email_notifications = request.POST.get("email_notifications") == "on"
            system_notifications = request.POST.get("system_notifications") == "on"

            # Combine address and work schedule
            user.address = f"Address: {address}\n\nWork Schedule: {schedule}"
            user.receive_email_notifications = email_notifications
            user.receive_system_notifications = system_notifications

            user.save()
            messages.success(request, "Additional information updated successfully.")

        except Exception as e:
            messages.error(request, f"Error updating additional information: {str(e)}")

    # For demonstration purposes, we'll create some dummy activity data
    # In a real application, you would fetch this from a proper activity log model
    activities = [
        {
            'timestamp': timezone.now() - timezone.timedelta(days=1),
            'action': 'Login',
            'details': 'Logged in from ***********'
        },
        {
            'timestamp': timezone.now() - timezone.timedelta(days=2),
            'action': 'Profile Update',
            'details': 'Updated personal information'
        },
        {
            'timestamp': timezone.now() - timezone.timedelta(days=5),
            'action': 'Password Change',
            'details': 'Changed account password'
        }
    ]

    context = {
        'user': user,
        'activities': activities
    }

    return render(request, 'user/profile.html', context)

# Change password view
@login_required
def change_password(request):
    user = request.user

    if request.method == "POST":
        current_password = request.POST.get("current_password")
        new_password = request.POST.get("new_password")
        confirm_password = request.POST.get("confirm_password")

        # Validate passwords
        if not check_password(current_password, user.password):
            messages.error(request, "Current password is incorrect.")
            return redirect('user:change_password')

        if new_password != confirm_password:
            messages.error(request, "New passwords do not match.")
            return redirect('user:change_password')

        if len(new_password) < 8:
            messages.error(request, "Password must be at least 8 characters long.")
            return redirect('user:change_password')

        # Update password
        user.password = make_password(new_password)
        user.save()

        # Update last activity
        user.last_activity = timezone.now()
        user.save(update_fields=['last_activity'])

        messages.success(request, "Password changed successfully.")
        return redirect('user:profile')

    return render(request, 'user/change_password.html')

# Admin Action Logs view
@login_required
@module_permission_required(module='user', required_level='full')
def admin_action_logs(request):
    """View for displaying admin action logs"""
    # Get all logs, ordered by most recent first
    logs = AdminActionLog.objects.all().order_by('-action_time')

    # Filter by action type if specified
    action_type = request.GET.get('action_type')
    if action_type:
        logs = logs.filter(action_type=action_type)

    # Filter by user if specified
    user_id = request.GET.get('user_id')
    if user_id:
        logs = logs.filter(user_id=user_id)

    # Filter by target user if specified
    target_user_id = request.GET.get('target_user_id')
    if target_user_id:
        logs = logs.filter(target_user_id=target_user_id)

    # Get all admin users for the filter dropdown
    admin_users = User.objects.filter(role='admin')

    context = {
        'logs': logs,
        'admin_users': admin_users,
        'action_types': AdminActionLog.ACTION_TYPES,
        'selected_action_type': action_type,
        'selected_user_id': user_id,
        'selected_target_user_id': target_user_id,
    }

    return render(request, 'user/admin_action_logs_fixed.html', context)

# Removed features as per requirements
# - Bulk User Import/Export
# - Custom Role Management


# ============================================================================
# COMPREHENSIVE USER ACTION LOGGING SYSTEM
# ============================================================================

@login_required
@module_permission_required(module='user', required_level='view')
def action_logs(request):
    """
    Comprehensive Action Logs page for Admin users to monitor all user activities
    """
    # Only admin users can access action logs
    if request.user.role != 'admin':
        messages.error(request, "Access denied. Only Admin users can view action logs.")
        return redirect('core:index')

    # Get filter parameters
    user_filter = request.GET.get('user', '')
    action_type_filter = request.GET.get('action_type', '')
    module_filter = request.GET.get('module', '')
    status_filter = request.GET.get('status', '')
    start_date = request.GET.get('start_date', '')
    end_date = request.GET.get('end_date', '')
    search_query = request.GET.get('search', '')

    # Base queryset
    logs = UserActionLog.objects.all()

    # Apply filters
    if user_filter:
        logs = logs.filter(user_id=user_filter)

    if action_type_filter:
        logs = logs.filter(action_type=action_type_filter)

    if module_filter:
        logs = logs.filter(module=module_filter)

    if status_filter:
        logs = logs.filter(status=status_filter)

    if start_date:
        try:
            start_date_obj = datetime.strptime(start_date, '%Y-%m-%d').date()
            logs = logs.filter(action_time__date__gte=start_date_obj)
        except ValueError:
            pass

    if end_date:
        try:
            end_date_obj = datetime.strptime(end_date, '%Y-%m-%d').date()
            logs = logs.filter(action_time__date__lte=end_date_obj)
        except ValueError:
            pass

    if search_query:
        logs = logs.filter(
            models.Q(target_description__icontains=search_query) |
            models.Q(description__icontains=search_query) |
            models.Q(target_id__icontains=search_query) |
            models.Q(ip_address__icontains=search_query)
        )

    # Order by action time (newest first)
    logs = logs.order_by('-action_time')

    # Pagination
    from django.core.paginator import Paginator
    paginator = Paginator(logs, 50)  # Show 50 logs per page
    page_number = request.GET.get('page')
    page_obj = paginator.get_page(page_number)

    # Get filter options
    users = User.objects.filter(action_logs__isnull=False).distinct()
    action_types = UserActionLog.ACTION_TYPES
    modules = UserActionLog.MODULE_CHOICES
    statuses = UserActionLog.STATUS_CHOICES

    # Get suspicious activity alerts
    suspicious_logs = []
    for log in page_obj:
        if log.is_suspicious():
            suspicious_logs.append(log)

    context = {
        'page_obj': page_obj,
        'users': users,
        'action_types': action_types,
        'modules': modules,
        'statuses': statuses,
        'suspicious_logs': suspicious_logs,
        'filters': {
            'user': user_filter,
            'action_type': action_type_filter,
            'module': module_filter,
            'status': status_filter,
            'start_date': start_date,
            'end_date': end_date,
            'search': search_query,
        }
    }

    return render(request, 'user/action_logs.html', context)


@login_required
@module_permission_required(module='user', required_level='view')
def action_log_detail(request, pk):
    """
    Detailed view of a specific action log entry
    """
    # Only admin users can access action logs
    if request.user.role != 'admin':
        messages.error(request, "Access denied. Only Admin users can view action logs.")
        return redirect('core:index')

    log = get_object_or_404(UserActionLog, pk=pk)

    # Get related logs from the same user around the same time
    related_logs = UserActionLog.objects.filter(
        user=log.user,
        action_time__gte=log.action_time - timezone.timedelta(hours=1),
        action_time__lte=log.action_time + timezone.timedelta(hours=1)
    ).exclude(pk=pk).order_by('-action_time')[:10]

    # Check for suspicious indicators
    suspicious_indicators = log.is_suspicious()

    # Get user risk score
    risk_score = SuspiciousActivityDetector.get_user_risk_score(log.user) if log.user else 0

    context = {
        'log': log,
        'related_logs': related_logs,
        'suspicious_indicators': suspicious_indicators,
        'risk_score': risk_score,
    }

    return render(request, 'user/action_log_detail.html', context)


@login_required
@module_permission_required(module='user', required_level='view')
def export_action_logs(request):
    """
    Export action logs to CSV for audit purposes
    """
    # Only admin users can export action logs
    if request.user.role != 'admin':
        messages.error(request, "Access denied. Only Admin users can export action logs.")
        return redirect('core:index')

    import csv
    from django.http import HttpResponse

    # Log the export action
    log_user_action(
        user=request.user,
        action_type='export_data',
        module='user',
        request=request,
        description="Action logs export to CSV",
        additional_data={'export_type': 'action_logs_csv'}
    )

    # Create the HttpResponse object with CSV header
    response = HttpResponse(content_type='text/csv')
    response['Content-Disposition'] = f'attachment; filename="action_logs_{timezone.now().strftime("%Y%m%d_%H%M%S")}.csv"'

    writer = csv.writer(response)

    # Write header
    writer.writerow([
        'ID', 'User', 'Action Type', 'Module', 'Action Time', 'Status',
        'Target Model', 'Target ID', 'Target Description', 'IP Address',
        'Financial Impact', 'Description'
    ])

    # Apply same filters as the main view
    logs = UserActionLog.objects.all()

    # Apply filters from request
    user_filter = request.GET.get('user', '')
    action_type_filter = request.GET.get('action_type', '')
    module_filter = request.GET.get('module', '')
    status_filter = request.GET.get('status', '')
    start_date = request.GET.get('start_date', '')
    end_date = request.GET.get('end_date', '')

    if user_filter:
        logs = logs.filter(user_id=user_filter)
    if action_type_filter:
        logs = logs.filter(action_type=action_type_filter)
    if module_filter:
        logs = logs.filter(module=module_filter)
    if status_filter:
        logs = logs.filter(status=status_filter)
    if start_date:
        try:
            start_date_obj = datetime.strptime(start_date, '%Y-%m-%d').date()
            logs = logs.filter(action_time__date__gte=start_date_obj)
        except ValueError:
            pass
    if end_date:
        try:
            end_date_obj = datetime.strptime(end_date, '%Y-%m-%d').date()
            logs = logs.filter(action_time__date__lte=end_date_obj)
        except ValueError:
            pass

    # Write data rows
    for log in logs.order_by('-action_time'):
        writer.writerow([
            log.id,
            log.user.username if log.user else 'Unknown',
            log.get_action_type_display(),
            log.get_module_display(),
            log.action_time.strftime('%Y-%m-%d %H:%M:%S'),
            log.get_status_display(),
            log.target_model,
            log.target_id,
            log.target_description,
            log.ip_address,
            log.get_financial_impact_display(),
            log.description
        ])

    return response


@login_required
@module_permission_required(module='user', required_level='view')
def security_dashboard(request):
    """
    Security dashboard showing suspicious activities and user risk scores
    """
    # Only admin users can access security dashboard
    if request.user.role != 'admin':
        messages.error(request, "Access denied. Only Admin users can view security dashboard.")
        return redirect('core:index')

    # Get recent suspicious activities
    recent_logs = UserActionLog.objects.filter(
        action_time__gte=timezone.now() - timezone.timedelta(days=7)
    ).order_by('-action_time')

    suspicious_activities = []
    for log in recent_logs:
        indicators = log.is_suspicious()
        if indicators:
            suspicious_activities.append({
                'log': log,
                'indicators': indicators
            })

    # Get users with high risk scores
    high_risk_users = []
    for user in User.objects.filter(is_active=True):
        risk_score = SuspiciousActivityDetector.get_user_risk_score(user)
        if risk_score > 30:  # High risk threshold
            high_risk_users.append({
                'user': user,
                'risk_score': risk_score
            })

    # Sort by risk score
    high_risk_users.sort(key=lambda x: x['risk_score'], reverse=True)

    # Get activity statistics
    today = timezone.now().date()
    stats = {
        'total_actions_today': UserActionLog.objects.filter(action_time__date=today).count(),
        'delete_actions_today': UserActionLog.objects.filter(
            action_time__date=today,
            action_type__contains='delete'
        ).count(),
        'failed_logins_today': UserActionLog.objects.filter(
            action_time__date=today,
            action_type='failed_login'
        ).count(),
        'unique_users_today': UserActionLog.objects.filter(
            action_time__date=today
        ).values('user').distinct().count(),
    }

    context = {
        'suspicious_activities': suspicious_activities[:20],  # Show top 20
        'high_risk_users': high_risk_users[:10],  # Show top 10
        'stats': stats,
    }

    return render(request, 'user/security_dashboard.html', context)
