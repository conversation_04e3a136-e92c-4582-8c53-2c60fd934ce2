import sys
import os
import re

def add_translation_tags(file_path):
    """Add translation tags to a template file."""
    with open(file_path, 'r', encoding='utf-8', errors='ignore') as f:
        content = f.read()

    # Make sure the file has {% load i18n %}
    if not re.search(r'{%\s*load\s+i18n\s*%}', content):
        print(f"Adding '{{% load i18n %}}' tag to {file_path}")

        # Find the first {% load ... %} tag
        load_match = re.search(r'{%\s*load\s+[^%]+%}', content)

        if load_match:
            # Add {% load i18n %} after the existing load tag
            end_pos = load_match.end()
            content = content[:end_pos] + '\n{% load i18n %}' + content[end_pos:]
        else:
            # If no load tag exists, add it after the first line (usually DOCTYPE or html tag)
            lines = content.split('\n', 1)
            if len(lines) > 1:
                content = lines[0] + '\n{% load i18n %}\n' + lines[1]
            else:
                content = '{% load i18n %}\n' + content

    # Add translation tags to common HTML elements

    # Headings
    for tag in ['h1', 'h2', 'h3', 'h4', 'h5', 'h6']:
        pattern = f'<{tag}([^>]*)>(.*?)</{tag}>'
        content = re.sub(pattern,
                        lambda m: f'<{tag}{m.group(1)}>{{% trans "{m.group(2).strip()}" %}}</{tag}>'
                        if m.group(2).strip() and not '{%' in m.group(2) else m.group(0),
                        content, flags=re.DOTALL)

    # Labels
    content = re.sub(r'<label([^>]*)>(.*?)</label>',
                    lambda m: f'<label{m.group(1)}>{{% trans "{m.group(2).strip()}" %}}</label>'
                    if m.group(2).strip() and not '{%' in m.group(2) else m.group(0),
                    content, flags=re.DOTALL)

    # Buttons
    content = re.sub(r'<button([^>]*)>(.*?)</button>',
                    lambda m: f'<button{m.group(1)}>{{% trans "{m.group(2).strip()}" %}}</button>'
                    if m.group(2).strip() and not '{%' in m.group(2) else m.group(0),
                    content, flags=re.DOTALL)

    # Spans
    content = re.sub(r'<span([^>]*)>(.*?)</span>',
                    lambda m: f'<span{m.group(1)}>{{% trans "{m.group(2).strip()}" %}}</span>'
                    if m.group(2).strip() and not '{%' in m.group(2) and not '{{' in m.group(2) else m.group(0),
                    content, flags=re.DOTALL)

    # Paragraphs
    content = re.sub(r'<p([^>]*)>(.*?)</p>',
                    lambda m: f'<p{m.group(1)}>{{% trans "{m.group(2).strip()}" %}}</p>'
                    if m.group(2).strip() and not '{%' in m.group(2) and not '{{' in m.group(2) else m.group(0),
                    content, flags=re.DOTALL)

    # Table headers
    content = re.sub(r'<th([^>]*)>(.*?)</th>',
                    lambda m: f'<th{m.group(1)}>{{% trans "{m.group(2).strip()}" %}}</th>'
                    if m.group(2).strip() and not '{%' in m.group(2) else m.group(0),
                    content, flags=re.DOTALL)

    # Options
    content = re.sub(r'<option([^>]*)>(.*?)</option>',
                    lambda m: f'<option{m.group(1)}>{{% trans "{m.group(2).strip()}" %}}</option>'
                    if m.group(2).strip() and not '{%' in m.group(2) else m.group(0),
                    content, flags=re.DOTALL)

    # Links
    content = re.sub(r'<a([^>]*)>(.*?)</a>',
                    lambda m: f'<a{m.group(1)}>{{% trans "{m.group(2).strip()}" %}}</a>'
                    if m.group(2).strip() and not '{%' in m.group(2) and not '{{' in m.group(2) and not '<i' in m.group(2) else m.group(0),
                    content, flags=re.DOTALL)

    # Attributes
    for attr in ['placeholder', 'title', 'alt']:
        content = re.sub(f'{attr}="([^"]*)"',
                        lambda m: f'{attr}="{{% trans "{m.group(1)}" %}}"'
                        if m.group(1).strip() and not '{%' in m.group(1) else m.group(0),
                        content)

    # Write the modified content back to the file
    with open(file_path, 'w', encoding='utf-8') as f:
        f.write(content)

    print(f"Added translation tags to {file_path}")

if __name__ == "__main__":
    if len(sys.argv) < 2:
        print("Usage: python translate_template.py <template_path>")
        sys.exit(1)

    template_path = sys.argv[1]

    if not os.path.exists(template_path):
        print(f"Error: File {template_path} does not exist")
        sys.exit(1)

    add_translation_tags(template_path)
