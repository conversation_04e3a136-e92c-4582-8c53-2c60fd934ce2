/* Breadcrumbs Styles */

.breadcrumbs-container {
    background-color: #f9fafb;
    padding: 0.75rem 1.5rem;
    border-radius: 0.5rem;
    margin-bottom: 1.5rem;
    box-shadow: 0 1px 2px rgba(0, 0, 0, 0.05);
    transition: all 0.3s ease;
}

.breadcrumbs-container:hover {
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.breadcrumbs-list {
    display: flex;
    flex-wrap: wrap;
    align-items: center;
    list-style: none;
    padding: 0;
    margin: 0;
}

.breadcrumbs-item {
    display: flex;
    align-items: center;
    color: #6b7280;
    font-size: 0.875rem;
}

.breadcrumbs-item:not(:last-child)::after {
    content: '/';
    margin: 0 0.5rem;
    color: #d1d5db;
}

.breadcrumbs-link {
    display: flex;
    align-items: center;
    color: #4b5563;
    text-decoration: none;
    transition: color 0.2s ease;
}

.breadcrumbs-link:hover {
    color: #1e40af;
    text-decoration: underline;
}

.breadcrumbs-current {
    display: flex;
    align-items: center;
    color: #1e40af;
    font-weight: 500;
}

.breadcrumbs-link i,
.breadcrumbs-current i {
    margin-right: 0.375rem;
    font-size: 0.875rem;
}

/* Responsive styles */
@media (max-width: 640px) {
    .breadcrumbs-container {
        padding: 0.5rem 1rem;
        margin-bottom: 1rem;
    }
    
    .breadcrumbs-text {
        display: none;
    }
    
    .breadcrumbs-item:last-child .breadcrumbs-text {
        display: inline;
    }
    
    .breadcrumbs-link i,
    .breadcrumbs-current i {
        margin-right: 0;
        font-size: 1rem;
    }
}

/* Dark mode styles */
.dark-mode .breadcrumbs-container {
    background-color: #1f2937;
    box-shadow: 0 1px 2px rgba(0, 0, 0, 0.2);
}

.dark-mode .breadcrumbs-item {
    color: #9ca3af;
}

.dark-mode .breadcrumbs-item:not(:last-child)::after {
    color: #4b5563;
}

.dark-mode .breadcrumbs-link {
    color: #d1d5db;
}

.dark-mode .breadcrumbs-link:hover {
    color: #60a5fa;
}

.dark-mode .breadcrumbs-current {
    color: #60a5fa;
}
