{% extends 'base.html' %}
{% load custom_filters %}
{% load currency_filters %}
{% load permission_tags %}
{% load static %}



{% block body %}
<!-- Check permission for delete functionality -->
{% has_permission user 'payment' 'full' as can_delete_payment %}

<div class="conponentSection p-5 bg-gray-200">
    <div class="componentWrapper">
        <!-- Header with Add Button -->
        <div class="flex justify-between items-center mb-4">
            <h3 class="text-2xl font-bold">Member Payments</h3>
            <div class="flex space-x-2">
                <a href="{% url 'payment:create_payment' %}" class="bg-blue-900 hover:bg-blue-800 text-white font-bold py-2 px-4 rounded transition duration-200">
                    <i class="fas fa-plus mr-2"></i>  Payment
                </a>
            </div>
        </div>

        <!-- Member Payments List -->
        <div class="listSection bg-white p-4 rounded shadow-md">
            <div class="flex justify-between items-center mb-4">
                <h3 class="text-xl font-semibold">Payment History</h3>
                <div class="text-right">
                    <p class="font-bold">Total: {{ total_khr|format_khr }}</p>
                </div>
            </div>

            <!-- Filter Form -->
            <form method="get" class="mb-4 grid grid-cols-3 gap-4">
                <div>
                    <label class="block text-sm font-medium mb-1">Member</label>
                    <select name="member" class="border w-full p-2 leading-tight bg-slate-100">
                        <option value="">All Members</option>
                        {% for member in members %}
                        <option value="{{ member.id }}" {% if member_filter == member.id|stringformat:"s" %}selected{% endif %}>{{ member.name }}</option>
                        {% endfor %}
                    </select>
                </div>
                <div>
                    <label class="block text-sm font-medium mb-1">From Date</label>
                    <input type="date" name="date_from" class="border w-full p-2 leading-tight bg-slate-100"
                           value="{{ date_from }}">
                </div>
                <div>
                    <label class="block text-sm font-medium mb-1">To Date</label>
                    <input type="date" name="date_to" class="border w-full p-2 leading-tight bg-slate-100"
                           value="{{ date_to }}">
                </div>
                <div>
                    <label class="block text-sm font-medium mb-1">Payment Method</label>
                    <select name="payment_method" class="border w-full p-2 leading-tight bg-slate-100">
                        <option value="">All Methods</option>
                        {% for value, label in payment_methods %}
                        <option value="{{ value }}" {% if payment_method == value %}selected{% endif %}>{{ label }}</option>
                        {% endfor %}
                    </select>
                </div>
                <div class="col-span-2 flex items-end">
                    <button type="submit" class="bg-blue-900 text-white font-bold py-2 px-4">Filter</button>
                    <a href="{% url 'payment:index' %}" class="bg-gray-500 text-white font-bold py-2 px-4 ml-2">Reset</a>
                </div>
            </form>

            <!-- Bulk Actions Form -->
            <form id="bulk-actions-form" method="post" action="{% url 'payment:bulk_actions' %}">
                {% csrf_token %}
                <div class="flex items-center space-x-2 mb-4">
                    <select name="bulk_action" class="border p-2 rounded">
                        <option value="">-- Select Bulk Action --</option>
                        <option value="print">Print Selected Receipts</option>
                        <option value="delete">Delete Selected Payments</option>
                    </select>
                    <button type="submit" class="bg-blue-900 text-white px-4 py-2 rounded">Apply</button>
                    <button type="button" id="select-all" class="bg-gray-500 text-white px-4 py-2 rounded">Select All</button>
                    <button type="button" id="deselect-all" class="bg-gray-500 text-white px-4 py-2 rounded">Deselect All</button>
                </div>

                <div class="relative overflow-x-auto">
                    <table class="w-full text-sm text-left">
                        <thead class="text-sm uppercase bg-blue-900 text-gray-50"><tr>
                                <th scope="col" class="px-2 py-3 w-10">
                                    <input type="checkbox" id="select-all-checkbox" class="w-4 h-4"></th>
                                <th scope="col" class="px-6 py-3">Invoice No.</th>
                                <th scope="col" class="px-6 py-3">Member</th>
                                <th scope="col" class="px-6 py-3">Amount (KHR)</th>
                                <th scope="col" class="px-6 py-3" style="display: none;">Amount (USD)</th>
                                <th scope="col" class="px-6 py-3">Method</th>
                                <th scope="col" class="px-6 py-3">Date</th>
                                <th scope="col" class="px-6 py-3">Collector</th>
                                <th scope="col" class="px-6 py-3">Actions</th>
                            </tr>
                        </thead>
                        <tbody>
                            {% for payment in payments %}
                            <tr class="bg-white border">
                                <td class="px-2 py-4">
                                    <input type="checkbox" name="selected_payments" value="{{ payment.id }}" class="payment-checkbox w-4 h-4">
                                </td>
                                <td class="px-6 py-4">{{ payment.invoice_no }}</td>
                                <td class="px-6 py-4">{{ payment.member.name }}</td>
                                <td class="px-6 py-4">{{ payment.amount_khr|format_khr }}</td>
                                <td class="px-6 py-4" style="display: none;">{% if payment.amount_usd %}{{ payment.amount_usd|format_usd }}{% else %}-{% endif %}</td>
                                <td class="px-6 py-4">{{ payment.get_payment_method_display }}</td>
                                <td class="px-6 py-4">{{ payment.payment_date|date:"d-M-Y H:i" }}</td>
                                <td class="px-6 py-4">{{ payment.collector.username }}</td>
                                <td class="px-6 py-4">
                                    <div class="flex flex-col sm:flex-row gap-2">
                                        <a href="{% url 'payment:print_receipt' payment.id %}"
                                           class="text-blue-600 hover:underline text-sm">
                                            Print Receipt
                                        </a>
                                        {% if can_delete_payment %}
                                        <button type="button"
                                                class="delete-payment-btn text-red-600 hover:text-red-800 text-sm font-medium transition duration-200"
                                                data-payment-id="{{ payment.id }}"
                                                data-invoice-no="{{ payment.invoice_no }}"
                                                data-amount="{{ payment.amount_khr|format_khr }}"
                                                data-member="{{ payment.member.name }}"
                                                data-payment-method="{{ payment.get_payment_method_display }}">
                                            <i class="fas fa-trash mr-1"></i>Delete
                                        </button>
                                        {% endif %}
                                    </div>
                                </td>
                            </tr>
                            {% empty %}
                            <tr class="bg-white border">
                                <td colspan="9" class="px-6 py-4 text-center">No payments found matching the filter criteria.</td>
                            </tr>
                            {% endfor %}
                        </tbody>
                    </table>
                </div>
            </form>
        </div>
    </div>
</div>
{% endblock %}

{% block js %}
<script src="{% static 'js/currency-formatter.js' %}"></script>
<script>
    document.addEventListener('DOMContentLoaded', function() {
        // Member payment form functionality
        const memberSelect = document.getElementById('member-select');
        const amountInput = document.getElementById('amount-khr');

        if (memberSelect && amountInput) {
            memberSelect.addEventListener('change', function() {
                const selectedOption = memberSelect.options[memberSelect.selectedIndex];

                if (selectedOption.value) {
                    // Get the data attributes from the selected option
                    const packagePrice = parseInt(selectedOption.getAttribute('data-package-price')) || 0;
                    const dueAmount = parseInt(selectedOption.getAttribute('data-due')) || 0;
                    const paymentStatus = selectedOption.getAttribute('data-payment-status');

                    // Remove any existing help text
                    const existingHelpText = document.getElementById('due-help-text');
                    if (existingHelpText) {
                        existingHelpText.remove();
                    }

                    // Remove any existing warning
                    const existingWarning = document.getElementById('payment-warning');
                    if (existingWarning) {
                        existingWarning.remove();
                    }

                    // Make sure the submit button is enabled by default
                    // (it will be disabled only for members who have already paid in full)
                    const submitButton = document.querySelector('button[type="submit"]');
                    submitButton.disabled = false;
                    submitButton.classList.remove('opacity-50', 'cursor-not-allowed');

                    // Set the amount input value based on due amount and payment status
                    // The due_payment field can be 0 for two reasons:
                    // 1. The member has already paid in full
                    // 2. The member is new and hasn't made any payments yet

                    // Check if the due amount is greater than 0 (member has a pending payment)
                    if (dueAmount > 0) {
                        // Member has a due amount
                        amountInput.value = dueAmount;

                        // Add a helpful message below the input
                        const helpText = document.createElement('p');
                        helpText.className = 'text-xs text-red-500 mt-1';
                        helpText.id = 'due-help-text';
                        helpText.textContent = `Due amount: ${formatKHR(dueAmount)} of ${formatKHR(packagePrice)} total`;
                        amountInput.parentNode.appendChild(helpText);

                        // Ensure the submit button is enabled
                        const submitButton = document.querySelector('button[type="submit"]');
                        submitButton.disabled = false;
                        submitButton.classList.remove('opacity-50', 'cursor-not-allowed');
                    }
                    // Check if the due amount is exactly 0 and the package price is also 0 (invalid state)
                    else if (dueAmount === 0 && packagePrice === 0) {
                        // This is an error state - member has no package assigned
                        const helpText = document.createElement('p');
                        helpText.className = 'text-xs text-red-500 mt-1';
                        helpText.id = 'due-help-text';
                        helpText.textContent = `Error: Member has no package assigned`;
                        amountInput.parentNode.appendChild(helpText);
                        amountInput.value = 0;
                    }
                    // Check if the due amount is exactly equal to the package price (member hasn't paid anything)
                    else if (dueAmount === packagePrice) {
                        // Member hasn't made any payments yet
                        amountInput.value = packagePrice;

                        // Add a helpful message below the input
                        const helpText = document.createElement('p');
                        helpText.className = 'text-xs text-blue-500 mt-1';
                        helpText.id = 'due-help-text';
                        helpText.textContent = `First payment: ${formatKHR(packagePrice)}`;
                        amountInput.parentNode.appendChild(helpText);

                        // Ensure the submit button is enabled
                        const submitButton = document.querySelector('button[type="submit"]');
                        submitButton.disabled = false;
                        submitButton.classList.remove('opacity-50', 'cursor-not-allowed');
                    }
                    // Check if the member has already paid in full (due amount is 0, payment status is 'paid', and package price is not 0)
                    else if (dueAmount === 0 && paymentStatus === 'paid' && packagePrice > 0) {
                        // Member has already paid in full
                        // Show a warning and disable the submit button by default
                        const warningDiv = document.createElement('div');
                        warningDiv.className = 'col-span-3 bg-yellow-100 border-l-4 border-yellow-500 text-yellow-700 p-4 mb-4';
                        warningDiv.id = 'payment-warning';
                        warningDiv.innerHTML = `
                            <p class="font-bold">Warning: Member has already paid in full</p>
                            <p>This member has no outstanding dues. Additional payments will automatically extend their membership.</p>

                            <div class="mt-3">
                                <p class="font-medium">Payment will be used to:</p>
                                <ul class="list-disc ml-5 mt-1">
                                    <li>Extend membership duration proportionally to payment amount</li>
                                    <li>For example, paying 50% of package price extends membership by 50% of package duration</li>
                                </ul>
                            </div>

                            <div class="mt-3">
                                <label class="inline-flex items-center"><input type="checkbox" id="confirm-additional-payment" class="form-checkbox h-5 w-5 text-blue-600">
                                    <span class="ml-2">I confirm this is an intentional additional payment to extend membership</span>"</label>
                            </div>
                        `;

                        // Insert the warning before the form
                        const formElement = document.querySelector('form');
                        formElement.insertBefore(warningDiv, formElement.firstChild);

                        // Disable the submit button
                        const submitButton = document.querySelector('button[type="submit"]');
                        submitButton.disabled = true;
                        submitButton.classList.add('opacity-50', 'cursor-not-allowed');

                        // Add event listener to the checkbox
                        setTimeout(() => {
                            const checkbox = document.getElementById('confirm-additional-payment');
                            if (checkbox) {
                                checkbox.addEventListener('change', function() {
                                    submitButton.disabled = !this.checked;
                                    if (this.checked) {
                                        submitButton.classList.remove('opacity-50', 'cursor-not-allowed');
                                    } else {
                                        submitButton.classList.add('opacity-50', 'cursor-not-allowed');
                                    }
                                });
                            }
                        }, 100);

                        // Still set a default amount
                        amountInput.value = packagePrice;
                    }
                    // Default case - handle any other scenarios
                    else {
                        // Set the amount to the package price
                        amountInput.value = packagePrice;

                        // Add a helpful message
                        const helpText = document.createElement('p');
                        helpText.className = 'text-xs text-blue-500 mt-1';
                        helpText.id = 'due-help-text';
                        helpText.textContent = `Payment amount: ${formatKHR(packagePrice)}`;
                        amountInput.parentNode.appendChild(helpText);

                        // Ensure the submit button is enabled
                        const submitButton = document.querySelector('button[type="submit"]');
                        submitButton.disabled = false;
                        submitButton.classList.remove('opacity-50', 'cursor-not-allowed');
                    }
                } else {
                    // Clear the amount if no member is selected
                    amountInput.value = '';
                }
            });
        }

        // Bulk actions checkbox functionality
        const selectAllBtn = document.getElementById('select-all');
        const deselectAllBtn = document.getElementById('deselect-all');
        const selectAllCheckbox = document.getElementById('select-all-checkbox');
        const paymentCheckboxes = document.querySelectorAll('.payment-checkbox');

        // Select all button
        if (selectAllBtn) {
            selectAllBtn.addEventListener('click', function() {
                paymentCheckboxes.forEach(checkbox => {
                    checkbox.checked = true;
                });
                if (selectAllCheckbox) {
                    selectAllCheckbox.checked = true;
                }
            });
        }

        // Deselect all button
        if (deselectAllBtn) {
            deselectAllBtn.addEventListener('click', function() {
                paymentCheckboxes.forEach(checkbox => {
                    checkbox.checked = false;
                });
                if (selectAllCheckbox) {
                    selectAllCheckbox.checked = false;
                }
            });
        }

        // Select all checkbox in table header
        if (selectAllCheckbox) {
            selectAllCheckbox.addEventListener('change', function() {
                paymentCheckboxes.forEach(checkbox => {
                    checkbox.checked = selectAllCheckbox.checked;
                });
            });
        }

        // Form validation before submission
        const bulkActionsForm = document.getElementById('bulk-actions-form');
        if (bulkActionsForm) {
            bulkActionsForm.addEventListener('submit', function(e) {
                const action = bulkActionsForm.querySelector('select[name="bulk_action"]').value;
                const checkedBoxes = bulkActionsForm.querySelectorAll('input[name="selected_payments"]:checked');

                if (!action) {
                    e.preventDefault();
                    alert('Please select an action to perform.');
                    return false;
                }

                if (checkedBoxes.length === 0) {
                    e.preventDefault();
                    alert('Please select at least one payment record.');
                    return false;
                }

                if (action === 'delete' && !confirm('Are you sure you want to delete the selected payment records? This action cannot be undone.')) {
                    e.preventDefault();
                    return false;
                }

                return true;
            });
        }

        // Delete payment functionality with confirmation
        document.querySelectorAll('.delete-payment-btn').forEach(button => {
            button.addEventListener('click', function() {
                const paymentId = this.getAttribute('data-payment-id');
                const invoiceNo = this.getAttribute('data-invoice-no');
                const amount = this.getAttribute('data-amount');
                const member = this.getAttribute('data-member');
                const paymentMethod = this.getAttribute('data-payment-method');

                // Create confirmation dialog
                const confirmDialog = confirm(
                    `⚠️ DELETE PAYMENT CONFIRMATION ⚠️\n\n` +
                    `Payment Details:\n` +
                    `• Invoice: ${invoiceNo}\n` +
                    `• Member: ${member}\n` +
                    `• Amount: ${amount}\n` +
                    `• Method: ${paymentMethod}\n\n` +
                    `⚠️ FINANCIAL WARNING ⚠️\n` +
                    `This will deduct ${amount} from gym funds.\n\n` +
                    `This action cannot be undone!\n\n` +
                    `Are you sure you want to delete this payment?`
                );

                if (confirmDialog) {
                    // Create a form and submit it
                    const form = document.createElement('form');
                    form.method = 'POST';
                    form.action = `/payment/delete/${paymentId}/`;

                    // Add CSRF token
                    const csrfToken = document.querySelector('[name=csrfmiddlewaretoken]').value;
                    const csrfInput = document.createElement('input');
                    csrfInput.type = 'hidden';
                    csrfInput.name = 'csrfmiddlewaretoken';
                    csrfInput.value = csrfToken;
                    form.appendChild(csrfInput);

                    // Add to body and submit
                    document.body.appendChild(form);
                    form.submit();
                }
            });
        });
    });
</script>
{% endblock %}
