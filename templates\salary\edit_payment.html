{% extends 'base.html' %}
{% load custom_filters %}



{% block body %}
<div class="conponentSection p-5 bg-gray-200">
    <div class="componentWrapper">
        <!-- Header with navigation -->
        <div class="flex justify-between items-center mb-4">
            <h3 class="text-2xl font-bold">Edit Salary Payment</h3>
            <div class="flex space-x-2">
                <a href="{% url 'salary:index' %}" class="bg-blue-900 text-white px-4 py-2 rounded">
                    <i class="fa-solid fa-arrow-left mr-2"></i>Back to Salary Payments
                </a>
            </div>
        </div>

        <!-- Form starts  -->
        <div class="formSection bg-white p-6 rounded shadow-md mb-4">
            <form method="post">
                {% csrf_token %}
                <div class="grid grid-cols-3 gap-4">
                    <!-- Employee Information (Read-only) -->
                    <div>
                        <label class="block text-sm font-medium mb-1">Employee</label>
                        <input type="text" class="border w-full p-4 leading-tight bg-slate-200"
                               value="{{ payment.employee.name }}" disabled>
                    </div>

                    <!-- Month & Year (Read-only) -->
                    <div>
                        <label class="block text-sm font-medium mb-1">Month & Year</label>
                        <input type="text" class="border w-full p-4 leading-tight bg-slate-200"
                               value="{{ payment.month|date:'F Y' }}" disabled>
                    </div>

                    <!-- Employment Type -->
                    <div>
                        <label class="block text-sm font-medium mb-1">Employment Type*</label>
                        <select name="employment_type" class="border w-full p-4 leading-tight bg-slate-100" required>
                            <option value="full_time" {% if payment.employment_type == 'full_time' %}selected{% endif %}>Full-time</option>
                            <option value="part_time" {% if payment.employment_type == 'part_time' %}selected{% endif %}>Part-time</option>
                        </select>
                    </div>

                    <!-- Base Salary -->
                    <div>
                        <label class="block text-sm font-medium mb-1">Base Salary (៛)*</label>
                        <input type="number" name="base_salary" class="border w-full p-4 leading-tight bg-slate-100"
                               value="{{ payment.base_salary }}" required>
                    </div>

                    <!-- Bonus -->
                    <div>
                        <label class="block text-sm font-medium mb-1">Bonus (៛)</label>
                        <input type="number" name="bonus" class="border w-full p-4 leading-tight bg-slate-100"
                               value="{{ payment.bonus }}">
                    </div>

                    <!-- Deduction -->
                    <div>
                        <label class="block text-sm font-medium mb-1">Deduction (៛)</label>
                        <input type="number" name="deduction" class="border w-full p-4 leading-tight bg-slate-100"
                               value="{{ payment.deduction }}">
                    </div>

                    <!-- Overtime Hours -->
                    <div>
                        <label class="block text-sm font-medium mb-1">Overtime Hours</label>
                        <input type="number" name="overtime_hours" class="border w-full p-4 leading-tight bg-slate-100"
                               value="{{ payment.overtime_hours }}">
                    </div>

                    <!-- Payment Method -->
                    <div>
                        <label class="block text-sm font-medium mb-1">Payment Method*</label>
                        <select name="payment_method" class="border w-full p-4 leading-tight bg-slate-100" required>
                            <option value="cash" {% if payment.payment_method == 'cash' %}selected{% endif %}>Cash</option>
                            <option value="aba" {% if payment.payment_method == 'aba' %}selected{% endif %}>ABA</option>
                            <option value="wing" {% if payment.payment_method == 'wing' %}selected{% endif %}>Wing</option>
                            <option value="bank" {% if payment.payment_method == 'bank' %}selected{% endif %}>Bank Transfer</option>
                            <option value="other" {% if payment.payment_method == 'other' %}selected{% endif %}>Other</option>
                        </select>
                    </div>

                    <!-- Notes -->
                    <div class="col-span-3">
                        <label class="block text-sm font-medium mb-1">Notes</label>
                        <textarea class="border w-full p-4 leading-tight bg-slate-100"
                                  name="notes"
                                  rows="3"
                                  placeholder="Additional notes">{{ payment.notes }}</textarea>
                    </div>

                    <!-- Submit Button -->
                    <div class="col-span-3 grid grid-cols-2 gap-4">
                        <button class="bg-blue-900 text-white font-bold py-2 px-4" type="submit">Update Salary Record</button>
                        <a href="{% url 'salary:view' payment.id %}" class="bg-gray-500 text-white font-bold py-2 px-4 text-center">Cancel</a>
                    </div>
                </div>
            </form>
        </div>
    </div>
</div>
{% endblock %}
