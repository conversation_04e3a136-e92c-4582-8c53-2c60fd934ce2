/**
 * Responsive Tables
 * Enhances tables to be more mobile-friendly with collapsible columns
 * Adds quick filters and sorting options directly in table headers
 */

document.addEventListener('DOMContentLoaded', function() {
    // Initialize responsive tables
    initResponsiveTables();
});

/**
 * Initialize responsive tables
 */
function initResponsiveTables() {
    // Get all responsive tables
    const tables = document.querySelectorAll('.responsive-table');

    tables.forEach(table => {
        // Add responsive data attributes to table cells
        addResponsiveDataAttributes(table);

        // Add filter inputs to table headers
        addFilterInputs(table);

        // Add sorting functionality to table headers
        addSortingFunctionality(table);

        // Add column toggle functionality
        addColumnToggle(table);

        // Add export functionality
        addExportFunctionality(table);

        // Add pagination if needed
        if (table.classList.contains('paginated')) {
            addPagination(table);
        }
    });
}

/**
 * Add responsive data attributes to table cells
 * @param {HTMLElement} table - The table element
 */
function addResponsiveDataAttributes(table) {
    // Get all table headers
    const headers = table.querySelectorAll('thead th');

    // Get all table rows
    const rows = table.querySelectorAll('tbody tr');

    // Add data attributes to each cell
    rows.forEach(row => {
        const cells = row.querySelectorAll('td');
        cells.forEach((cell, index) => {
            if (index < headers.length) {
                const headerText = headers[index].textContent.trim();
                cell.setAttribute('data-label', headerText);
            }
        });
    });
}

/**
 * Add filter inputs to table headers
 * @param {HTMLElement} table - The table element
 */
function addFilterInputs(table) {
    // Check if the table has the filterable class
    if (!table.classList.contains('filterable')) {
        return;
    }

    // Get the table header row
    const headerRow = table.querySelector('thead tr');

    // Create a new row for filters
    const filterRow = document.createElement('tr');
    filterRow.classList.add('filter-row');

    // Get all table headers
    const headers = headerRow.querySelectorAll('th');

    // Add filter inputs to each header
    headers.forEach(header => {
        const filterCell = document.createElement('th');

        // Skip if the header has the no-filter class
        if (header.classList.contains('no-filter')) {
            filterCell.classList.add('no-filter');
            filterRow.appendChild(filterCell);
            return;
        }

        // Create filter input
        const input = document.createElement('input');
        input.type = 'text';
        input.classList.add('filter-input');
        input.placeholder = 'Filter...';

        // Add event listener for filtering
        input.addEventListener('input', function() {
            filterTable(table);
        });

        filterCell.appendChild(input);
        filterRow.appendChild(filterCell);
    });

    // Add the filter row to the table header
    table.querySelector('thead').appendChild(filterRow);

    // Add global filter if needed
    if (table.classList.contains('global-filter')) {
        addGlobalFilter(table);
    }
}

/**
 * Add global filter to the table
 * @param {HTMLElement} table - The table element
 */
function addGlobalFilter(table) {
    // Create global filter container
    const filterContainer = document.createElement('div');
    filterContainer.classList.add('global-filter-container');

    // Create filter input
    const input = document.createElement('input');
    input.type = 'text';
    input.classList.add('global-filter-input');
    input.placeholder = 'Search in table...';

    // Add event listener for filtering
    input.addEventListener('input', function() {
        filterTable(table, this.value);
    });

    // Add clear button
    const clearButton = document.createElement('button');
    clearButton.classList.add('global-filter-clear');
    clearButton.innerHTML = '<i class="fas fa-times"></i>';
    clearButton.addEventListener('click', function() {
        input.value = '';
        filterTable(table, '');
    });

    // Add elements to container
    filterContainer.appendChild(input);
    filterContainer.appendChild(clearButton);

    // Add container before the table
    table.parentNode.insertBefore(filterContainer, table);
}

/**
 * Filter the table based on input values
 * @param {HTMLElement} table - The table element
 * @param {string} globalValue - The global filter value
 */
function filterTable(table, globalValue = null) {
    // Get all table rows
    const rows = table.querySelectorAll('tbody tr');

    // Get filter values
    const filterInputs = table.querySelectorAll('.filter-input');
    const filterValues = Array.from(filterInputs).map(input => input.value.toLowerCase());

    // Filter rows
    rows.forEach(row => {
        const cells = row.querySelectorAll('td');
        let showRow = true;

        // Check global filter if provided
        if (globalValue) {
            const rowText = Array.from(cells).map(cell => cell.textContent.toLowerCase()).join(' ');
            if (!rowText.includes(globalValue.toLowerCase())) {
                showRow = false;
            }
        } else {
            // Check individual column filters
            cells.forEach((cell, index) => {
                if (index < filterValues.length && filterValues[index]) {
                    const cellText = cell.textContent.toLowerCase();
                    if (!cellText.includes(filterValues[index])) {
                        showRow = false;
                    }
                }
            });
        }

        // Show or hide the row
        row.style.display = showRow ? '' : 'none';
    });
}

/**
 * Add sorting functionality to table headers
 * @param {HTMLElement} table - The table element
 */
function addSortingFunctionality(table) {
    // Check if the table has the sortable class
    if (!table.classList.contains('sortable')) {
        return;
    }

    // Get all table headers
    const headers = table.querySelectorAll('thead th');

    // Add sorting functionality to each header
    headers.forEach((header, index) => {
        // Skip if the header has the no-sort class
        if (header.classList.contains('no-sort')) {
            return;
        }

        // Add sort indicator
        const sortIndicator = document.createElement('span');
        sortIndicator.classList.add('sort-indicator');
        sortIndicator.innerHTML = '<i class="fas fa-sort"></i>';
        header.appendChild(sortIndicator);

        // Add click event for sorting
        header.addEventListener('click', function() {
            sortTable(table, index, this);
        });
    });
}

/**
 * Sort the table based on a column
 * @param {HTMLElement} table - The table element
 * @param {number} columnIndex - The index of the column to sort
 * @param {HTMLElement} header - The header element
 */
function sortTable(table, columnIndex, header) {
    // Get the sort direction
    let sortDirection = header.getAttribute('data-sort-direction') || 'asc';
    sortDirection = sortDirection === 'asc' ? 'desc' : 'asc';

    // Update the sort direction
    header.setAttribute('data-sort-direction', sortDirection);

    // Update sort indicators for all headers
    const headers = table.querySelectorAll('thead th');
    headers.forEach(h => {
        const indicator = h.querySelector('.sort-indicator');
        if (indicator) {
            indicator.innerHTML = '<i class="fas fa-sort"></i>';
        }
    });

    // Update the sort indicator for the current header
    const indicator = header.querySelector('.sort-indicator');
    indicator.innerHTML = sortDirection === 'asc'
        ? '<i class="fas fa-sort-up"></i>'
        : '<i class="fas fa-sort-down"></i>';

    // Get all table rows
    const rows = Array.from(table.querySelectorAll('tbody tr'));

    // Sort the rows
    rows.sort((a, b) => {
        const aValue = a.querySelectorAll('td')[columnIndex].textContent.trim();
        const bValue = b.querySelectorAll('td')[columnIndex].textContent.trim();

        // Check if the values are numbers
        const aNum = parseFloat(aValue.replace(/[^0-9.-]+/g, ''));
        const bNum = parseFloat(bValue.replace(/[^0-9.-]+/g, ''));

        if (!isNaN(aNum) && !isNaN(bNum)) {
            return sortDirection === 'asc' ? aNum - bNum : bNum - aNum;
        }

        // Sort as strings
        return sortDirection === 'asc'
            ? aValue.localeCompare(bValue)
            : bValue.localeCompare(aValue);
    });

    // Reorder the rows
    const tbody = table.querySelector('tbody');
    rows.forEach(row => tbody.appendChild(row));
}

/**
 * Add column toggle functionality
 * @param {HTMLElement} table - The table element
 */
function addColumnToggle(table) {
    // Check if the table has the column-toggle class
    if (!table.classList.contains('column-toggle')) {
        return;
    }

    // Create column toggle container
    const toggleContainer = document.createElement('div');
    toggleContainer.classList.add('column-toggle-container');

    // Create toggle button
    const toggleButton = document.createElement('button');
    toggleButton.classList.add('column-toggle-button');
    toggleButton.innerHTML = '<i class="fas fa-columns"></i> Columns';

    // Create dropdown menu
    const dropdownMenu = document.createElement('div');
    dropdownMenu.classList.add('column-toggle-dropdown');

    // Get all table headers
    const headers = table.querySelectorAll('thead th');

    // Add toggle options for each header
    headers.forEach((header, index) => {
        // Skip if the header has the no-toggle class
        if (header.classList.contains('no-toggle')) {
            return;
        }

        const headerText = header.textContent.trim();

        // Create toggle option
        const option = document.createElement('div');
        option.classList.add('column-toggle-option');

        // Create checkbox
        const checkbox = document.createElement('input');
        checkbox.type = 'checkbox';
        checkbox.id = `column-toggle-${table.id}-${index}`;
        checkbox.checked = true;

        // Add event listener for toggling
        checkbox.addEventListener('change', function() {
            toggleColumn(table, index, this.checked);
        });

        // Create label
        const label = document.createElement('label');
        label.htmlFor = checkbox.id;
        label.textContent = headerText;

        // Add elements to option
        option.appendChild(checkbox);
        option.appendChild(label);

        // Add option to dropdown
        dropdownMenu.appendChild(option);
    });

    // Add elements to container
    toggleContainer.appendChild(toggleButton);
    toggleContainer.appendChild(dropdownMenu);

    // Add container before the table
    table.parentNode.insertBefore(toggleContainer, table);

    // Add event listener for toggle button
    toggleButton.addEventListener('click', function() {
        dropdownMenu.classList.toggle('show');
    });

    // Close dropdown when clicking outside
    document.addEventListener('click', function(event) {
        if (!toggleContainer.contains(event.target)) {
            dropdownMenu.classList.remove('show');
        }
    });
}

/**
 * Toggle a column in the table
 * @param {HTMLElement} table - The table element
 * @param {number} columnIndex - The index of the column to toggle
 * @param {boolean} show - Whether to show or hide the column
 */
function toggleColumn(table, columnIndex, show) {
    // Get all table headers and rows
    const headers = table.querySelectorAll('thead th');
    const rows = table.querySelectorAll('tbody tr');

    // Toggle the header
    if (columnIndex < headers.length) {
        headers[columnIndex].style.display = show ? '' : 'none';
    }

    // Toggle the cells
    rows.forEach(row => {
        const cells = row.querySelectorAll('td');
        if (columnIndex < cells.length) {
            cells[columnIndex].style.display = show ? '' : 'none';
        }
    });
}

/**
 * Add export functionality
 * @param {HTMLElement} table - The table element
 */
function addExportFunctionality(table) {
    // Check if the table has the exportable class
    if (!table.classList.contains('exportable')) {
        return;
    }

    // Create export container
    const exportContainer = document.createElement('div');
    exportContainer.classList.add('export-container');

    // Create export button
    const exportButton = document.createElement('button');
    exportButton.classList.add('export-button');
    exportButton.innerHTML = '<i class="fas fa-download"></i> Export';

    // Create dropdown menu
    const dropdownMenu = document.createElement('div');
    dropdownMenu.classList.add('export-dropdown');

    // Add export options
    const exportOptions = [
        { format: 'csv', label: 'CSV', icon: 'file-csv' },
        { format: 'print', label: 'Print', icon: 'print' }
    ];

    exportOptions.forEach(option => {
        const optionElement = document.createElement('div');
        optionElement.classList.add('export-option');
        optionElement.innerHTML = `<i class="fas fa-${option.icon}"></i> ${option.label}`;

        // Add event listener for exporting
        optionElement.addEventListener('click', function() {
            exportTable(table, option.format);
        });

        dropdownMenu.appendChild(optionElement);
    });

    // Add elements to container
    exportContainer.appendChild(exportButton);
    exportContainer.appendChild(dropdownMenu);

    // Add container before the table
    table.parentNode.insertBefore(exportContainer, table);

    // Add event listener for export button
    exportButton.addEventListener('click', function() {
        dropdownMenu.classList.toggle('show');
    });

    // Close dropdown when clicking outside
    document.addEventListener('click', function(event) {
        if (!exportContainer.contains(event.target)) {
            dropdownMenu.classList.remove('show');
        }
    });
}

/**
 * Export the table to a specific format
 * @param {HTMLElement} table - The table element
 * @param {string} format - The export format
 */
function exportTable(table, format) {
    // Get table data
    const headers = Array.from(table.querySelectorAll('thead th')).map(th => th.textContent.trim());
    const rows = Array.from(table.querySelectorAll('tbody tr')).map(tr =>
        Array.from(tr.querySelectorAll('td')).map(td => td.textContent.trim())
    );

    // Export based on format
    switch (format) {
        case 'csv':
            exportCSV(headers, rows, table.id || 'table');
            break;
        case 'print':
            printTable(table);
            break;
    }
}

/**
 * Export table data to CSV
 * @param {Array} headers - The table headers
 * @param {Array} rows - The table rows
 * @param {string} filename - The filename
 */
function exportCSV(headers, rows, filename) {
    // Create CSV content
    let csv = headers.join(',') + '\n';
    rows.forEach(row => {
        csv += row.join(',') + '\n';
    });

    // Create download link
    const blob = new Blob([csv], { type: 'text/csv;charset=utf-8;' });
    const url = URL.createObjectURL(blob);
    const link = document.createElement('a');
    link.href = url;
    link.setAttribute('download', `${filename}.csv`);
    link.style.display = 'none';

    // Trigger download
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
}

/**
 * Print the table
 * @param {HTMLElement} table - The table element
 */
function printTable(table) {
    // Create a new window
    const printWindow = window.open('', '_blank');

    // Create HTML content
    const html = `
        <!DOCTYPE html>
        <html>
        <head>
            <title>Print Table</title>
            <style>
                body {
                    font-family: Arial, sans-serif;
                }
                table {
                    border-collapse: collapse;
                    width: 100%;
                }
                th, td {
                    border: 1px solid #ddd;
                    padding: 8px;
                    text-align: left;
                }
                th {
                    background-color: #f2f2f2;
                }
            </style>
        </head>
        <body>
            <h1>${table.caption ? table.caption.textContent : 'Table'}</h1>
            ${table.outerHTML}
        </body>
        </html>
    `;

    // Write HTML to the new window
    printWindow.document.write(html);
    printWindow.document.close();

    // Print the window
    printWindow.print();
}

/**
 * Add pagination to the table
 * @param {HTMLElement} table - The table element
 */
function addPagination(table) {
    // Get pagination settings
    const pageSize = parseInt(table.dataset.pageSize) || 10;

    // Get all table rows
    const rows = table.querySelectorAll('tbody tr');
    const totalRows = rows.length;
    const totalPages = Math.ceil(totalRows / pageSize);

    // Create pagination container
    const paginationContainer = document.createElement('div');
    paginationContainer.classList.add('pagination-container');

    // Create pagination controls
    const controls = document.createElement('div');
    controls.classList.add('pagination-controls');

    // Create page info
    const pageInfo = document.createElement('div');
    pageInfo.classList.add('pagination-info');

    // Add elements to container
    paginationContainer.appendChild(controls);
    paginationContainer.appendChild(pageInfo);

    // Add container after the table
    table.parentNode.insertBefore(paginationContainer, table.nextSibling);

    // Initialize pagination
    updatePagination(table, 1, pageSize, totalPages, rows, controls, pageInfo);
}

/**
 * Update pagination
 * @param {HTMLElement} table - The table element
 * @param {number} currentPage - The current page
 * @param {number} pageSize - The page size
 * @param {number} totalPages - The total number of pages
 * @param {NodeList} rows - The table rows
 * @param {HTMLElement} controls - The pagination controls
 * @param {HTMLElement} pageInfo - The page info element
 */
function updatePagination(table, currentPage, pageSize, totalPages, rows, controls, pageInfo) {
    // Update page info
    pageInfo.textContent = `Page ${currentPage} of ${totalPages}`;

    // Clear controls
    controls.innerHTML = '';

    // Create previous button
    const prevButton = document.createElement('button');
    prevButton.classList.add('pagination-button', 'prev-button');
    prevButton.innerHTML = '<i class="fas fa-chevron-left"></i>';
    prevButton.disabled = currentPage === 1;
    prevButton.addEventListener('click', function() {
        if (currentPage > 1) {
            updatePagination(table, currentPage - 1, pageSize, totalPages, rows, controls, pageInfo);
        }
    });

    // Create next button
    const nextButton = document.createElement('button');
    nextButton.classList.add('pagination-button', 'next-button');
    nextButton.innerHTML = '<i class="fas fa-chevron-right"></i>';
    nextButton.disabled = currentPage === totalPages;
    nextButton.addEventListener('click', function() {
        if (currentPage < totalPages) {
            updatePagination(table, currentPage + 1, pageSize, totalPages, rows, controls, pageInfo);
        }
    });

    // Add navigation buttons
    controls.appendChild(prevButton);
    controls.appendChild(nextButton);

    // Show/hide rows based on current page
    const startIndex = (currentPage - 1) * pageSize;
    const endIndex = startIndex + pageSize;

    Array.from(rows).forEach((row, index) => {
        row.style.display = (index >= startIndex && index < endIndex) ? '' : 'none';
    });
}
