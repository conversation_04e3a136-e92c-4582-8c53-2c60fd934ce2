{% extends 'base.html' %}
{% load custom_filters %}
{% load currency_filters %}
{% load static %}



{% block body %}
<div class="conponentSection p-5 bg-gray-200">
    <div class="componentWrapper">
        <!-- Header with navigation -->
        <div class="flex justify-between items-center mb-4">
            <h3 class="text-2xl font-bold">Make a Withdrawal</h3>
            <div class="flex space-x-2">
                <a href="{% url 'finance:index' %}" class="bg-blue-900 text-white px-4 py-2 rounded">
                    <i class="fa-solid fa-arrow-left mr-2"></i>Back to Finance
                </a>
            </div>
        </div>

        <!-- Current Balance Alert -->
        <div class="bg-blue-50 border-l-4 border-blue-400 p-4 mb-6">
            <div class="flex items-center">
                <div class="flex-shrink-0">
                    <i class="fas fa-info-circle text-blue-400"></i>
                </div>
                <div class="ml-3">
                    <p class="text-sm text-blue-700">
                        Current available balance: {{ balance|format_khr }}
                    </p>
                </div>
            </div>
        </div>

        <!-- Withdrawal Form -->
        <div class="bg-white p-6 rounded shadow-md">
            <form method="post">
                {% csrf_token %}
                <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                    <!-- Left Column -->
                    <div class="space-y-6">
                        <!-- Amount -->
                        <div>
                            <label for="amount_khr" class="block text-sm font-medium text-gray-700 mb-1">Amount (KHR)*</label>
                            <div class="relative">
                                <div class="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                                    <span class="text-gray-500">៛</span>
                                </div>
                                <input type="text" id="amount_khr" name="amount_khr" class="pl-8 block w-full border-gray-300 rounded-md shadow-sm focus:ring-blue-500 focus:border-blue-500 p-4 leading-tight bg-slate-100" placeholder="0" required>
                            </div>
                            <p class="mt-1 text-sm text-gray-500">Enter the amount in Cambodian Riel (maximum: {{ balance|format_khr }})</p>
                        </div>

                        <!-- Payment Method -->
                        <div>
                            <label for="payment_method" class="block text-sm font-medium text-gray-700 mb-1">Payment Method*</label>
                            <select id="payment_method" name="payment_method" class="block w-full border-gray-300 rounded-md shadow-sm focus:ring-blue-500 focus:border-blue-500 p-4 leading-tight bg-slate-100" required>
                                {% for value, label in payment_methods %}
                                <option value="{{ value }}">{{ label }}</option>
                                {% endfor %}
                            </select>
                        </div>
                    </div>

                    <!-- Right Column -->
                    <div class="space-y-6">
                        <!-- Notes -->
                        <div>
                            <label for="notes" class="block text-sm font-medium text-gray-700 mb-1">Notes*</label>
                            <textarea id="notes" name="notes" rows="4" class="block w-full border-gray-300 rounded-md shadow-sm focus:ring-blue-500 focus:border-blue-500 p-4 leading-tight bg-slate-100" placeholder="Detailed reason for this withdrawal" required></textarea>
                            <p class="mt-1 text-sm text-gray-500">Please provide a detailed explanation for this withdrawal</p>
                        </div>
                    </div>
                </div>

                <!-- Approval Notice -->
                {% if request.user.role != 'admin' %}
                <div class="mt-6 bg-yellow-50 border-l-4 border-yellow-400 p-4">
                    <div class="flex">
                        <div class="flex-shrink-0">
                            <i class="fas fa-exclamation-triangle text-yellow-400"></i>
                        </div>
                        <div class="ml-3">
                            <p class="text-sm text-yellow-700">
                                Note: Your withdrawal request will require approval from a manager or administrator before it is processed.
                            </p>
                        </div>
                    </div>
                </div>
                {% endif %}

                <!-- Submit Button -->
                <div class="mt-8 pt-5 border-t border-gray-200 flex justify-between">
                    <a href="{% url 'finance:index' %}" class="bg-gray-500 hover:bg-gray-600 text-white font-bold py-2 px-6 rounded transition duration-200">
                        Cancel
                    </a>
                    <button type="submit" class="bg-blue-900 hover:bg-blue-800 text-white font-bold py-2 px-6 rounded transition duration-200">
                        {% if request.user.role == 'admin' %}
                            Complete Withdrawal
                        {% else %}
                            Request Withdrawal
                        {% endif %}
                    </button>
                </div>
            </form>
        </div>
    </div>
</div>
{% endblock %}

{% block js %}
<script>
    document.addEventListener('DOMContentLoaded', function() {
        // Format amount as currency and validate against balance
        const amountInput = document.getElementById('amount_khr');
        const maxBalance = {{ balance }};

        // Setup formatted input
        setupFormattedInput(amountInput, 'khr');

        // Add validation for maximum balance
        amountInput.addEventListener('blur', function() {
            // Get the numeric value
            const numericValue = parseInt(this.value.replace(/[^\d]/g, '')) || 0;

            // Validate against balance
            if (numericValue > maxBalance) {
                this.setCustomValidity(`Amount exceeds available balance (${formatKHR(maxBalance)})`);
            } else {
                this.setCustomValidity('');
            }
        });

        // Ensure the form submits the numeric value
        const form = amountInput.closest('form');
        form.addEventListener('submit', function(e) {
            // Get the current formatted value
            const formattedValue = amountInput.value;

            // Convert to numeric value for submission
            const numericValue = formattedValue.replace(/[^\d]/g, '');
            amountInput.value = numericValue;

            // Final validation check
            if (parseInt(numericValue) > maxBalance) {
                e.preventDefault();
                alert(`Amount exceeds available balance (${formatKHR(maxBalance)})`);
                return false;
            }
        });
    });
</script>
{% endblock %}
