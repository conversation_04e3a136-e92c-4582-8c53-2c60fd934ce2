{% extends 'base.html' %}
{% load custom_filters %}



{% block body %}
<div class="conponentSection p-5 bg-gray-200">
    <div class="componentWrapper">
        <!-- Header -->
        <div class="flex justify-between items-center mb-4">
            <h3 class="text-2xl font-bold">My Salary History</h3>
        </div>

        <!-- Employee Information Card -->
        <div class="bg-white p-6 rounded-lg shadow-md mb-6">
            <div class="grid grid-cols-4 gap-4">
                <div>
                    <p class="text-gray-600 text-sm">Employee Name</p>
                    <p class="font-medium">{{ employee.name }}</p>
                </div>
                <div>
                    <p class="text-gray-600 text-sm">Employee ID</p>
                    <p class="font-medium">{{ employee.emp_id }}</p>
                </div>
                <div>
                    <p class="text-gray-600 text-sm">Role</p>
                    <p class="font-medium">{{ employee.role|title }}</p>
                </div>
                <div>
                    <p class="text-gray-600 text-sm">Base Salary</p>
                    <p class="font-medium">{{ employee.salary|format_khr }}</p>
                </div>
            </div>
        </div>

        <!-- Salary History Table -->
        <div class="bg-white p-6 rounded-lg shadow-md">
            <h4 class="text-xl font-semibold mb-4">Salary Payment History</h4>

            <div class="relative overflow-x-auto">
                <table class="w-full text-sm text-left">
                    <thead class="text-sm uppercase bg-blue-900 text-gray-50"><tr>
                            <th scope="col" class="px-6 py-3">Month</th>
                            <th scope="col" class="px-6 py-3">Base Salary</th>
                            <th scope="col" class="px-6 py-3">Bonus</th>
                            <th scope="col" class="px-6 py-3">Overtime</th>
                            <th scope="col" class="px-6 py-3">Deduction</th>
                            <th scope="col" class="px-6 py-3">Final Pay</th>
                            <th scope="col" class="px-6 py-3">Payment Date</th>
                            <th scope="col" class="px-6 py-3">Status</th>
                            <th scope="col" class="px-6 py-3">Actions</th>
                        </tr>
                    </thead>
                    <tbody>
                        {% for payment in payments %}
                        <tr class="bg-white border">
                            <td class="px-6 py-4">{{ payment.month|date:"M Y" }}</td>
                            <td class="px-6 py-4">{{ payment.base_salary|format_khr }}</td>
                            <td class="px-6 py-4">{{ payment.bonus|format_khr }}</td>
                            <td class="px-6 py-4">
                                {% if payment.overtime_hours > 0 %}
                                0៛ ({{ payment.overtime_hours }} hrs)
                                {% else %}
                                -
                                {% endif %}
                            </td>
                            <td class="px-6 py-4">{{ payment.deduction|format_khr }}</td>
                            <td class="px-6 py-4 font-medium">{{ payment.final_pay|format_khr }}</td>
                            <td class="px-6 py-4">
                                {% if payment.payment_date %}
                                {{ payment.payment_date|date:"d-M-Y" }}
                                {% else %}
                                -
                                {% endif %}
                            </td>
                            <td class="px-6 py-4">
                                {% if payment.payment_status == 'paid' %}
                                <span class="bg-green-100 text-green-800 text-xs font-medium px-2.5 py-0.5 rounded">Paid</span>
                                {% else %}
                                <span class="bg-yellow-100 text-yellow-800 text-xs font-medium px-2.5 py-0.5 rounded">Pending</span>
                                {% endif %}
                            </td>
                            <td class="px-6 py-4">
                                {% if payment.payment_status == 'paid' %}
                                <a href="{% url 'salary:print' payment.id %}" class="text-blue-600 hover:underline">Print Slip</a>
                                {% else %}
                                <span class="text-gray-400">Awaiting Payment</span>
                                {% endif %}
                            </td>
                        </tr>
                        {% empty %}
                        <tr class="bg-white border">
                            <td colspan="9" class="px-6 py-4 text-center">No salary payment records found.</td>
                        </tr>
                        {% endfor %}
                    </tbody>
                </table>
            </div>
        </div>
    </div>
</div>
{% endblock %}
