@echo off
echo ==========================================
echo   Legend Fitness Club - Shutdown Script
echo ==========================================
echo.

echo Step 1: Stopping Django server...
echo (If Django is running, press Ctrl+C in its window)
echo.

echo Step 2: Stopping Redis server...
tasklist /FI "IMAGENAME eq redis-server.exe" 2>NUL | find /I /N "redis-server.exe">NUL
if "%ERRORLEVEL%"=="0" (
    echo Stopping Redis server...
    taskkill /f /im redis-server.exe >nul 2>&1
    if "%ERRORLEVEL%"=="0" (
        echo ✓ Redis server stopped successfully
    ) else (
        echo ⚠ Could not stop Redis server
    )
) else (
    echo ✓ Redis server is not running
)

echo.
echo Step 3: Cleanup...
echo Checking for any remaining processes...

REM Check if any Redis processes are still running
tasklist /FI "IMAGENAME eq redis-server.exe" 2>NUL | find /I /N "redis-server.exe">NUL
if "%ERRORLEVEL%"=="0" (
    echo ⚠ Some Redis processes may still be running
    echo You can manually close them from Task Manager
) else (
    echo ✓ All Redis processes stopped
)

echo.
echo ==========================================
echo   Legend Fitness Club - Shutdown Complete
echo ==========================================
echo.
pause
