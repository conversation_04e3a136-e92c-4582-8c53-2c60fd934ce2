# Redis 7.x Upgrade Script for Legend Fitness Club
# Run this script as Administrator in PowerShell

Write-Host "Redis 7.x Upgrade for Legend Fitness Club" -ForegroundColor Green
Write-Host "=======================================" -ForegroundColor Green

# Check if running as administrator
if (-NOT ([Security.Principal.WindowsPrincipal] [Security.Principal.WindowsIdentity]::GetCurrent()).IsInRole([Security.Principal.WindowsBuiltInRole] "Administrator")) {
    Write-Host "This script must be run as Administrator!" -ForegroundColor Red
    Write-Host "Right-click PowerShell and select 'Run as Administrator'" -ForegroundColor Yellow
    Read-Host "Press Enter to exit"
    exit 1
}

Write-Host "Running as Administrator - proceeding..." -ForegroundColor Green

# Step 1: Stop current Redis service
Write-Host "`nStep 1: Stopping current Redis service..." -ForegroundColor Yellow
try {
    Stop-Service -Name "redis" -Force -ErrorAction SilentlyContinue
    Write-Host "Redis service stopped successfully" -ForegroundColor Green
} catch {
    Write-Host "Redis service was not running or already stopped" -ForegroundColor Yellow
}

# Step 2: Download Redis 7.x for Windows
$redisUrl = "https://github.com/tporadowski/redis/releases/download/v7.0.15/Redis-x64-7.0.15.zip"
$downloadPath = "C:\Temp\Redis-7.0.15.zip"
$extractPath = "C:\Redis7"

Write-Host "`nStep 2: Downloading Redis 7.0.15..." -ForegroundColor Yellow

# Create temp directory
if (!(Test-Path "C:\Temp")) {
    New-Item -ItemType Directory -Path "C:\Temp" -Force
}

# Download Redis 7.x
try {
    Write-Host "Downloading from: $redisUrl" -ForegroundColor Cyan
    Invoke-WebRequest -Uri $redisUrl -OutFile $downloadPath -UseBasicParsing
    Write-Host "Download completed successfully!" -ForegroundColor Green
} catch {
    Write-Host "Download failed: $($_.Exception.Message)" -ForegroundColor Red
    Write-Host "Please download manually from: $redisUrl" -ForegroundColor Yellow
    Read-Host "Press Enter to exit"
    exit 1
}

# Step 3: Extract Redis 7.x
Write-Host "`nStep 3: Extracting Redis 7.0.15..." -ForegroundColor Yellow

if (Test-Path $extractPath) {
    Write-Host "Removing existing Redis7 directory..." -ForegroundColor Yellow
    Remove-Item -Path $extractPath -Recurse -Force
}

try {
    Expand-Archive -Path $downloadPath -DestinationPath $extractPath -Force
    Write-Host "Extraction completed successfully!" -ForegroundColor Green
} catch {
    Write-Host "Extraction failed: $($_.Exception.Message)" -ForegroundColor Red
    Read-Host "Press Enter to exit"
    exit 1
}

# Step 4: Create Redis configuration
Write-Host "`nStep 4: Creating Redis configuration..." -ForegroundColor Yellow

$redisConfig = @"
# Redis 7.x Configuration for Legend Fitness Club
port 6379
bind 127.0.0.1
timeout 0
tcp-keepalive 60
loglevel notice
logfile "redis-server.log"
databases 16

# Persistence
save 900 1
save 300 10
save 60 10000
stop-writes-on-bgsave-error yes
rdbcompression yes
rdbchecksum yes
dbfilename dump.rdb
dir ./

# Memory management
maxmemory 512mb
maxmemory-policy allkeys-lru

# Performance tuning
tcp-backlog 511
timeout 0
tcp-keepalive 300

# Enable keyspace notifications for Django Channels
notify-keyspace-events Ex
"@

$configPath = "$extractPath\redis.windows-service.conf"
$redisConfig | Out-File -FilePath $configPath -Encoding UTF8
Write-Host "Redis configuration created: $configPath" -ForegroundColor Green

# Step 5: Install Redis 7.x as Windows service
Write-Host "`nStep 5: Installing Redis 7.x as Windows service..." -ForegroundColor Yellow

Set-Location $extractPath

try {
    # Remove old Redis service if exists
    & .\redis-server.exe --service-uninstall --service-name redis 2>$null
    
    # Install new Redis 7.x service
    & .\redis-server.exe --service-install redis.windows-service.conf --loglevel verbose --service-name Redis7
    
    if ($LASTEXITCODE -eq 0) {
        Write-Host "Redis 7.x service installed successfully!" -ForegroundColor Green
        
        # Start the service
        & .\redis-server.exe --service-start --service-name Redis7
        
        if ($LASTEXITCODE -eq 0) {
            Write-Host "Redis 7.x service started successfully!" -ForegroundColor Green
        } else {
            Write-Host "Failed to start Redis 7.x service" -ForegroundColor Red
        }
    } else {
        Write-Host "Failed to install Redis 7.x service" -ForegroundColor Red
    }
} catch {
    Write-Host "Service installation failed: $($_.Exception.Message)" -ForegroundColor Red
}

# Step 6: Test Redis 7.x
Write-Host "`nStep 6: Testing Redis 7.x installation..." -ForegroundColor Yellow

Start-Sleep -Seconds 3

try {
    $pingResult = & .\redis-cli.exe ping 2>$null
    if ($pingResult -eq "PONG") {
        Write-Host "✓ Redis 7.x is responding to ping!" -ForegroundColor Green
        
        # Get Redis version
        $versionInfo = & .\redis-cli.exe info server 2>$null | Select-String "redis_version"
        Write-Host "✓ $versionInfo" -ForegroundColor Green
        
        # Test basic operations
        & .\redis-cli.exe set test "Redis 7.x Working" >$null 2>&1
        $testResult = & .\redis-cli.exe get test 2>$null
        & .\redis-cli.exe del test >$null 2>&1
        
        if ($testResult -eq "Redis 7.x Working") {
            Write-Host "✓ Redis 7.x basic operations working!" -ForegroundColor Green
        }
    } else {
        Write-Host "✗ Redis 7.x is not responding" -ForegroundColor Red
    }
} catch {
    Write-Host "✗ Redis 7.x test failed: $($_.Exception.Message)" -ForegroundColor Red
}

# Step 7: Update system PATH
Write-Host "`nStep 7: Updating system PATH..." -ForegroundColor Yellow

$currentPath = [Environment]::GetEnvironmentVariable("PATH", "Machine")
if ($currentPath -notlike "*$extractPath*") {
    $newPath = "$currentPath;$extractPath"
    [Environment]::SetEnvironmentVariable("PATH", $newPath, "Machine")
    Write-Host "✓ Redis 7.x added to system PATH" -ForegroundColor Green
} else {
    Write-Host "✓ Redis 7.x already in system PATH" -ForegroundColor Green
}

# Cleanup
Write-Host "`nStep 8: Cleaning up..." -ForegroundColor Yellow
Remove-Item -Path $downloadPath -Force -ErrorAction SilentlyContinue
Write-Host "✓ Temporary files cleaned up" -ForegroundColor Green

# Final summary
Write-Host "`n" + "="*50 -ForegroundColor Green
Write-Host "Redis 7.x Upgrade Completed!" -ForegroundColor Green
Write-Host "="*50 -ForegroundColor Green
Write-Host "Redis 7.x Location: $extractPath" -ForegroundColor Yellow
Write-Host "Configuration File: $configPath" -ForegroundColor Yellow
Write-Host "Service Name: Redis7" -ForegroundColor Yellow
Write-Host ""
Write-Host "Next Steps:" -ForegroundColor Cyan
Write-Host "1. Restart your Django server" -ForegroundColor White
Write-Host "2. Run: python manage.py test_redis --detailed" -ForegroundColor White
Write-Host "3. Check for Redis 7.x detection in Django logs" -ForegroundColor White
Write-Host ""
Write-Host "Expected Django output:" -ForegroundColor Cyan
Write-Host "✓ Redis 7.x.x detected - using Redis channel layer" -ForegroundColor Green
Write-Host "✓ Redis detected - using Redis cache backend" -ForegroundColor Green

Read-Host "`nPress Enter to exit"
