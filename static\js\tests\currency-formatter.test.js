/**
 * Unit tests for currency-formatter.js
 * 
 * To run these tests, open the browser console and include this file after currency-formatter.js
 */

// Test suite for formatKHR function
function testFormatKHR() {
    console.group('Testing formatKHR function');
    
    // Test with integer values
    console.assert(formatKHR(1000) === '1,000៛', 'formatKHR(1000) should return "1,000៛"');
    console.assert(formatKHR(10000) === '10,000៛', 'formatKHR(10000) should return "10,000៛"');
    console.assert(formatKHR(1000000) === '1,000,000៛', 'formatKHR(1000000) should return "1,000,000៛"');
    
    // Test with float values (should truncate decimal places)
    console.assert(formatKHR(1000.50) === '1,000៛', 'formatKHR(1000.50) should return "1,000៛"');
    console.assert(formatKHR(1000.99) === '1,000៛', 'formatKHR(1000.99) should return "1,000៛"');
    
    // Test with string values
    console.assert(formatKHR('1000') === '1,000៛', 'formatKHR("1000") should return "1,000៛"');
    console.assert(formatKHR('1,000') === '1,000៛', 'formatKHR("1,000") should return "1,000៛"');
    
    // Test with null, undefined, and empty values
    console.assert(formatKHR(null) === '0៛', 'formatKHR(null) should return "0៛"');
    console.assert(formatKHR(undefined) === '0៛', 'formatKHR(undefined) should return "0៛"');
    console.assert(formatKHR('') === '0៛', 'formatKHR("") should return "0៛"');
    
    // Test with invalid values
    console.assert(formatKHR('invalid') === '0៛', 'formatKHR("invalid") should return "0៛"');
    
    console.groupEnd();
}

// Test suite for formatUSD function
function testFormatUSD() {
    console.group('Testing formatUSD function');
    
    // Test with integer values
    console.assert(formatUSD(1000) === '$1,000.00', 'formatUSD(1000) should return "$1,000.00"');
    console.assert(formatUSD(10000) === '$10,000.00', 'formatUSD(10000) should return "$10,000.00"');
    console.assert(formatUSD(1000000) === '$1,000,000.00', 'formatUSD(1000000) should return "$1,000,000.00"');
    
    // Test with float values (should keep 2 decimal places)
    console.assert(formatUSD(1000.50) === '$1,000.50', 'formatUSD(1000.50) should return "$1,000.50"');
    console.assert(formatUSD(1000.99) === '$1,000.99', 'formatUSD(1000.99) should return "$1,000.99"');
    
    // Test with string values
    console.assert(formatUSD('1000') === '$1,000.00', 'formatUSD("1000") should return "$1,000.00"');
    console.assert(formatUSD('1,000') === '$1,000.00', 'formatUSD("1,000") should return "$1,000.00"');
    console.assert(formatUSD('1000.50') === '$1,000.50', 'formatUSD("1000.50") should return "$1,000.50"');
    
    // Test with null, undefined, and empty values
    console.assert(formatUSD(null) === '$0.00', 'formatUSD(null) should return "$0.00"');
    console.assert(formatUSD(undefined) === '$0.00', 'formatUSD(undefined) should return "$0.00"');
    console.assert(formatUSD('') === '$0.00', 'formatUSD("") should return "$0.00"');
    
    // Test with invalid values
    console.assert(formatUSD('invalid') === '$0.00', 'formatUSD("invalid") should return "$0.00"');
    
    console.groupEnd();
}

// Test suite for formatNumber function
function testFormatNumber() {
    console.group('Testing formatNumber function');
    
    // Test with integer values and default decimals (0)
    console.assert(formatNumber(1000) === '1,000', 'formatNumber(1000) should return "1,000"');
    console.assert(formatNumber(10000) === '10,000', 'formatNumber(10000) should return "10,000"');
    console.assert(formatNumber(1000000) === '1,000,000', 'formatNumber(1000000) should return "1,000,000"');
    
    // Test with float values and default decimals (0)
    console.assert(formatNumber(1000.00) === '1,000', 'formatNumber(1000.00) should return "1,000"');
    console.assert(formatNumber(1000.50) === '1,001', 'formatNumber(1000.50) should return "1,001"');
    
    // Test with specified decimal places
    console.assert(formatNumber(1000, 2) === '1,000.00', 'formatNumber(1000, 2) should return "1,000.00"');
    console.assert(formatNumber(1000.50, 2) === '1,000.50', 'formatNumber(1000.50, 2) should return "1,000.50"');
    console.assert(formatNumber(1000.99, 2) === '1,000.99', 'formatNumber(1000.99, 2) should return "1,000.99"');
    
    // Test with string values
    console.assert(formatNumber('1000') === '1,000', 'formatNumber("1000") should return "1,000"');
    console.assert(formatNumber('1,000') === '1,000', 'formatNumber("1,000") should return "1,000"');
    
    // Test with null, undefined, and empty values
    console.assert(formatNumber(null) === '0', 'formatNumber(null) should return "0"');
    console.assert(formatNumber(undefined) === '0', 'formatNumber(undefined) should return "0"');
    console.assert(formatNumber('') === '0', 'formatNumber("") should return "0"');
    
    // Test with invalid values
    console.assert(formatNumber('invalid') === '0', 'formatNumber("invalid") should return "0"');
    
    console.groupEnd();
}

// Run all tests
function runAllTests() {
    console.group('Running Currency Formatter Tests');
    testFormatKHR();
    testFormatUSD();
    testFormatNumber();
    console.groupEnd();
    console.log('All tests completed. Check for any assertion failures above.');
}

// Automatically run tests when this file is loaded
runAllTests();
