import os
import re
import sys

def remove_i18n_tags(file_path, verbose=False):
    """Remove internationalization tags from a template file."""
    with open(file_path, 'r', encoding='utf-8', errors='ignore') as f:
        content = f.read()

    # Keep track of original content to check if changes were made
    original_content = content

    # Initialize counters for logging
    changes = {
        'i18n_tags': 0,
        'trans_tags': 0,
        'blocktrans_tags': 0,
        'quotation_marks': 0
    }

    # Remove {% load i18n %} tag
    content_after = re.sub(r'{%\s*load\s+i18n\s*%}', '', content)
    if content != content_after:
        changes['i18n_tags'] += content.count('{%') - content_after.count('{%')
        if verbose:
            print("  - Removed load i18n tags")
    content = content_after

    # First, handle {% trans "text" %} tags with double quotes
    content_after = re.sub(r'{%\s*trans\s+"([^"]*?)"\s*%}', r'\1', content)
    if content != content_after:
        changes['trans_tags'] += content.count('{% trans "') - content_after.count('{% trans "')
        if verbose:
            print("  - Removed trans tags with double quotes")
    content = content_after

    # Then, handle {% trans 'text' %} tags with single quotes
    content_after = re.sub(r"{%\s*trans\s+'([^']*?)'\s*%}", r'\1', content)
    if content != content_after:
        changes['trans_tags'] += content.count("{% trans '") - content_after.count("{% trans '")
        if verbose:
            print("  - Removed trans tags with single quotes")
    content = content_after

    # Handle cases where the quotes might have been left behind
    content_after = re.sub(r'"{%\s*trans\s+([^%]*?)\s*%}"', r'\1', content)
    content_after = re.sub(r"'{%\s*trans\s+([^%]*?)\s*%}'", r'\1', content_after)
    if content != content_after:
        if verbose:
            print("  - Cleaned up quotes around trans tags")
    content = content_after

    # Remove {% trans text %} tags (without quotes)
    content_after = re.sub(r'{%\s*trans\s+([^%]*?)\s*%}', r'\1', content)
    if content != content_after:
        changes['trans_tags'] += content.count('{% trans ') - content_after.count('{% trans ')
        if verbose:
            print("  - Removed trans tags without quotes")
    content = content_after

    # Clean up any leftover double quotes that might have been part of trans tags
    content_after = re.sub(r'"([^"]*?)"{%\s*trans\s+"([^"]*?)"\s*%}"([^"]*?)"', r'\1\2\3', content)
    content_after = re.sub(r'"([^"]*?)"{%\s*trans\s+"([^"]*?)"\s*%}', r'\1\2', content_after)
    content_after = re.sub(r'{%\s*trans\s+"([^"]*?)"\s*%}"([^"]*?)"', r'\1\2', content_after)
    if content != content_after:
        if verbose:
            print("  - Cleaned up leftover quotes from trans tags")
    content = content_after

    # Remove {% blocktrans %}...{% endblocktrans %} tags, preserving content
    content_after = re.sub(r'{%\s*blocktrans\s*(?:with\s+[^%]*?)?\s*%}(.*?){%\s*endblocktrans\s*%}',
                    r'\1', content, flags=re.DOTALL)
    if content != content_after:
        changes['blocktrans_tags'] += content.count('{% blocktrans') - content_after.count('{% blocktrans')
        if verbose:
            print("  - Removed blocktrans tags")
    content = content_after

    # Remove {% blocktrans trimmed %}...{% endblocktrans %} tags
    content_after = re.sub(r'{%\s*blocktrans\s+trimmed\s*(?:with\s+[^%]*?)?\s*%}(.*?){%\s*endblocktrans\s*%}',
                    r'\1', content, flags=re.DOTALL)
    if content != content_after:
        changes['blocktrans_tags'] += content.count('{% blocktrans trimmed') - content_after.count('{% blocktrans trimmed')
        if verbose:
            print("  - Removed blocktrans trimmed tags")
    content = content_after

    # Remove trans tags from attributes like placeholder="{% trans 'text' %}"
    content_after = re.sub(r'([\w-]+)="{%\s*trans\s+"([^"]*?)"\s*%}"', r'\1="\2"', content)
    content_after = re.sub(r"([\w-]+)=\"{%\s*trans\s+'([^']*?)'\s*%}\"", r'\1="\2"', content_after)
    if content != content_after:
        changes['trans_tags'] += 2  # Approximate count
        if verbose:
            print("  - Removed trans tags from attributes")
    content = content_after

    # Handle cases where quotes might have been left behind in attributes
    content_after = re.sub(r'([\w-]+)=""{%\s*trans\s+([^%]*?)\s*%}""', r'\1="\2"', content)
    content_after = re.sub(r'([\w-]+)="\'"{%\s*trans\s+([^%]*?)\s*%}\'"\'', r'\1="\2"', content_after)
    if content != content_after:
        if verbose:
            print("  - Cleaned up quotes in attributes")
    content = content_after

    # Additional cleanup for any remaining trans tags in attributes
    content_after = re.sub(r'([\w-]+)="([^"]*?){%\s*trans\s+"([^"]*?)"\s*%}([^"]*?)"', r'\1="\2\3\4"', content)
    content_after = re.sub(r'([\w-]+)="([^"]*?){%\s*trans\s+\'([^\']*?)\'\s*%}([^"]*?)"', r'\1="\2\3\4"', content_after)
    if content != content_after:
        if verbose:
            print("  - Cleaned up remaining trans tags in attributes")
    content = content_after

    # Final cleanup for any leftover quotation marks from trans tags
    content_after = re.sub(r'""([^"]*?)""', r'\1', content)
    content_after = re.sub(r'"([^"]*?)""{%\s*trans\s*%}"', r'\1', content_after)
    content_after = re.sub(r'"([^"]*?)"{%\s*endtrans\s*%}"', r'\1', content_after)
    if content != content_after:
        if verbose:
            print("  - Cleaned up leftover quotation marks from trans tags")
    content = content_after

    # Additional cleanup for specific cases
    content_before = content
    content_after = re.sub(r'>""<', r'><', content)
    content_after = re.sub(r'>"([^"<>]*?)"<', r'>\1<', content_after)
    content_after = re.sub(r'>"""<i', r'><i', content_after)
    content_after = re.sub(r'</i>"""<', r'</i><', content_after)
    content_after = re.sub(r'>"""([^"<>]*?)"""<', r'>\1<', content_after)
    if content_before != content_after:
        changes['quotation_marks'] += 1
        if verbose:
            print("  - Cleaned up quotation marks in HTML elements")
    content = content_after

    # Specific cleanup for category names and other elements with unnecessary quotation marks
    content_before = content
    content_after = re.sub(r'class="([^"]*?)">"\s*<', r'class="\1"><', content)  # Fix quotation marks at the beginning of elements
    content_after = re.sub(r'class="([^"]*?)">"\s*([^<]*?)"<', r'class="\1">\2<', content_after)  # Fix quotation marks around text
    content_after = re.sub(r'class="([^"]*?)">"\s*<i', r'class="\1"><i', content_after)  # Fix quotation marks before icons
    content_after = re.sub(r'</i>\s*"<', r'</i><', content_after)  # Fix quotation marks after icons
    content_after = re.sub(r'</i>\s*([^<]*?)"<', r'</i> \1<', content_after)  # Fix quotation marks after icons with text
    if content_before != content_after:
        changes['quotation_marks'] += 1
        if verbose:
            print("  - Cleaned up quotation marks in class attributes")
    content = content_after

    # Fix quotation marks in buttons and other elements
    content_before = content
    content_after = re.sub(r'<button([^>]*?)>"\s*', r'<button\1>', content)  # Remove opening quote in buttons
    content_after = re.sub(r'"\s*</button>', r'</button>', content_after)  # Remove closing quote in buttons
    content_after = re.sub(r'<span([^>]*?)>"\s*', r'<span\1>', content_after)  # Remove opening quote in spans
    content_after = re.sub(r'"\s*</span>', r'</span>', content_after)  # Remove closing quote in spans
    if content_before != content_after:
        changes['quotation_marks'] += 1
        if verbose:
            print("  - Cleaned up quotation marks in buttons and spans")
    content = content_after

    # Fix quotation marks in table headers and rows
    content_before = content
    content_after = re.sub(r'<thead([^>]*?)>"\s*<tr>', r'<thead\1><tr>', content)  # Fix thead opening tag
    content_after = re.sub(r'<th([^>]*?)>([^<]*?)"</th>', r'<th\1>\2</th>', content_after)  # Fix th closing tag
    if content_before != content_after:
        changes['quotation_marks'] += 1
        if verbose:
            print("  - Cleaned up quotation marks in table headers")
    content = content_after

    # Fix specific category names with quotation marks
    content_before = content
    for category in ["All Products", "Beverages", "Snacks", "Supplements", "Merchandise", "All Categories",
                     "Table View", "Grid View", "Apply Filters", "Import from CSV", "Advanced Filters"]:
        # Replace with and without spaces around the category name
        content_after = re.sub(f'"{category}"', category, content)
        content_after = re.sub(f'" {category} "', f' {category} ', content_after)
        content_after = re.sub(f'"{category} "', f'{category} ', content_after)
        content_after = re.sub(f'" {category}"', f' {category}', content_after)
        content = content_after

    if content_before != content_after:
        changes['quotation_marks'] += 1
        if verbose:
            print("  - Cleaned up quotation marks around category names")

    # Check if any changes were made
    if content == original_content:
        print(f"No internationalization tags found in {file_path}")
        return False

    # Write the modified content back to the file
    with open(file_path, 'w', encoding='utf-8') as f:
        f.write(content)

    # Log the changes
    changes_summary = []
    if changes['i18n_tags'] > 0:
        changes_summary.append(f"{changes['i18n_tags']} load i18n tags")
    if changes['trans_tags'] > 0:
        changes_summary.append(f"{changes['trans_tags']} trans tags")
    if changes['blocktrans_tags'] > 0:
        changes_summary.append(f"{changes['blocktrans_tags']} blocktrans tags")
    if changes['quotation_marks'] > 0:
        changes_summary.append(f"{changes['quotation_marks']} instances of quotation marks")

    if changes_summary:
        summary_text = ", ".join(changes_summary)
        print(f"Removed from {file_path}: {summary_text}")
    else:
        print(f"Removed internationalization tags from {file_path}")

    return True

def find_html_files(directory):
    """Find all HTML files in the given directory and its subdirectories."""
    html_files = []
    for root, _, files in os.walk(directory):
        for file in files:
            if file.endswith('.html'):
                html_files.append(os.path.join(root, file))
    return html_files

def main():
    import argparse

    parser = argparse.ArgumentParser(description='Remove internationalization tags from Django templates.')
    parser.add_argument('path', help='Template file path or directory containing templates')
    parser.add_argument('-v', '--verbose', action='store_true', help='Enable verbose output')
    parser.add_argument('-p', '--product-only', action='store_true', help='Process only product templates')
    args = parser.parse_args()

    path = args.path
    verbose = args.verbose

    if os.path.isfile(path):
        # Process a single file
        if path.endswith('.html'):
            remove_i18n_tags(path, verbose=verbose)
        else:
            print(f"Error: {path} is not an HTML file")
    elif os.path.isdir(path):
        # Process all HTML files in the directory and subdirectories
        html_files = find_html_files(path)
        if not html_files:
            print(f"No HTML files found in {path}")
            sys.exit(1)

        # Filter for product templates if requested
        if args.product_only:
            html_files = [f for f in html_files if 'product' in f.lower()]
            print(f"Found {len(html_files)} product template files")

        processed_count = 0

        for html_file in html_files:
            if verbose:
                print(f"Processing {html_file}...")

            if remove_i18n_tags(html_file, verbose=verbose):
                processed_count += 1

        print(f"Processed {len(html_files)} HTML files. Removed internationalization tags from {processed_count} files.")
    else:
        print(f"Error: {path} does not exist")
        sys.exit(1)

if __name__ == "__main__":
    main()
