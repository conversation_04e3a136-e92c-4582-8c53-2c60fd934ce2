# Redis 7.4.0 Upgrade Guide for Windows

## Step 1: Stop Current Redis 3.0.504 Service

### Option A: Using Administrator Command Prompt
1. **Right-click Command Prompt** → **Run as Administrator**
2. **Stop Redis service**:
   ```cmd
   sc stop redis
   ```
3. **Verify it's stopped**:
   ```cmd
   sc query redis
   ```

### Option B: Using Services Manager
1. **Press Win + R** → Type `services.msc` → Press Enter
2. **Find "Redis" service** in the list
3. **Right-click** → **Stop**

## Step 2: Install Redis 7.x for Windows

Since Redis 7.4.0 source requires compilation, we'll use a pre-built Redis 7.x version:

### Option A: Using Pre-built Redis 7.x (Recommended)
1. **Download Redis 7.x for Windows**:
   - Go to: https://github.com/tporadowski/redis/releases
   - Download: `Redis-x64-7.0.15.zip` (latest stable)

2. **Extract to new location**:
   ```
   Extract to: C:\Redis7\
   ```

3. **Copy configuration**:
   ```cmd
   copy "C:\Program Files\Redis\redis.windows-service.conf" "C:\Redis7\redis.windows-service.conf"
   ```

### Option B: Using Docker (Alternative)
```cmd
docker run -d -p 6379:6379 --name redis7 redis:7-alpine
```

## Step 3: Configure Redis 7.x

### Create Redis 7.x Configuration
Create `C:\Redis7\redis.windows-service.conf`:
```conf
# Redis 7.x Configuration for Legend Fitness Club
port 6379
bind 127.0.0.1
timeout 0
tcp-keepalive 60
loglevel notice
logfile "redis-server.log"
databases 16

# Persistence
save 900 1
save 300 10
save 60 10000
stop-writes-on-bgsave-error yes
rdbcompression yes
rdbchecksum yes
dbfilename dump.rdb
dir ./

# Memory management
maxmemory 512mb
maxmemory-policy allkeys-lru

# Security (optional)
# requirepass your_password_here

# Performance tuning
tcp-backlog 511
timeout 0
tcp-keepalive 300
```

## Step 4: Install Redis 7.x as Windows Service

### Install New Service
```cmd
# Navigate to Redis 7 directory
cd C:\Redis7

# Install as service
redis-server.exe --service-install redis.windows-service.conf --loglevel verbose --service-name Redis7

# Start the service
redis-server.exe --service-start --service-name Redis7
```

### Alternative: Manual Start (for testing)
```cmd
cd C:\Redis7
redis-server.exe redis.windows-service.conf
```

## Step 5: Verify Redis 7.x Installation

### Test Redis Connection
```cmd
cd C:\Redis7
redis-cli.exe ping
```
Expected output: `PONG`

### Check Redis Version
```cmd
redis-cli.exe info server
```
Should show Redis version 7.x

### Test Basic Operations
```cmd
redis-cli.exe set test "Hello Redis 7"
redis-cli.exe get test
redis-cli.exe del test
```

## Step 6: Update Windows Service (if needed)

### Remove Old Redis Service
```cmd
sc delete redis
```

### Verify New Service
```cmd
sc query Redis7
```

## Step 7: Test with Django Application

### Restart Django Server
```cmd
cd "c:\Final Project\legend_fitness_club-gym-ms"
python manage.py runserver 8000
```

### Expected Output
```
✓ Redis 7.x.x detected - using Redis channel layer
✓ Redis detected - using Redis cache backend
Starting ASGI/Daphne version 4.1.0 development server
```

### Verify Full Redis Integration
```cmd
python manage.py test_redis --detailed
```

Expected output:
```
✓ Redis is available and responding
✓ Cache Backend: django_redis.cache.RedisCache
✓ Channel Backend: channels_redis.core.RedisChannelLayer
✓ Redis is fully operational!
```

## Troubleshooting

### Port Already in Use
```cmd
netstat -an | findstr :6379
taskkill /F /IM redis-server.exe
```

### Service Installation Issues
```cmd
# Run as Administrator
redis-server.exe --service-uninstall --service-name Redis7
redis-server.exe --service-install redis.windows-service.conf --service-name Redis7
```

### Permission Issues
- Ensure Command Prompt is running as Administrator
- Check Windows Firewall settings
- Verify Redis directory permissions

## Quick Commands Summary

```cmd
# Stop old Redis
sc stop redis

# Start Redis 7 manually (for testing)
cd C:\Redis7
redis-server.exe

# Test connection
redis-cli.exe ping

# Test Django integration
python manage.py test_redis --detailed
```
