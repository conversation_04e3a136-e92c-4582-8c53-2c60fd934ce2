# Generated by Django 5.0.2 on 2025-05-13 01:14

from django.db import migrations


class Migration(migrations.Migration):

    dependencies = [
        ('product', '0008_product_discount_type_product_discount_value_and_more'),
    ]

    operations = [
        migrations.RemoveField(
            model_name='promotion',
            name='categories',
        ),
        migrations.RemoveField(
            model_name='promotion',
            name='products',
        ),
        migrations.RemoveField(
            model_name='saleitem',
            name='promotion',
        ),
        migrations.RemoveField(
            model_name='product',
            name='discount_type',
        ),
        migrations.RemoveField(
            model_name='product',
            name='discount_value',
        ),
        migrations.RemoveField(
            model_name='product',
            name='discounted_price',
        ),
        migrations.RemoveField(
            model_name='product',
            name='has_discount',
        ),
        migrations.RemoveField(
            model_name='saleitem',
            name='discount_amount',
        ),
        migrations.RemoveField(
            model_name='saleitem',
            name='discount_type',
        ),
        migrations.DeleteModel(
            name='Discount',
        ),
        migrations.DeleteModel(
            name='Promotion',
        ),
    ]
