{% extends "base.html" %}
{% load custom_filters %}



{% block body %}
<div class="conponentSection p-5 sm:ml-64 bg-gray-100">
    <div class="componentWrapper max-w-7xl mx-auto">
        <!-- Header with title and actions -->
        <div class="bg-white p-6 rounded-lg shadow-sm mb-6 border-l-4 border-blue-600">
            <div class="flex flex-col md:flex-row justify-between items-start md:items-center mb-6">
                <div>
                    <div class="flex items-center">
                        <a href="{% url 'financialreport:index' %}" class="mr-3 bg-gray-100 hover:bg-gray-200 text-gray-700 p-2 rounded-full transition-colors">
                            <i class="fa-solid fa-arrow-left"></i>
                        </a>
                        <div>
                            <h2 class="text-3xl font-bold text-gray-800">{{ title }}</h2>
                            <p class="text-gray-600 mt-1">Manage report templates for PDF exports</p>
                        </div>
                    </div>
                </div>
                <div class="flex mt-4 md:mt-0">
                    <a href="{% url 'financialreport:create_template' %}" class="flex items-center bg-blue-600 text-white px-4 py-2 rounded-md hover:bg-blue-700 transition-colors">
                        <i class="fa-solid fa-plus mr-2"></i> Create Template
                    </a>
                </div>
            </div>
        </div>

        <!-- Template Demo Section -->
        <div class="bg-white rounded-lg shadow-sm p-6 mb-6 border-l-4 border-purple-600">
            <h3 class="text-xl font-bold text-gray-800 mb-4">Template Demo</h3>
            <p class="text-gray-600 mb-6">Create different templates for your financial reports and see how they look in real-time.</p>

            <div class="grid grid-cols-1 md:grid-cols-3 gap-6 mb-6">
                <div class="bg-gradient-to-br from-blue-50 to-blue-100 p-6 rounded-lg border border-blue-200">
                    <div class="flex items-center mb-4">
                        <i class="fa-solid fa-money-bill-trend-up text-blue-600 text-2xl mr-3"></i>
                        <h4 class="text-lg font-semibold text-blue-800">Income Reports</h4>
                    </div>
                    <p class="text-gray-600 mb-4">Customize how your income data is presented with different colors, fonts, and layouts.</p>
                    <div class="flex justify-between items-center">
                        <span class="text-sm text-blue-600">{{ income_templates_count }} templates</span>
                        <a href="{% url 'financialreport:create_template' %}?type=income" class="text-blue-600 hover:text-blue-800">
                            <i class="fa-solid fa-plus mr-1"></i> Add
                        </a>
                    </div>
                </div>

                <div class="bg-gradient-to-br from-red-50 to-red-100 p-6 rounded-lg border border-red-200">
                    <div class="flex items-center mb-4">
                        <i class="fa-solid fa-file-invoice-dollar text-red-600 text-2xl mr-3"></i>
                        <h4 class="text-lg font-semibold text-red-800">Expense Reports</h4>
                    </div>
                    <p class="text-gray-600 mb-4">Create professional expense reports with customizable headers, footers, and color schemes.</p>
                    <div class="flex justify-between items-center">
                        <span class="text-sm text-red-600">{{ expense_templates_count }} templates</span>
                        <a href="{% url 'financialreport:create_template' %}?type=expense" class="text-red-600 hover:text-red-800">
                            <i class="fa-solid fa-plus mr-1"></i> Add
                        </a>
                    </div>
                </div>

                <div class="bg-gradient-to-br from-green-50 to-green-100 p-6 rounded-lg border border-green-200">
                    <div class="flex items-center mb-4">
                        <i class="fa-solid fa-scale-balanced text-green-600 text-2xl mr-3"></i>
                        <h4 class="text-lg font-semibold text-green-800">Balance Reports</h4>
                    </div>
                    <p class="text-gray-600 mb-4">Design balance reports that highlight your financial performance with custom styling.</p>
                    <div class="flex justify-between items-center">
                        <span class="text-sm text-green-600">{{ balance_templates_count }} templates</span>
                        <a href="{% url 'financialreport:create_template' %}?type=balance" class="text-green-600 hover:text-green-800">
                            <i class="fa-solid fa-plus mr-1"></i> Add
                        </a>
                    </div>
                </div>
            </div>

            <div class="bg-gray-50 p-4 rounded-lg border border-gray-200">
                <div class="flex items-center mb-2">
                    <i class="fa-solid fa-lightbulb text-yellow-500 mr-2"></i>
                    <h5 class="font-medium text-gray-700">Template Tips</h5>
                </div>
                <ul class="text-sm text-gray-600 space-y-2 ml-6 list-disc">
                    <li>Create different templates for different purposes (e.g., internal review, board meetings, tax reporting)</li>
                    <li>Use consistent colors that match your brand identity</li>
                    <li>Set a default template for each report type to streamline your workflow</li>
                    <li>Preview templates before using them to ensure they meet your requirements</li>
                </ul>
            </div>
        </div>

        <!-- Template List -->
        <div class="bg-white rounded-lg shadow-sm p-6 mb-6">
            <h3 class="text-xl font-bold text-gray-800 mb-4">Report Templates</h3>

            {% if templates %}
            <div class="overflow-x-auto">
                <table class="w-full text-sm text-left">
                    <thead>"<tr class="bg-gray-50 text-xs text-gray-700 uppercase">
                            <th scope="col" class="px-6 py-3 font-medium">Name</th>
                            <th scope="col" class="px-6 py-3 font-medium">Type</th>
                            <th scope="col" class="px-6 py-3 font-medium">Default</th>
                            <th scope="col" class="px-6 py-3 font-medium">Created</th>
                            <th scope="col" class="px-6 py-3 font-medium">Actions</th>
                        </tr>
                    </thead>
                    <tbody class="divide-y divide-gray-200">
                        {% for template in templates %}
                        <tr class="hover:bg-gray-50">
                            <td class="px-6 py-4 font-medium text-gray-900">
                                {{ template.name }}
                                <div class="flex mt-1">
                                    <span class="inline-block w-4 h-4 rounded-full mr-1" style="background-color: {{ template.header_color }};"></span>
                                    <span class="inline-block w-4 h-4 rounded-full mr-1" style="background-color: {{ template.accent_color }};"></span>
                                    <span class="inline-block w-4 h-4 rounded-full" style="background-color: {{ template.text_color }};"></span>
                                </div>
                            </td>
                            <td class="px-6 py-4">
                                {% if template.template_type == 'income' %}
                                <span class="text-blue-600"><i class="fa-solid fa-money-bill-trend-up mr-1"></i> Income</span>
                                {% elif template.template_type == 'expense' %}
                                <span class="text-red-600"><i class="fa-solid fa-file-invoice-dollar mr-1"></i> Expense</span>
                                {% elif template.template_type == 'balance' %}
                                <span class="text-green-600"><i class="fa-solid fa-scale-balanced mr-1"></i> Balance</span>
                                {% endif %}
                            </td>
                            <td class="px-6 py-4">
                                {% if template.is_default %}
                                <span class="bg-green-100 text-green-800 text-xs font-medium px-2.5 py-0.5 rounded">Default</span>
                                {% else %}
                                <span class="bg-gray-100 text-gray-800 text-xs font-medium px-2.5 py-0.5 rounded">No</span>
                                {% endif %}
                            </td>
                            <td class="px-6 py-4">{{ template.created_at|date:"d-M-Y" }}</td>
                            <td class="px-6 py-4">
                                <div class="flex space-x-3">
                                    <a href="{% url 'financialreport:preview_template' template.id %}" class="text-blue-600 hover:text-blue-900" target="_blank" title="Preview">
                                        <i class="fa-solid fa-eye"></i>
                                    </a>
                                    <a href="{% url 'financialreport:edit_template' template.id %}" class="text-yellow-600 hover:text-yellow-900" title="Edit">
                                        <i class="fa-solid fa-edit"></i>
                                    </a>
                                    <a href="{% url 'financialreport:delete_template' template.id %}" class="text-red-600 hover:text-red-900" title="Delete">
                                        <i class="fa-solid fa-trash"></i>
                                    </a>
                                </div>
                            </td>
                        </tr>
                        {% endfor %}
                    </tbody>
                </table>
            </div>
            {% else %}
            <div class="text-center py-8">
                <div class="text-gray-400 mb-4">
                    <i class="fa-solid fa-file-pdf text-5xl"></i>
                </div>
                <h4 class="text-lg font-medium text-gray-700 mb-2">No Templates Found</h4>
                <p class="text-gray-500 mb-4">You haven't created any report templates yet.</p>
                <a href="{% url 'financialreport:create_template' %}" class="bg-blue-600 text-white px-4 py-2 rounded-md hover:bg-blue-700 transition-colors">Create Your First Template</a>
            </div>
            {% endif %}
        </div>
    </div>
</div>
{% endblock %}
