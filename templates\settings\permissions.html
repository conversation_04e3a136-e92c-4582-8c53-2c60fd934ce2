{% extends 'base.html' %}
{% load static %}


{% load settings_filters %}

{% block body %}
<div class="conponentSection p-3 sm:p-5 bg-gray-200">
    <div class="componentWrapper">
        <!-- Header with Back Button -->
        <div class="flex items-center mb-4">
            <a href="{% url 'settings:dashboard' %}" class="bg-blue-900 text-white p-2 rounded-full hover:bg-blue-800 mr-3">
                <i class="fas fa-arrow-left"></i>
            </a>
            <h2 class="text-xl sm:text-2xl font-bold">Permissions Management</h2>
        </div>

        <!-- Permissions Card -->
        <div class="bg-white rounded-lg shadow-md overflow-hidden mb-6">
            <!-- Card Header -->
            <div class="bg-blue-900 text-white p-4">
                <div class="flex items-center">
                    <i class="fas fa-user-shield text-2xl mr-3"></i>
                    <h3 class="text-xl font-bold">Role-Based Permissions</h3>
                </div>
            </div>

            <!-- Card Body -->
            <div class="p-4 sm:p-6">
                <div class="mb-4">
                    <p class="text-gray-700">
                        Configure access permissions for different user roles. Select which modules each role can access and their permission level.
                    </p>
                </div>

                <!-- Role Tabs -->
                <div class="mb-6">
                    <div class="border-b border-gray-200 overflow-x-auto">
                        <nav class="flex -mb-px min-w-max" aria-label="Tabs">
                            {% for role in roles %}
                                <button class="px-4 sm:px-6 py-3 text-sm font-medium {% if forloop.first %}border-b-2 border-blue-900 text-blue-900{% else %}border-b-2 border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300{% endif %} whitespace-nowrap flex items-center role-tab" data-role="{{ role }}">
                                    <i class="fas {% if role == 'admin' %}fa-user-shield{% elif role == 'cashier' %}fa-cash-register{% elif role == 'coach' %}fa-dumbbell{% elif role == 'cleaner' %}fa-broom{% elif role == 'security' %}fa-user-shield{% else %}fa-user{% endif %} mr-2"></i>
                                    {{ role|title }}
                                </button>
                            {% endfor %}
                        </nav>
                    </div>
                </div>

                <!-- Role Permission Forms -->
                {% for role in roles %}
                <div id="{{ role }}-permissions" class="role-permissions {% if not forloop.first %}hidden{% endif %}">
                    <form action="{% url 'settings:update_permissions' %}" method="POST" class="space-y-6">
                        {% csrf_token %}
                        <input type="hidden" name="role" value="{{ role }}">

                        <div class="flex flex-col sm:flex-row justify-between items-start sm:items-center mb-6 gap-4">
                            <h4 class="text-lg font-semibold text-gray-800">
                                Permissions for {{ role|title }}
                            </h4>
                            <div class="flex flex-wrap gap-2 w-full sm:w-auto">
                                <button type="submit" class="bg-blue-700 hover:bg-blue-800 text-white font-medium py-2 px-4 rounded flex items-center justify-center transition-colors flex-grow sm:flex-grow-0">
                                    <i class="fa-solid fa-save mr-2"></i>Save Changes
                                </button>
                                <a href="{% url 'settings:reset_role_permissions' role=role %}" class="bg-gray-500 hover:bg-gray-600 text-white font-medium py-2 px-4 rounded flex items-center justify-center transition-colors flex-grow sm:flex-grow-0" onclick="return confirm('Are you sure you want to reset permissions for this role to default values?')">
                                    <i class="fa-solid fa-rotate-left mr-2"></i>Reset to Default
                                </a>
                            </div>
                        </div>

                        <div class="overflow-x-auto -mx-4 sm:mx-0 p-4 sm:p-0">
                            <table class="min-w-full divide-y divide-gray-200 table-fixed md:table-auto">
                                <thead class="bg-gray-50">
                                    <tr>
                                        <th scope="col" class="px-3 sm:px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider w-1/3">
                                            Module
                                        </th>
                                        <th scope="col" class="px-3 sm:px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider w-1/3">
                                            Permission Level
                                        </th>
                                        <th scope="col" class="px-3 sm:px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider w-1/3">
                                            Default
                                        </th>
                                    </tr>
                                </thead>
                                <tbody class="bg-white divide-y divide-gray-200">
                                    {% for module_code, module_name in modules %}
                                    <tr>
                                        <td class="px-3 sm:px-6 py-4 whitespace-nowrap">
                                            <div class="flex items-center">
                                                <i class="fas {% if module_code == 'dashboard' %}fa-tachometer-alt{% elif module_code == 'member' %}fa-users{% elif module_code == 'payment' %}fa-money-bill-wave{% elif module_code == 'payroll' %}fa-file-invoice-dollar{% elif module_code == 'product' %}fa-box{% elif module_code == 'inventory' %}fa-boxes{% elif module_code == 'purchase' %}fa-shopping-cart{% elif module_code == 'pos' %}fa-cash-register{% elif module_code == 'paypervisit' %}fa-walking{% elif module_code == 'bill' %}fa-file-invoice{% elif module_code == 'finance' %}fa-chart-line{% elif module_code == 'financialreport' %}fa-chart-bar{% elif module_code == 'user' %}fa-user-cog{% elif module_code == 'settings' %}fa-cog{% else %}fa-circle{% endif %} mr-2 text-gray-500 hidden sm:inline-block"></i>
                                                <div class="text-xs sm:text-sm font-medium text-gray-900">{{ module_name }}</div>
                                            </div>
                                        </td>
                                        <td class="px-3 sm:px-6 py-4">
                                            <select name="{{ role }}_{{ module_code }}" class="block w-full pl-2 pr-8 sm:pl-3 sm:pr-10 py-1 sm:py-2 text-xs sm:text-sm border-gray-300 focus:outline-none focus:ring-blue-500 focus:border-blue-500 rounded-md">
                                                {% for level_code, level_name in permission_levels %}
                                                <option value="{{ level_code }}" {% if role_permissions|get_item:role|get_item:module_code == level_code %}selected{% endif %}>{{ level_name }}</option>
                                                {% endfor %}
                                            </select>
                                        </td>
                                        <td class="px-3 sm:px-6 py-4 whitespace-nowrap text-xs sm:text-sm text-gray-500">
                                            {% if default_permissions|get_item:role|get_item:module_code %}
                                                {% for level_code, level_name in permission_levels %}
                                                    {% if default_permissions|get_item:role|get_item:module_code == level_code %}
                                                        {{ level_name }}
                                                    {% endif %}
                                                {% endfor %}
                                            {% else %}
                                                No Access
                                            {% endif %}
                                        </td>
                                    </tr>
                                    {% endfor %}
                                </tbody>
                            </table>
                        </div>
                    </form>
                </div>
                {% endfor %}

                <!-- Reset All Permissions -->
                <div class="mt-8 pt-6 border-t border-gray-200">
                    <div class="flex flex-col sm:flex-row justify-between items-start sm:items-center gap-4">
                        <h4 class="text-lg font-semibold text-gray-800">
                            Reset All Permissions
                        </h4>
                        <a href="{% url 'settings:reset_all_permissions' %}" class="bg-red-600 hover:bg-red-700 text-white font-medium py-2 px-4 rounded flex items-center justify-center transition-colors w-full sm:w-auto" onclick="return confirm('Are you sure you want to reset ALL permissions for ALL roles to their default values? This cannot be undone.')">
                            <i class="fa-solid fa-rotate-left mr-2"></i>Reset All Permissions to Default
                        </a>
                    </div>
                    <p class="mt-4 text-sm text-gray-600">
                        This will reset all permissions for all roles to their default values. This action cannot be undone.
                    </p>
                </div>
            </div>
        </div>
    </div>
</div>

{% block extra_js %}
<script>
    document.addEventListener('DOMContentLoaded', function() {
        // Role tab switching
        const roleTabs = document.querySelectorAll('.role-tab');
        const rolePermissions = document.querySelectorAll('.role-permissions');

        roleTabs.forEach(tab => {
            tab.addEventListener('click', function() {
                // Remove active class from all tabs
                roleTabs.forEach(t => {
                    t.classList.remove('border-blue-900', 'text-blue-900');
                    t.classList.add('border-transparent', 'text-gray-500');
                });

                // Add active class to clicked tab
                this.classList.remove('border-transparent', 'text-gray-500');
                this.classList.add('border-blue-900', 'text-blue-900');

                // Hide all permission forms
                rolePermissions.forEach(form => {
                    form.classList.add('hidden');
                });

                // Show the selected permission form
                const role = this.getAttribute('data-role');
                document.getElementById(`${role}-permissions`).classList.remove('hidden');
            });
        });
    });
</script>
{% endblock %}
{% endblock %}
