{% extends "../base.html" %}
{% load static %}
{% load i18n %}

{% block extra_css %}
<style>
/* Employee Dashboard Specific Styles */
.employee-dashboard {
    background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%);
    min-height: calc(100vh - 120px);
}

/* Ensure proper spacing and layout for employee dashboard */
.conponentSection.employee-dashboard {
    margin-left: 16rem; /* Sidebar width */
    padding: 1rem 1.25rem;
}

/* Responsive adjustments for employee dashboard */
@media (max-width: 640px) {
    .conponentSection.employee-dashboard {
        margin-left: 0 !important; /* Remove sidebar margin on mobile */
        padding: 0.75rem;
    }
}

@media (min-width: 641px) and (max-width: 1024px) {
    .conponentSection.employee-dashboard {
        padding: 1rem;
    }
}

@media (min-width: 1025px) {
    .conponentSection.employee-dashboard {
        padding: 1.25rem 2rem;
    }
}

.dashboard-header {
    background: linear-gradient(135deg, #1e3a8a 0%, #3730a3 100%);
    border-radius: 1rem;
    box-shadow: 0 10px 25px rgba(30, 58, 138, 0.2);
    margin-bottom: 2rem;
}

.welcome-card {
    background: rgba(255, 255, 255, 0.95);
    backdrop-filter: blur(10px);
    border: 1px solid rgba(255, 255, 255, 0.2);
    border-radius: 0.75rem;
    box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
}

.dashboard-cards-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
    gap: 1.5rem;
    margin-top: 2rem;
    width: 100%;
    max-width: 100%;
}

.dashboard-card {
    background: white;
    border-radius: 1rem;
    padding: 2rem;
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    border: 1px solid rgba(226, 232, 240, 0.8);
    position: relative;
    overflow: hidden;
    cursor: pointer;
}

.dashboard-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 4px;
    background: linear-gradient(90deg, var(--card-color, #3b82f6), var(--card-color-light, #60a5fa));
    transform: scaleX(0);
    transition: transform 0.3s ease;
}

.dashboard-card:hover::before {
    transform: scaleX(1);
}

.dashboard-card:hover {
    transform: translateY(-8px);
    box-shadow: 0 20px 40px rgba(0, 0, 0, 0.15);
    border-color: var(--card-color, #3b82f6);
}

.dashboard-card-icon {
    width: 4rem;
    height: 4rem;
    border-radius: 1rem;
    display: flex;
    align-items: center;
    justify-content: center;
    margin-bottom: 1.5rem;
    background: linear-gradient(135deg, var(--card-color, #3b82f6), var(--card-color-light, #60a5fa));
    color: white;
    font-size: 1.75rem;
    box-shadow: 0 8px 20px rgba(0, 0, 0, 0.15);
}

.dashboard-card-title {
    font-size: 1.25rem;
    font-weight: 600;
    color: #1f2937;
    margin-bottom: 0.5rem;
    line-height: 1.3;
}

.dashboard-card-description {
    color: #6b7280;
    font-size: 0.875rem;
    margin-bottom: 1.5rem;
    line-height: 1.5;
}

.dashboard-card-action {
    display: inline-flex;
    align-items: center;
    color: var(--card-color, #3b82f6);
    font-weight: 500;
    text-decoration: none;
    font-size: 0.875rem;
    transition: all 0.2s ease;
}

.dashboard-card-action:hover {
    color: var(--card-color-dark, #1d4ed8);
    text-decoration: none;
}

.dashboard-card-action i {
    margin-left: 0.5rem;
    transition: transform 0.2s ease;
}

.dashboard-card:hover .dashboard-card-action i {
    transform: translateX(4px);
}

/* Color variations for different modules */
.dashboard-card.members {
    --card-color: #8b5cf6;
    --card-color-light: #a78bfa;
    --card-color-dark: #7c3aed;
}

.dashboard-card.paypervisit {
    --card-color: #f59e0b;
    --card-color-light: #fbbf24;
    --card-color-dark: #d97706;
}

.dashboard-card.products {
    --card-color: #06b6d4;
    --card-color-light: #22d3ee;
    --card-color-dark: #0891b2;
}

.dashboard-card.purchases {
    --card-color: #10b981;
    --card-color-light: #34d399;
    --card-color-dark: #059669;
}

.dashboard-card.payments {
    --card-color: #3b82f6;
    --card-color-light: #60a5fa;
    --card-color-dark: #1d4ed8;
}

.dashboard-card.finance {
    --card-color: #ef4444;
    --card-color-light: #f87171;
    --card-color-dark: #dc2626;
}

.no-access-card {
    background: linear-gradient(135deg, #fef3c7 0%, #fde68a 100%);
    border: 1px solid #f59e0b;
    border-radius: 1rem;
    padding: 2rem;
    text-align: center;
    margin-top: 2rem;
}

/* Responsive Design */
@media (max-width: 768px) {
    .dashboard-cards-grid {
        grid-template-columns: 1fr;
        gap: 1rem;
        margin-top: 1.5rem;
    }

    .dashboard-card {
        padding: 1.5rem;
    }

    .dashboard-card-icon {
        width: 3rem;
        height: 3rem;
        font-size: 1.5rem;
        margin-bottom: 1rem;
    }

    .dashboard-card-title {
        font-size: 1.125rem;
    }

    .dashboard-header {
        margin-bottom: 1.5rem;
        padding: 1rem !important;
    }

    .welcome-card {
        padding: 1rem !important;
    }
}

@media (max-width: 480px) {
    .conponentSection.employee-dashboard {
        padding: 0.75rem !important;
    }

    .dashboard-card {
        padding: 1.25rem;
    }

    .dashboard-card-icon {
        width: 2.5rem;
        height: 2.5rem;
        font-size: 1.25rem;
    }

    .dashboard-cards-grid {
        gap: 0.75rem;
        margin-top: 1rem;
    }
}

/* Touch-friendly improvements */
@media (hover: none) and (pointer: coarse) {
    .dashboard-card {
        padding: 1.75rem;
    }

    .dashboard-card-action {
        padding: 0.5rem 0;
        font-size: 1rem;
    }
}
</style>
{% endblock extra_css %}

{% block body %}
<!-- Employee Dashboard -->
<div class="conponentSection employee-dashboard p-3 sm:p-5 bg-gray-200">
    <div class="max-w-7xl mx-auto">
        <!-- Dashboard Header -->
        <div class="dashboard-header p-6 sm:p-8 text-white">
            <div class="welcome-card p-6">
                <div class="flex flex-col sm:flex-row sm:items-center sm:justify-between">
                    <div class="mb-4 sm:mb-0">
                        <h1 class="text-2xl sm:text-3xl font-bold text-gray-800 mb-2">
                            {% trans "Welcome back" %}, {{ user.name|default:user.username }}
                        </h1>
                        <p class="text-gray-600 flex items-center">
                            <i class="fas fa-user-tag mr-2 text-blue-600"></i>
                            {% trans "Your role" %}: <span class="font-semibold ml-1 text-blue-700">{{ role|title }}</span>
                        </p>
                    </div>
                    <div class="flex items-center space-x-4">
                        <div class="text-center">
                            <div class="text-sm text-gray-500">{% trans "Today" %}</div>
                            <div class="font-semibold text-gray-800">{{ "now"|date:"M d, Y" }}</div>
                        </div>
                        <div class="w-12 h-12 bg-gradient-to-br from-blue-500 to-purple-600 rounded-full flex items-center justify-center text-white font-bold text-lg">
                            {{ user.name|default:user.username|first|upper }}
                        </div>
                    </div>
                </div>
            </div>
        </div>

                <!-- Additional Quick Actions -->
        {% if permissions.can_manage_paypervisit or permissions.can_manage_products %}
        <div class="mb-8">
            <h3 class="text-lg font-semibold text-gray-800 mb-4 flex items-center">
                <i class="fas fa-bolt mr-2 text-yellow-500"></i>
                {% trans "Quick Actions" %}
            </h3>
            <div class="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-4">
                {% if permissions.can_manage_paypervisit %}
                <a href="{% url "paypervisit:index" %}" class="bg-white p-4 rounded-lg shadow-md hover:shadow-lg transition-shadow border-l-4 border-yellow-500">
                    <div class="flex items-center">
                        <div class="bg-yellow-100 p-2 rounded-lg mr-3">
                            <i class="fas fa-plus text-yellow-600"></i>
                        </div>
                        <div>
                            <div class="font-medium text-gray-800">{% trans "New Visit" %}</div>
                            <div class="text-sm text-gray-600">{% trans "Register walk-in" %}</div>
                        </div>
                    </div>
                </a>
                {% endif %}

                {% if permissions.can_manage_products %}
                <a href="{% url "product:pos" %}" class="bg-white p-4 rounded-lg shadow-md hover:shadow-lg transition-shadow border-l-4 border-cyan-500">
                    <div class="flex items-center">
                        <div class="bg-cyan-100 p-2 rounded-lg mr-3">
                            <i class="fas fa-cash-register text-cyan-600"></i>
                        </div>
                        <div>
                            <div class="font-medium text-gray-800">{% trans "POS System" %}</div>
                            <div class="text-sm text-gray-600">{% trans "Sell products" %}</div>
                        </div>
                    </div>
                </a>
                {% endif %}

                {% if permissions.can_manage_payments %}
                <a href="{% url "payment:index" %}" class="bg-white p-4 rounded-lg shadow-md hover:shadow-lg transition-shadow border-l-4 border-blue-500">
                    <div class="flex items-center">
                        <div class="bg-blue-100 p-2 rounded-lg mr-3">
                            <i class="fas fa-money-bill-wave text-blue-600"></i>
                        </div>
                        <div>
                            <div class="font-medium text-gray-800">{% trans "New Payment" %}</div>
                            <div class="text-sm text-gray-600">{% trans "Process payment" %}</div>
                        </div>
                    </div>
                </a>
                {% endif %}

                {% if permissions.can_manage_members %}
                <a href="{% url "member:create_member" %}" class="bg-white p-4 rounded-lg shadow-md hover:shadow-lg transition-shadow border-l-4 border-purple-500">
                    <div class="flex items-center">
                        <div class="bg-purple-100 p-2 rounded-lg mr-3">
                            <i class="fas fa-user-plus text-purple-600"></i>
                        </div>
                        <div>
                            <div class="font-medium text-gray-800">{% trans "New Member" %}</div>
                            <div class="text-sm text-gray-600">{% trans "Register member" %}</div>
                        </div>
                    </div>
                </a>
                {% endif %}
            </div>
        </div>
        <!-- Quick Access Modules -->
        <div class="mb-8">
            <h2 class="text-xl sm:text-2xl font-bold text-gray-800 mb-6 flex items-center">
                <i class="fas fa-tachometer-alt mr-3 text-blue-600"></i>
                {% trans "Quick Access" %}
            </h2>

            <div class="dashboard-cards-grid">
                {% if permissions.can_manage_members %}
                <!-- Members Management Card -->
                <div class="dashboard-card members">
                    <div class="dashboard-card-icon">
                        <i class="fas fa-users"></i>
                    </div>
                    <h3 class="dashboard-card-title">{% trans "Member Management" %}</h3>
                    <p class="dashboard-card-description">
                        {% trans "Manage gym members, memberships, and member information" %}
                    </p>
                    <a href="{% url "member:index" %}" class="dashboard-card-action">
                        {% trans "Manage Members" %} <i class="fas fa-arrow-right"></i>
                    </a>
                </div>
                {% endif %}

                {% if permissions.can_manage_paypervisit %}
                <!-- Pay-per-visit Management Card -->
                <div class="dashboard-card paypervisit">
                    <div class="dashboard-card-icon">
                        <i class="fas fa-walking"></i>
                    </div>
                    <h3 class="dashboard-card-title">{% trans "Pay-per-visit" %}</h3>
                    <p class="dashboard-card-description">
                        {% trans "Process daily gym visits and manage walk-in customers" %}
                    </p>
                    <a href="{% url "paypervisit:index" %}" class="dashboard-card-action">
                        {% trans "Manage Visits" %} <i class="fas fa-arrow-right"></i>
                    </a>
                </div>
                {% endif %}

                {% if permissions.can_manage_products %}
                <!-- Products Management Card -->
                <div class="dashboard-card products">
                    <div class="dashboard-card-icon">
                        <i class="fas fa-box"></i>
                    </div>
                    <h3 class="dashboard-card-title">{% trans "Product Management" %}</h3>
                    <p class="dashboard-card-description">
                        {% trans "Manage gym products, inventory, and sales" %}
                    </p>
                    <a href="{% url "product:index" %}" class="dashboard-card-action">
                        {% trans "Manage Products" %} <i class="fas fa-arrow-right"></i>
                    </a>
                </div>
                {% endif %}

                {% if permissions.can_manage_purchases %}
                <!-- Purchase Management Card -->
                <div class="dashboard-card purchases">
                    <div class="dashboard-card-icon">
                        <i class="fas fa-shopping-cart"></i>
                    </div>
                    <h3 class="dashboard-card-title">{% trans "Purchase Management" %}</h3>
                    <p class="dashboard-card-description">
                        {% trans "Record and track product purchases from suppliers" %}
                    </p>
                    <a href="{% url "product:purchases" %}" class="dashboard-card-action">
                        {% trans "Manage Purchases" %} <i class="fas fa-arrow-right"></i>
                    </a>
                </div>
                {% endif %}

                {% if permissions.can_manage_payments %}
                <!-- Payment Processing Card -->
                <div class="dashboard-card payments">
                    <div class="dashboard-card-icon">
                        <i class="fas fa-credit-card"></i>
                    </div>
                    <h3 class="dashboard-card-title">{% trans "Payment Processing" %}</h3>
                    <p class="dashboard-card-description">
                        {% trans "Process member payments and manage payment records" %}
                    </p>
                    <a href="{% url "payment:index" %}" class="dashboard-card-action">
                        {% trans "Process Payments" %} <i class="fas fa-arrow-right"></i>
                    </a>
                </div>
                {% endif %}

                {% if permissions.can_manage_transactions %}
                <!-- Finance Management Card -->
                <div class="dashboard-card finance">
                    <div class="dashboard-card-icon">
                        <i class="fas fa-chart-line"></i>
                    </div>
                    <h3 class="dashboard-card-title">{% trans "Finance Management" %}</h3>
                    <p class="dashboard-card-description">
                        {% trans "View financial transactions and manage gym finances" %}
                    </p>
                    <a href="{% url "finance:index" %}" class="dashboard-card-action">
                        {% trans "View Transactions" %} <i class="fas fa-arrow-right"></i>
                    </a>
                </div>
                {% endif %}

                {% if permissions.can_manage_bills %}
                <!-- Bill Management Card -->
                <div class="dashboard-card bills">
                    <div class="dashboard-card-icon">
                        <i class="fas fa-file-invoice"></i>
                    </div>
                    <h3 class="dashboard-card-title">{% trans "Bill Management" %}</h3>
                    <p class="dashboard-card-description">
                        {% trans "Manage gym bills and expense tracking" %}
                    </p>
                    <a href="{% url "billmanagement:index" %}" class="dashboard-card-action">
                        {% trans "Manage Bills" %} <i class="fas fa-arrow-right"></i>
                    </a>
                </div>
                {% endif %}
            </div>

            <!-- No Access Message -->
            {% if not permissions.can_manage_members and not permissions.can_manage_paypervisit and not permissions.can_manage_products and not permissions.can_manage_purchases and not permissions.can_manage_transactions and not permissions.can_manage_payments and not permissions.can_manage_inventory and not permissions.can_manage_bills %}
            <div class="no-access-card">
                <div class="text-center">
                    <i class="fas fa-exclamation-triangle text-4xl text-amber-600 mb-4"></i>
                    <h3 class="text-xl font-semibold text-amber-800 mb-2">{% trans "No Access" %}</h3>
                    <p class="text-amber-700">
                        {% trans "Your role doesn't have access to any modules. Please contact an administrator for assistance." %}
                    </p>
                </div>
            </div>
            {% endif %}
        </div>


        {% endif %}
    </div>
</div>
{% endblock body %}