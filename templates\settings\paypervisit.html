{% extends 'base.html' %}
{% load static %}



{% block body %}
<div class="conponentSection p-5 bg-gray-200">
    <div class="componentWrapper">
        <!-- Header with Back Button -->
        <div class="flex items-center mb-4">
            <a href="{% url 'settings:dashboard' %}" class="bg-blue-900 text-white p-2 rounded-full hover:bg-blue-800 mr-3">
                <i class="fas fa-arrow-left"></i>
            </a>
            <h2 class="text-2xl font-bold">Pay-per-visit Settings</h2>
        </div>

        <!-- Settings Card -->
        <div class="bg-white rounded-lg shadow-md overflow-hidden">
            <!-- Card Header -->
            <div class="bg-blue-900 text-white p-4">
                <div class="flex items-center">
                    <i class="fas fa-users text-2xl mr-3"></i>
                    <h3 class="text-xl font-bold">Price Configuration</h3>
                </div>
            </div>

            <!-- Card Body -->
            <div class="p-6">
                <form method="post">
                    {% csrf_token %}

                    <div class="mb-6">
                        <label class="block text-sm font-medium mb-2">Price Per Person (KHR)*</label>
                        <div class="relative">
                            <div class="absolute inset-y-0 left-0 flex items-center pl-4 pointer-events-none">
                                <span class="text-gray-500">៛</span>
                            </div>
                            <input class="border w-full p-4 pl-8 text-xl font-bold leading-tight bg-slate-100 rounded"
                                   id="price_per_person"
                                   name="price_per_person"
                                   type="number"
                                   min="1"
                                   placeholder="Enter price"
                                   value="{{ settings.paypervisit_price_per_person }}"
                                   required />
                        </div>
                        <p class="text-sm text-gray-500 mt-2">Base price per person</p>
                    </div>

                    <div class="mb-6">
                        <h4 class="text-lg font-semibold mb-3">Quick Selection Options</h4>
                        <p class="text-sm text-gray-600 mb-4">Configure the quick selection buttons that appear in the Pay-per-visit POS</p>
                        
                        <div class="grid grid-cols-1 md:grid-cols-2 gap-4 mb-4">
                            <div>
                                <label class="block text-sm font-medium mb-2">Quick Selection 1 (People Count)*</label>
                                <input class="border w-full p-4 leading-tight bg-slate-100 rounded"
                                       id="quick_select_1"
                                       name="quick_select_1"
                                       type="number"
                                       min="2"
                                       placeholder="Number of people"
                                       value="{{ settings.paypervisit_quick_select_1 }}"
                                       required />
                            </div>
                            <div>
                                <label class="block text-sm font-medium mb-2">Custom Price (KHR)*</label>
                                <div class="relative">
                                    <div class="absolute inset-y-0 left-0 flex items-center pl-4 pointer-events-none">
                                        <span class="text-gray-500">៛</span>
                                    </div>
                                    <input class="border w-full p-4 pl-8 leading-tight bg-slate-100 rounded"
                                           id="custom_price_1"
                                           name="custom_price_1"
                                           type="number"
                                           min="1"
                                           placeholder="Enter price"
                                           value="{{ settings.paypervisit_custom_price_1 }}"
                                           required />
                                </div>
                            </div>
                        </div>
                        
                        <div class="grid grid-cols-1 md:grid-cols-2 gap-4 mb-4">
                            <div>
                                <label class="block text-sm font-medium mb-2">Quick Selection 2 (People Count)*</label>
                                <input class="border w-full p-4 leading-tight bg-slate-100 rounded"
                                       id="quick_select_2"
                                       name="quick_select_2"
                                       type="number"
                                       min="2"
                                       placeholder="Number of people"
                                       value="{{ settings.paypervisit_quick_select_2 }}"
                                       required />
                            </div>
                            <div>
                                <label class="block text-sm font-medium mb-2">Custom Price (KHR)*</label>
                                <div class="relative">
                                    <div class="absolute inset-y-0 left-0 flex items-center pl-4 pointer-events-none">
                                        <span class="text-gray-500">៛</span>
                                    </div>
                                    <input class="border w-full p-4 pl-8 leading-tight bg-slate-100 rounded"
                                           id="custom_price_2"
                                           name="custom_price_2"
                                           type="number"
                                           min="1"
                                           placeholder="Enter price"
                                           value="{{ settings.paypervisit_custom_price_2 }}"
                                           required />
                                </div>
                            </div>
                        </div>
                        
                        <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                            <div>
                                <label class="block text-sm font-medium mb-2">Quick Selection 3 (People Count)*</label>
                                <input class="border w-full p-4 leading-tight bg-slate-100 rounded"
                                       id="quick_select_3"
                                       name="quick_select_3"
                                       type="number"
                                       min="2"
                                       placeholder="Number of people"
                                       value="{{ settings.paypervisit_quick_select_3 }}"
                                       required />
                            </div>
                            <div>
                                <label class="block text-sm font-medium mb-2">Custom Price (KHR)*</label>
                                <div class="relative">
                                    <div class="absolute inset-y-0 left-0 flex items-center pl-4 pointer-events-none">
                                        <span class="text-gray-500">៛</span>
                                    </div>
                                    <input class="border w-full p-4 pl-8 leading-tight bg-slate-100 rounded"
                                           id="custom_price_3"
                                           name="custom_price_3"
                                           type="number"
                                           min="1"
                                           placeholder="Enter price"
                                           value="{{ settings.paypervisit_custom_price_3 }}"
                                           required />
                                </div>
                            </div>
                        </div>

                        <!-- Info Box -->
                        <div class="mt-3 p-3 bg-white rounded border border-blue-100">
                            <p class="text-sm text-gray-700"><i class="fas fa-info-circle text-blue-600 mr-1"></i> These settings control the quick selection buttons in the Pay-per-visit POS. You can customize both the number of people and the price for each selection.</p>
                        </div>
                    </div>

                    <div class="flex items-center justify-between">
                        <a href="{% url 'settings:dashboard' %}" class="bg-gray-300 hover:bg-gray-400 text-gray-800 font-bold py-3 px-6 rounded">Cancel</a>
                        <button class="bg-blue-900 hover:bg-blue-800 text-white font-bold py-3 px-6 rounded"
                                type="submit"><i class="fas fa-save mr-2"></i> Save Changes</button>
                    </div>
                </form>
            </div>

            <!-- Last Updated Info -->
            <div class="bg-gray-100 p-4 border-t">
                <p class="text-sm text-gray-600">
                    <i class="fas fa-clock mr-1"></i> Last updated: {{ settings.last_updated|date:"F j, Y H:i" }}
                </p>
            </div>
        </div>
    </div>
</div>
{% endblock %}
