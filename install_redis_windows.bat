@echo off
echo Installing Redis for Windows...
echo.

REM Check if chocolatey is installed
choco --version >nul 2>&1
if %errorlevel% neq 0 (
    echo Chocolatey not found. Installing Chocolatey first...
    echo.
    powershell -Command "Set-ExecutionPolicy Bypass -Scope Process -Force; [System.Net.ServicePointManager]::SecurityProtocol = [System.Net.ServicePointManager]::SecurityProtocol -bor 3072; iex ((New-Object System.Net.WebClient).DownloadString('https://community.chocolatey.org/install.ps1'))"
    
    if %errorlevel% neq 0 (
        echo Failed to install Chocolatey. Please install Redis manually.
        echo.
        echo Manual installation options:
        echo 1. Download Redis from: https://github.com/microsoftarchive/redis/releases
        echo 2. Or use WSL: wsl --install then sudo apt-get install redis-server
        echo 3. Or use Docker: docker run -d -p 6379:6379 redis:alpine
        pause
        exit /b 1
    )
)

echo Installing Redis using Chocolatey...
choco install redis-64 -y

if %errorlevel% neq 0 (
    echo Failed to install Redis via Chocolatey.
    echo.
    echo Alternative installation methods:
    echo 1. Download from: https://github.com/microsoftarchive/redis/releases
    echo 2. Use WSL: wsl --install then sudo apt-get install redis-server
    echo 3. Use Docker: docker run -d -p 6379:6379 redis:alpine
    pause
    exit /b 1
)

echo.
echo Redis installed successfully!
echo Starting Redis server...

REM Start Redis server
redis-server --service-install
redis-server --service-start

echo.
echo Redis is now running on localhost:6379
echo You can test it by running: redis-cli ping
echo.
pause
