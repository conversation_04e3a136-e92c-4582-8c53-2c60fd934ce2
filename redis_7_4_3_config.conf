# Redis 7.4.3 Configuration for Legend Fitness Club
# Basic server configuration
port 6379
bind 127.0.0.1
timeout 0
tcp-keepalive 60
tcp-backlog 511

# Logging
loglevel notice
logfile "redis-server.log"

# Database configuration
databases 16

# Persistence configuration
save 900 1
save 300 10
save 60 10000
stop-writes-on-bgsave-error yes
rdbcompression yes
rdbchecksum yes
dbfilename dump.rdb
dir ./

# Memory management
maxmemory 1gb
maxmemory-policy allkeys-lru

# Network and performance
tcp-keepalive 300

# Enable keyspace notifications for Django Channels
notify-keyspace-events Ex

# Redis 7.4.3 optimizations
lazyfree-lazy-eviction yes
lazyfree-lazy-expire yes
lazyfree-lazy-server-del yes
replica-lazy-flush yes

# Security (optional)
# requirepass your_password_here
