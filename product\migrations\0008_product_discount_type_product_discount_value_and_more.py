# Generated by Django 5.0.2 on 2025-05-13 00:34

import django.db.models.deletion
from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('product', '0007_remove_barcode_fields'),
    ]

    operations = [
        migrations.AddField(
            model_name='product',
            name='discount_type',
            field=models.CharField(choices=[('none', 'None'), ('percentage', 'Percentage'), ('fixed', 'Fixed Amount')], default='none', max_length=20, verbose_name='Discount Type'),
        ),
        migrations.AddField(
            model_name='product',
            name='discount_value',
            field=models.IntegerField(default=0, help_text='Percentage or fixed amount', verbose_name='Discount Value'),
        ),
        migrations.AddField(
            model_name='product',
            name='discounted_price',
            field=models.IntegerField(blank=True, help_text='Calculated discounted price', null=True, verbose_name='Discounted Price'),
        ),
        migrations.AddField(
            model_name='product',
            name='has_discount',
            field=models.BooleanField(default=False, verbose_name='Has Discount'),
        ),
        migrations.AddField(
            model_name='saleitem',
            name='discount_amount',
            field=models.IntegerField(default=0, help_text='Discount amount applied to this item', verbose_name='Discount Amount'),
        ),
        migrations.AddField(
            model_name='saleitem',
            name='discount_type',
            field=models.CharField(choices=[('none', 'None'), ('percentage', 'Percentage'), ('fixed', 'Fixed Amount'), ('promotion', 'Promotion')], default='none', max_length=20, verbose_name='Discount Type'),
        ),
        migrations.CreateModel(
            name='Discount',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('name', models.CharField(max_length=100, verbose_name='Name')),
                ('description', models.TextField(blank=True, null=True, verbose_name='Description')),
                ('discount_type', models.CharField(choices=[('percentage', 'Percentage'), ('fixed', 'Fixed Amount')], default='percentage', max_length=20, verbose_name='Discount Type')),
                ('discount_value', models.IntegerField(help_text='Percentage or fixed amount', verbose_name='Discount Value')),
                ('min_quantity', models.IntegerField(default=1, help_text='Minimum quantity required to apply discount', verbose_name='Minimum Quantity')),
                ('is_active', models.BooleanField(default=True, verbose_name='Active')),
                ('created_at', models.DateTimeField(auto_now_add=True, verbose_name='Created At')),
                ('updated_at', models.DateTimeField(auto_now=True, verbose_name='Updated At')),
                ('category', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.CASCADE, related_name='discounts', to='product.category')),
                ('product', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.CASCADE, related_name='discounts', to='product.product')),
            ],
            options={
                'verbose_name_plural': 'Discounts',
            },
        ),
        migrations.CreateModel(
            name='Promotion',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('name', models.CharField(max_length=100, verbose_name='Name')),
                ('description', models.TextField(blank=True, null=True, verbose_name='Description')),
                ('start_date', models.DateTimeField(verbose_name='Start Date')),
                ('end_date', models.DateTimeField(verbose_name='End Date')),
                ('discount_type', models.CharField(choices=[('percentage', 'Percentage'), ('fixed', 'Fixed Amount'), ('buy_x_get_y', 'Buy X Get Y Free')], default='percentage', max_length=20, verbose_name='Discount Type')),
                ('discount_value', models.IntegerField(help_text='Percentage or fixed amount', verbose_name='Discount Value')),
                ('min_purchase_amount', models.IntegerField(default=0, help_text='Minimum purchase amount required', verbose_name='Minimum Purchase Amount')),
                ('is_active', models.BooleanField(default=True, verbose_name='Active')),
                ('created_at', models.DateTimeField(auto_now_add=True, verbose_name='Created At')),
                ('updated_at', models.DateTimeField(auto_now=True, verbose_name='Updated At')),
                ('buy_quantity', models.IntegerField(default=0, help_text='Number of items to buy', verbose_name='Buy Quantity')),
                ('free_quantity', models.IntegerField(default=0, help_text='Number of items to get free', verbose_name='Free Quantity')),
                ('categories', models.ManyToManyField(blank=True, related_name='promotions', to='product.category')),
                ('products', models.ManyToManyField(blank=True, related_name='promotions', to='product.product')),
            ],
            options={
                'verbose_name_plural': 'Promotions',
            },
        ),
        migrations.AddField(
            model_name='saleitem',
            name='promotion',
            field=models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='sale_items', to='product.promotion'),
        ),
    ]
