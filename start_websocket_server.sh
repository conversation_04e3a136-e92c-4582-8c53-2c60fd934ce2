#!/bin/bash

echo "Starting Legend Fitness Club with WebSocket Support..."
echo

# Check if virtual environment is activated
if [ -z "$VIRTUAL_ENV" ]; then
    echo "Activating virtual environment..."
    source venv/bin/activate
    if [ $? -ne 0 ]; then
        echo "Failed to activate virtual environment. Please ensure venv exists."
        exit 1
    fi
fi

echo "Checking Django configuration..."
python manage.py check
if [ $? -ne 0 ]; then
    echo "Django configuration check failed. Please fix the errors above."
    exit 1
fi

echo
echo "Starting Daphne ASGI server with WebSocket support..."
echo "Server will be available at: http://127.0.0.1:8000/"
echo "WebSocket endpoints:"
echo "  - ws://127.0.0.1:8000/ws/permissions/"
echo "  - ws://127.0.0.1:8000/ws/notifications/"
echo
echo "Press Ctrl+C to stop the server"
echo

# Start Daphne server
daphne -p 8000 core.asgi:application
