import os
import datetime
import subprocess
from django.core.management.base import BaseCommand
from django.conf import settings
from django.utils import timezone
from settings.utils import mark_backup_complete

class Command(BaseCommand):
    help = 'Create a backup of the MySQL database'

    def add_arguments(self, parser):
        parser.add_argument(
            '--output-dir',
            default='backups',
            help='Directory where backups will be stored',
        )
        parser.add_argument(
            '--filename',
            help='Custom filename for the backup (without extension)',
        )

    def handle(self, *args, **options):
        # Get database settings
        db_settings = settings.DATABASES['default']
        db_name = db_settings['NAME']
        db_user = db_settings['USER']
        db_password = db_settings['PASSWORD']
        db_host = db_settings['HOST']
        db_port = db_settings['PORT']

        # Create backup directory if it doesn't exist
        output_dir = options['output_dir']
        backup_dir = os.path.join(settings.BASE_DIR, output_dir)
        os.makedirs(backup_dir, exist_ok=True)

        # Generate filename with timestamp if not provided
        if options['filename']:
            filename = f"{options['filename']}.sql"
        else:
            timestamp = datetime.datetime.now().strftime('%Y%m%d_%H%M%S')
            filename = f"backup_{timestamp}.sql"

        # Full path to the backup file
        backup_path = os.path.join(backup_dir, filename)

        # Build the mysqldump command
        cmd = [
            'mysqldump',
            f'--host={db_host}',
            f'--port={db_port}',
            f'--user={db_user}',
        ]

        if db_password:
            cmd.append(f'--password={db_password}')

        cmd.append(db_name)

        # Execute the command and redirect output to file
        try:
            with open(backup_path, 'w') as f:
                self.stdout.write(self.style.WARNING(f'Creating backup of database "{db_name}"...'))
                process = subprocess.Popen(cmd, stdout=f, stderr=subprocess.PIPE)
                _, stderr = process.communicate()

                if process.returncode != 0:
                    self.stdout.write(self.style.ERROR(f'Error creating backup: {stderr.decode()}'))
                    return

            # Update the last backup date in settings
            mark_backup_complete()

            self.stdout.write(self.style.SUCCESS(f'Backup created successfully at {backup_path}'))
            
            # Return the backup path for use in views
            return backup_path
            
        except Exception as e:
            self.stdout.write(self.style.ERROR(f'Error creating backup: {str(e)}'))
