{% extends 'base.html' %}
{% load custom_filters %}



{% block body %}
<div class="conponentSection p-5 bg-gray-200">
    <div class="componentWrapper">
        <div class="bg-white p-8 rounded shadow-md max-w-2xl mx-auto">
            <!-- Navigation -->
            <div class="mb-6 flex justify-between">
                <a href="{% url 'finance:template_list' %}" class="bg-gray-500 hover:bg-gray-600 text-white font-bold py-2 px-4 rounded transition duration-200">
                    <i class="fas fa-arrow-left mr-2"></i>Back to Templates
                </a>
                <a href="{% url 'finance:edit_template' template.id %}" class="bg-blue-900 hover:bg-blue-800 text-white font-bold py-2 px-4 rounded transition duration-200">
                    <i class="fas fa-edit mr-2"></i>Edit Template
                </a>
            </div>

            <!-- Template Info -->
            <div class="mb-6 p-4 bg-blue-50 border-l-4 border-blue-500 rounded">
                <h3 class="text-lg font-bold text-blue-900">Template Preview</h3>
                <p class="text-sm text-blue-800">{{ template.name }} ({{ template.get_language_display }})</p>
                <p class="text-xs text-blue-700 mt-1">This is a preview using sample data</p>
            </div>

            <!-- Receipt Template -->
            <div class="receipt-container" style="background-color: {{ template.background_color }}; color: {{ template.text_color }}; border: 1px solid #ddd; padding: 20px;">
                <!-- Header -->
                <div class="text-center mb-6">
                    {% if template.company_logo %}
                    <div class="mb-4">
                        <img src="{{ template.company_logo.url }}" alt="Company Logo" class="mx-auto h-20">
                    </div>
                    {% endif %}
                    <h1 class="text-2xl font-bold" style="color: {{ template.accent_color }};">{{ template.header_text }}</h1>
                    <h2 class="text-xl">{{ template.subheader_text }}</h2>
                </div>

                <!-- Company Info -->
                {% if template.show_company_info %}
                <div class="mb-6 text-center">
                    <p>Legend Fitness Club</p>
                    <p>Phnom Penh, Cambodia</p>
                    <p>Phone: ***********</p>
                </div>
                {% endif %}

                <!-- Transaction Details -->
                <div class="mb-6">
                    <div class="border-b-2 border-t-2 py-2 mb-4" style="border-color: {{ template.accent_color }};">
                        <h3 class="text-lg font-bold text-center" style="color: {{ template.accent_color }};">
                            DEPOSIT RECEIPT
                        </h3>
                    </div>

                    <table class="w-full">
                        <tr>
                            <td class="py-2 font-semibold">Transaction ID:</td>
                            <td class="py-2">{{ transaction.transaction_id }}</td>
                        </tr>
                        <tr>
                            <td class="py-2 font-semibold">Date:</td>
                            <td class="py-2">{{ transaction.transaction_date|date:"d M Y H:i" }}</td>
                        </tr>
                        <tr>
                            <td class="py-2 font-semibold">Type:</td>
                            <td class="py-2">Deposit</td>
                        </tr>
                        <tr>
                            <td class="py-2 font-semibold">Amount:</td>
                            <td class="py-2">{{ transaction.amount_khr|format_khr }}</td>
                        </tr>
                        <tr>
                            <td class="py-2 font-semibold">Payment Method:</td>
                            <td class="py-2">Cash</td>
                        </tr>
                        <tr>
                            <td class="py-2 font-semibold">Source:</td>
                            <td class="py-2">Membership Sales</td>
                        </tr>
                        <tr>
                            <td class="py-2 font-semibold">Status:</td>
                            <td class="py-2">Completed</td>
                        </tr>
                        <tr>
                            <td class="py-2 font-semibold">Staff:</td>
                            <td class="py-2">{{ request.user.name }}</td>
                        </tr>
                        <tr>
                            <td class="py-2 font-semibold">Notes:</td>
                            <td class="py-2">Sample transaction for template preview</td>
                        </tr>
                    </table>
                </div>

                <!-- Signatures -->
                {% if template.show_signatures %}
                <div class="mt-12 grid grid-cols-2 gap-8">
                    <div class="text-center">
                        <div class="border-t border-gray-400 pt-2">
                            Authorized Signature
                        </div>
                    </div>
                    <div class="text-center">
                        <div class="border-t border-gray-400 pt-2">
                            Recipient Signature
                        </div>
                    </div>
                </div>
                {% endif %}

                <!-- Footer -->
                <div class="mt-12 text-center text-sm">
                    <p>{{ template.footer_text }}</p>
                    <p class="mt-2">Printed on {{ transaction.transaction_date|date:"d M Y H:i" }}</p>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block js %}
<style>
    /* Apply custom CSS if provided */
    {% if template.custom_css %}
    {{ template.custom_css|safe }}
    {% endif %}
</style>
{% endblock %}
