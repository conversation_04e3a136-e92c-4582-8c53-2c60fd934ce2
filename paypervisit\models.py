from django.db import models
from django.utils.translation import gettext_lazy as _
from user.models import User

class PayPerVisitSettings(models.Model):
    # Base price
    price_per_person = models.IntegerField(_("Price Per Person"), default=4000)

    # Quick selection people counts
    quick_select_1 = models.IntegerField(_("Quick Selection 1"), default=2)
    quick_select_2 = models.IntegerField(_("Quick Selection 2"), default=5)
    quick_select_3 = models.IntegerField(_("Quick Selection 3"), default=10)

    # Custom prices for quick selections
    price_for_2 = models.IntegerField(_("Price For 2 People"), default=8000)
    price_for_5 = models.IntegerField(_("Price For 5 People"), default=20000)
    price_for_10 = models.IntegerField(_("Price For 10 People"), default=40000)

    # Custom prices for new quick selections
    custom_price_1 = models.IntegerField(_("Custom Price 1"), default=8000)
    custom_price_2 = models.IntegerField(_("Custom Price 2"), default=20000)
    custom_price_3 = models.IntegerField(_("Custom Price 3"), default=40000)

    last_updated = models.DateTimeField(_("Last Updated"), auto_now=True)

    def __str__(self):
        return f"Pay-per-visit Settings (Price: {self.price_per_person}៛)"

    @classmethod
    def get_price(cls):
        """Get the current price per person, or create default if none exists"""
        settings = cls.objects.first()
        if not settings:
            settings = cls.objects.create(
                price_per_person=4000,
                quick_select_1=2,
                quick_select_2=5,
                quick_select_3=10,
                price_for_2=8000,
                price_for_5=20000,
                price_for_10=40000,
                custom_price_1=8000,
                custom_price_2=20000,
                custom_price_3=40000
            )
        return settings.price_per_person

    def save(self, *args, **kwargs):
        # If this is a new object, set default prices based on price_per_person
        if not self.pk:
            self.price_for_2 = self.price_per_person * 2
            self.price_for_5 = self.price_per_person * 5
            self.price_for_10 = self.price_per_person * 10
            self.custom_price_1 = self.price_per_person * self.quick_select_1
            self.custom_price_2 = self.price_per_person * self.quick_select_2
            self.custom_price_3 = self.price_per_person * self.quick_select_3
        super().save(*args, **kwargs)




class PayPerVisit(models.Model):
    PAYMENT_METHOD_CHOICES = [
        ('cash', _('Cash')),
        ('bank', _('Bank Transfer')),
        ('other', _('Other')),
    ]

    trxId = models.CharField(_("Transaction ID"), max_length=50, unique=True)
    amount = models.IntegerField(_("Amount"))
    num_people = models.IntegerField(_("Number of People"))
    date = models.DateTimeField(_("Date"), auto_now_add=True)
    payment_method = models.CharField(_("Payment Method"), max_length=20, choices=PAYMENT_METHOD_CHOICES, default='cash')
    received_by = models.ForeignKey(User, on_delete=models.SET_NULL, null=True)

    def __str__(self):
        return f"{self.trxId} - {self.num_people} people"

    class Meta:
        ordering = ['-date']
