from django.urls import path
from . import views

app_name = 'financialreport'

urlpatterns = [
    # Dashboard view
    path('', views.index, name='index'),

    # Detailed reports
    path('income/', views.income_report, name='income_report'),
    path('expense/', views.expense_report, name='expense_report'),
    path('balance/', views.balance_report, name='balance_report'),

    # API endpoints for charts and data
    path('api/summary/', views.api_summary, name='api_summary'),
    path('api/income-data/', views.api_income_data, name='api_income_data'),
    path('api/expense-data/', views.api_expense_data, name='api_expense_data'),
    path('api/monthly-data/', views.api_monthly_data, name='api_monthly_data'),

    # Export functionality
    path('export/pdf/<str:report_type>/', views.export_pdf, name='export_pdf'),
    path('export/csv/<str:report_type>/', views.export_csv, name='export_csv'),
    path('print/<str:report_type>/', views.print_report, name='print_report'),
    path('preview/<str:report_type>/<str:format_type>/', views.preview_report, name='preview_report'),

    # Template management
    path('templates/', views.template_list, name='template_list'),
    path('templates/create/', views.create_template, name='create_template'),
    path('templates/edit/<int:pk>/', views.edit_template, name='edit_template'),
    path('templates/delete/<int:pk>/', views.delete_template, name='delete_template'),
    path('templates/preview/<int:pk>/', views.preview_template, name='preview_template'),
]
