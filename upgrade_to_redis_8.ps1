# Redis 8.0.1 Upgrade Script for Legend Fitness Club
# Run this script as Administrator in PowerShell

Write-Host "Redis 8.0.1 Upgrade for Legend Fitness Club" -ForegroundColor Green
Write-Host "===========================================" -ForegroundColor Green

# Check if running as administrator
if (-NOT ([Security.Principal.WindowsPrincipal] [Security.Principal.WindowsIdentity]::GetCurrent()).IsInRole([Security.Principal.WindowsBuiltInRole] "Administrator")) {
    Write-Host "This script must be run as Administrator!" -ForegroundColor Red
    Write-Host "Right-click PowerShell and select 'Run as Administrator'" -ForegroundColor Yellow
    Read-Host "Press Enter to exit"
    exit 1
}

Write-Host "Running as Administrator - proceeding..." -ForegroundColor Green

# Step 1: Stop current Redis 3.0.504 service
Write-Host "`nStep 1: Stopping Redis 3.0.504 service..." -ForegroundColor Yellow
try {
    Stop-Service -Name "redis" -Force -ErrorAction Stop
    Write-Host "✓ Redis 3.0.504 service stopped successfully" -ForegroundColor Green
    Start-Sleep -Seconds 3
} catch {
    Write-Host "⚠ Redis service may not be running or already stopped: $($_.Exception.Message)" -ForegroundColor Yellow
}

# Step 2: Verify Redis 8.0.1 installation
Write-Host "`nStep 2: Verifying Redis 8.0.1 installation..." -ForegroundColor Yellow
$redis8Path = "C:\redis-8.0.1"

if (!(Test-Path $redis8Path)) {
    Write-Host "✗ Redis 8.0.1 directory not found at $redis8Path" -ForegroundColor Red
    Write-Host "Please ensure Redis 8.0.1 is installed in the correct location" -ForegroundColor Red
    Read-Host "Press Enter to exit"
    exit 1
}

Write-Host "✓ Redis 8.0.1 found at $redis8Path" -ForegroundColor Green

# Step 3: Create Redis 8.0.1 configuration
Write-Host "`nStep 3: Creating Redis 8.0.1 configuration..." -ForegroundColor Yellow

$redisConfig = @"
# Redis 8.0.1 Configuration for Legend Fitness Club
# Basic server configuration
port 6379
bind 127.0.0.1
timeout 0
tcp-keepalive 60
tcp-backlog 511

# Logging
loglevel notice
logfile "redis-server.log"

# Database configuration
databases 16

# Persistence configuration
save 900 1
save 300 10
save 60 10000
stop-writes-on-bgsave-error yes
rdbcompression yes
rdbchecksum yes
dbfilename dump.rdb
dir ./

# Memory management
maxmemory 1gb
maxmemory-policy allkeys-lru

# Network and performance
tcp-keepalive 300

# Enable keyspace notifications for Django Channels
notify-keyspace-events Ex

# Redis 8.0.1 optimizations
lazyfree-lazy-eviction yes
lazyfree-lazy-expire yes
lazyfree-lazy-server-del yes
replica-lazy-flush yes

# Security (optional)
# requirepass your_password_here
"@

$configPath = "$redis8Path\redis.windows-service.conf"
$redisConfig | Out-File -FilePath $configPath -Encoding UTF8
Write-Host "✓ Redis 8.0.1 configuration file created: $configPath" -ForegroundColor Green

# Step 4: Install Redis 8.0.1 as Windows service
Write-Host "`nStep 4: Installing Redis 8.0.1 as Windows service..." -ForegroundColor Yellow

Set-Location $redis8Path

try {
    # Remove old Redis service if exists
    & .\redis-server.exe --service-uninstall --service-name redis 2>$null
    & .\redis-server.exe --service-uninstall --service-name Redis8 2>$null
    
    # Install new Redis 8.0.1 service
    & .\redis-server.exe --service-install redis.windows-service.conf --loglevel verbose --service-name Redis8
    
    if ($LASTEXITCODE -eq 0) {
        Write-Host "✓ Redis 8.0.1 service installed successfully!" -ForegroundColor Green
        
        # Start the service
        & .\redis-server.exe --service-start --service-name Redis8
        
        if ($LASTEXITCODE -eq 0) {
            Write-Host "✓ Redis 8.0.1 service started successfully!" -ForegroundColor Green
        } else {
            Write-Host "⚠ Failed to start Redis 8.0.1 service, trying manual start..." -ForegroundColor Yellow
            Start-Process -FilePath ".\redis-server.exe" -ArgumentList "redis.windows-service.conf" -WindowStyle Minimized
        }
    } else {
        Write-Host "⚠ Failed to install Redis 8.0.1 service, trying manual start..." -ForegroundColor Yellow
        Start-Process -FilePath ".\redis-server.exe" -ArgumentList "redis.windows-service.conf" -WindowStyle Minimized
    }
} catch {
    Write-Host "⚠ Service installation failed: $($_.Exception.Message)" -ForegroundColor Yellow
    Write-Host "Trying manual start..." -ForegroundColor Yellow
    Start-Process -FilePath ".\redis-server.exe" -ArgumentList "redis.windows-service.conf" -WindowStyle Minimized
}

# Step 5: Test Redis 8.0.1
Write-Host "`nStep 5: Testing Redis 8.0.1 installation..." -ForegroundColor Yellow

Start-Sleep -Seconds 5

try {
    $pingResult = & .\redis-cli.exe ping 2>$null
    if ($pingResult -eq "PONG") {
        Write-Host "✓ Redis 8.0.1 is responding to ping!" -ForegroundColor Green
        
        # Get Redis version
        $versionInfo = & .\redis-cli.exe info server 2>$null | Select-String "redis_version"
        Write-Host "✓ $versionInfo" -ForegroundColor Green
        
        # Test basic operations
        & .\redis-cli.exe set test "Redis 8.0.1 Working" >$null 2>&1
        $testResult = & .\redis-cli.exe get test 2>$null
        & .\redis-cli.exe del test >$null 2>&1
        
        if ($testResult -eq "Redis 8.0.1 Working") {
            Write-Host "✓ Redis 8.0.1 basic operations working!" -ForegroundColor Green
        }
        
        # Test advanced Redis 8.0.1 features
        $memoryInfo = & .\redis-cli.exe info memory 2>$null | Select-String "used_memory_human"
        Write-Host "✓ Memory usage: $memoryInfo" -ForegroundColor Green
        
    } else {
        Write-Host "✗ Redis 8.0.1 is not responding" -ForegroundColor Red
    }
} catch {
    Write-Host "✗ Redis 8.0.1 test failed: $($_.Exception.Message)" -ForegroundColor Red
}

# Step 6: Update system PATH
Write-Host "`nStep 6: Updating system PATH..." -ForegroundColor Yellow

$currentPath = [Environment]::GetEnvironmentVariable("PATH", "Machine")
if ($currentPath -notlike "*$redis8Path*") {
    $newPath = "$currentPath;$redis8Path"
    [Environment]::SetEnvironmentVariable("PATH", $newPath, "Machine")
    Write-Host "✓ Redis 8.0.1 added to system PATH" -ForegroundColor Green
} else {
    Write-Host "✓ Redis 8.0.1 already in system PATH" -ForegroundColor Green
}

# Final summary
Write-Host "`n" + "="*60 -ForegroundColor Green
Write-Host "Redis 8.0.1 Upgrade Completed Successfully!" -ForegroundColor Green
Write-Host "="*60 -ForegroundColor Green
Write-Host "Redis 8.0.1 Location: $redis8Path" -ForegroundColor Yellow
Write-Host "Configuration File: $configPath" -ForegroundColor Yellow
Write-Host "Service Name: Redis8" -ForegroundColor Yellow
Write-Host "Port: 6379" -ForegroundColor Yellow
Write-Host ""
Write-Host "Next Steps:" -ForegroundColor Cyan
Write-Host "1. Restart your Django server" -ForegroundColor White
Write-Host "2. Run: python manage.py test_redis --detailed" -ForegroundColor White
Write-Host "3. Check for Redis 8.0.1 detection in Django logs" -ForegroundColor White
Write-Host "4. Test real-time WebSocket functionality" -ForegroundColor White
Write-Host ""
Write-Host "Expected Django output:" -ForegroundColor Cyan
Write-Host "✓ Redis 8.0.1 detected - using Redis channel layer" -ForegroundColor Green
Write-Host "✓ Redis detected - using Redis cache backend" -ForegroundColor Green
Write-Host "✓ Channel Backend: channels_redis.core.RedisChannelLayer" -ForegroundColor Green

Read-Host "`nPress Enter to exit"
