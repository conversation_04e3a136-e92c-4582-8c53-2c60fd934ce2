from django.urls import path
from . import views, api_views

app_name = 'settings'

urlpatterns = [
    path('', views.dashboard, name='dashboard'),
    path('general/', views.general, name='general'),
    path('product/', views.product, name='product'),
    path('paypervisit/', views.paypervisit, name='paypervisit'),
    path('currency/', views.currency, name='currency'),
    path('ui/', views.ui, name='ui'),
    path('system/', views.system, name='system'),
    path('clean-all-data/', views.clean_all_data, name='clean_all_data'),

    # Permissions management
    path('permissions/', views.permissions, name='permissions'),
    path('permissions/update/', views.update_permissions, name='update_permissions'),
    path('permissions/reset/<str:role>/', views.reset_role_permissions_view, name='reset_role_permissions'),
    path('permissions/reset-all/', views.reset_all_permissions, name='reset_all_permissions'),

    # Fix permissions (temporary utility)
    path('fix-permissions/', views.fix_permissions, name='fix_permissions'),

    # Backup and restore functionality
    path('backup/', views.backup_database, name='backup_database'),
    path('backups/', views.backup_list, name='backup_list'),
    path('restore/<str:filename>/', views.restore_database, name='restore_database'),
    path('delete-backup/<str:filename>/', views.delete_backup, name='delete_backup'),

    # API endpoints for real-time permission management
    path('api/permissions/check/', api_views.check_permissions, name='api_check_permissions'),
    path('api/permissions/status/', api_views.PermissionStatusView.as_view(), name='api_permission_status'),
    path('api/permissions/invalidate-cache/', api_views.invalidate_permission_cache, name='api_invalidate_cache'),
    path('api/permissions/health/', api_views.permission_health_check, name='api_permission_health'),
    path('api/websocket/status/', api_views.websocket_status, name='api_websocket_status'),
    path('api/redis/status/', api_views.redis_status, name='api_redis_status'),
]
