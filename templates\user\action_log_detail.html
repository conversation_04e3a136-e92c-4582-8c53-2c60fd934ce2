{% extends 'base.html' %}
{% load custom_filters %}

{% block title %}Action Log Detail - Legend Fitness Club{% endblock %}

{% block body %}
<div class="conponentSection p-5 bg-gray-200">
    <div class="bg-white rounded-lg shadow-lg p-6">
        <!-- Header -->
        <div class="flex flex-col sm:flex-row justify-between items-start sm:items-center mb-6">
            <div>
                <h1 class="text-2xl font-bold text-gray-800 mb-2">🔍 Action Log Detail</h1>
                <p class="text-gray-600">Detailed information about user action #{{ log.id }}</p>
            </div>
            <div class="flex gap-2 mt-4 sm:mt-0">
                <a href="{% url 'user:action_logs' %}" 
                   class="bg-gray-500 hover:bg-gray-600 text-white px-4 py-2 rounded-lg text-sm font-medium transition duration-200">
                    ← Back to Logs
                </a>
            </div>
        </div>

        <!-- Suspicious Activity Alert -->
        {% if suspicious_indicators %}
        <div class="bg-red-50 border-l-4 border-red-400 p-4 mb-6">
            <div class="flex">
                <div class="flex-shrink-0">
                    <svg class="h-5 w-5 text-red-400" viewBox="0 0 20 20" fill="currentColor">
                        <path fill-rule="evenodd" d="M8.257 3.099c.765-1.36 2.722-1.36 3.486 0l5.58 9.92c.75 1.334-.213 2.98-1.742 2.98H4.42c-1.53 0-2.493-1.646-1.743-2.98l5.58-9.92zM11 13a1 1 0 11-2 0 1 1 0 012 0zm-1-8a1 1 0 00-1 1v3a1 1 0 002 0V6a1 1 0 00-1-1z" clip-rule="evenodd" />
                    </svg>
                </div>
                <div class="ml-3">
                    <h3 class="text-sm font-medium text-red-800">⚠️ SUSPICIOUS ACTIVITY DETECTED</h3>
                    <div class="mt-2 text-sm text-red-700">
                        <ul class="list-disc list-inside">
                            {% for indicator in suspicious_indicators %}
                            <li>{{ indicator }}</li>
                            {% endfor %}
                        </ul>
                    </div>
                </div>
            </div>
        </div>
        {% endif %}

        <!-- User Risk Score -->
        {% if risk_score > 30 %}
        <div class="bg-orange-50 border-l-4 border-orange-400 p-4 mb-6">
            <div class="flex">
                <div class="flex-shrink-0">
                    <svg class="h-5 w-5 text-orange-400" viewBox="0 0 20 20" fill="currentColor">
                        <path fill-rule="evenodd" d="M8.257 3.099c.765-1.36 2.722-1.36 3.486 0l5.58 9.92c.75 1.334-.213 2.98-1.742 2.98H4.42c-1.53 0-2.493-1.646-1.743-2.98l5.58-9.92zM11 13a1 1 0 11-2 0 1 1 0 012 0zm-1-8a1 1 0 00-1 1v3a1 1 0 002 0V6a1 1 0 00-1-1z" clip-rule="evenodd" />
                    </svg>
                </div>
                <div class="ml-3">
                    <h3 class="text-sm font-medium text-orange-800">🚨 HIGH RISK USER</h3>
                    <div class="mt-2 text-sm text-orange-700">
                        <p>User risk score: <strong>{{ risk_score }}/100</strong> - Requires additional monitoring</p>
                    </div>
                </div>
            </div>
        </div>
        {% endif %}

        <div class="grid grid-cols-1 lg:grid-cols-2 gap-6">
            <!-- Basic Information -->
            <div class="bg-gray-50 rounded-lg p-6">
                <h2 class="text-lg font-semibold text-gray-800 mb-4">📋 Basic Information</h2>
                <div class="space-y-3">
                    <div class="flex justify-between">
                        <span class="text-gray-600">Log ID:</span>
                        <span class="font-medium">#{{ log.id }}</span>
                    </div>
                    <div class="flex justify-between">
                        <span class="text-gray-600">User:</span>
                        <span class="font-medium">{{ log.user.username|default:"Unknown" }} ({{ log.user.name|default:"N/A" }})</span>
                    </div>
                    <div class="flex justify-between">
                        <span class="text-gray-600">Action Type:</span>
                        <span class="font-medium">{{ log.get_action_type_display }}</span>
                    </div>
                    <div class="flex justify-between">
                        <span class="text-gray-600">Module:</span>
                        <span class="font-medium">{{ log.get_module_display }}</span>
                    </div>
                    <div class="flex justify-between">
                        <span class="text-gray-600">Status:</span>
                        <span class="px-2 inline-flex text-xs leading-5 font-semibold rounded-full 
                            {% if log.status == 'success' %}bg-green-100 text-green-800
                            {% elif log.status == 'failed' %}bg-red-100 text-red-800
                            {% else %}bg-yellow-100 text-yellow-800{% endif %}">
                            {{ log.get_status_display }}
                        </span>
                    </div>
                    <div class="flex justify-between">
                        <span class="text-gray-600">Timestamp:</span>
                        <span class="font-medium">{{ log.action_time|date:"M d, Y H:i:s" }}</span>
                    </div>
                </div>
            </div>

            <!-- Target Information -->
            <div class="bg-gray-50 rounded-lg p-6">
                <h2 class="text-lg font-semibold text-gray-800 mb-4">🎯 Target Information</h2>
                <div class="space-y-3">
                    <div class="flex justify-between">
                        <span class="text-gray-600">Target Model:</span>
                        <span class="font-medium">{{ log.target_model|default:"N/A" }}</span>
                    </div>
                    <div class="flex justify-between">
                        <span class="text-gray-600">Target ID:</span>
                        <span class="font-medium">{{ log.target_id|default:"N/A" }}</span>
                    </div>
                    <div class="flex justify-between">
                        <span class="text-gray-600">Description:</span>
                        <span class="font-medium">{{ log.target_description|default:"N/A" }}</span>
                    </div>
                    {% if log.financial_impact %}
                    <div class="flex justify-between">
                        <span class="text-gray-600">Financial Impact:</span>
                        <span class="font-medium text-red-600">{{ log.get_financial_impact_display }}</span>
                    </div>
                    {% endif %}
                </div>
            </div>

            <!-- Request Information -->
            <div class="bg-gray-50 rounded-lg p-6">
                <h2 class="text-lg font-semibold text-gray-800 mb-4">🌐 Request Information</h2>
                <div class="space-y-3">
                    <div class="flex justify-between">
                        <span class="text-gray-600">IP Address:</span>
                        <span class="font-medium">{{ log.ip_address|default:"N/A" }}</span>
                    </div>
                    <div class="flex justify-between">
                        <span class="text-gray-600">User Agent:</span>
                        <span class="font-medium text-xs">{{ log.user_agent|truncatechars:50|default:"N/A" }}</span>
                    </div>
                </div>
            </div>

            <!-- Additional Information -->
            <div class="bg-gray-50 rounded-lg p-6">
                <h2 class="text-lg font-semibold text-gray-800 mb-4">ℹ️ Additional Information</h2>
                <div class="space-y-3">
                    {% if log.description %}
                    <div>
                        <span class="text-gray-600 block mb-1">Description:</span>
                        <span class="font-medium">{{ log.description }}</span>
                    </div>
                    {% endif %}
                    {% if log.additional_data %}
                    <div>
                        <span class="text-gray-600 block mb-1">Additional Data:</span>
                        <pre class="bg-gray-100 p-2 rounded text-xs overflow-x-auto">{{ log.additional_data|pprint }}</pre>
                    </div>
                    {% endif %}
                </div>
            </div>
        </div>

        <!-- Before/After Values -->
        {% if log.before_values or log.after_values %}
        <div class="mt-6">
            <h2 class="text-lg font-semibold text-gray-800 mb-4">🔄 Change Details</h2>
            <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                {% if log.before_values %}
                <div class="bg-red-50 rounded-lg p-6">
                    <h3 class="text-md font-semibold text-red-800 mb-3">Before (Original Values)</h3>
                    <pre class="bg-red-100 p-3 rounded text-sm overflow-x-auto">{{ log.before_values|pprint }}</pre>
                </div>
                {% endif %}
                {% if log.after_values %}
                <div class="bg-green-50 rounded-lg p-6">
                    <h3 class="text-md font-semibold text-green-800 mb-3">After (New Values)</h3>
                    <pre class="bg-green-100 p-3 rounded text-sm overflow-x-auto">{{ log.after_values|pprint }}</pre>
                </div>
                {% endif %}
            </div>
        </div>
        {% endif %}

        <!-- Related Activity -->
        {% if related_logs %}
        <div class="mt-6">
            <h2 class="text-lg font-semibold text-gray-800 mb-4">🔗 Related Activity (±1 hour)</h2>
            <div class="overflow-x-auto">
                <table class="min-w-full bg-white border border-gray-200">
                    <thead class="bg-gray-50">
                        <tr>
                            <th class="px-4 py-2 text-left text-xs font-medium text-gray-500 uppercase">Time</th>
                            <th class="px-4 py-2 text-left text-xs font-medium text-gray-500 uppercase">Action</th>
                            <th class="px-4 py-2 text-left text-xs font-medium text-gray-500 uppercase">Target</th>
                            <th class="px-4 py-2 text-left text-xs font-medium text-gray-500 uppercase">Status</th>
                            <th class="px-4 py-2 text-left text-xs font-medium text-gray-500 uppercase">Actions</th>
                        </tr>
                    </thead>
                    <tbody class="divide-y divide-gray-200">
                        {% for related_log in related_logs %}
                        <tr class="hover:bg-gray-50">
                            <td class="px-4 py-2 text-sm text-gray-900">{{ related_log.action_time|date:"H:i:s" }}</td>
                            <td class="px-4 py-2 text-sm text-gray-900">{{ related_log.get_action_type_display }}</td>
                            <td class="px-4 py-2 text-sm text-gray-900">{{ related_log.target_description|truncatechars:30 }}</td>
                            <td class="px-4 py-2">
                                <span class="px-2 inline-flex text-xs leading-5 font-semibold rounded-full 
                                    {% if related_log.status == 'success' %}bg-green-100 text-green-800
                                    {% elif related_log.status == 'failed' %}bg-red-100 text-red-800
                                    {% else %}bg-yellow-100 text-yellow-800{% endif %}">
                                    {{ related_log.get_status_display }}
                                </span>
                            </td>
                            <td class="px-4 py-2 text-sm font-medium">
                                <a href="{% url 'user:action_log_detail' related_log.pk %}" 
                                   class="text-blue-600 hover:text-blue-900">View</a>
                            </td>
                        </tr>
                        {% endfor %}
                    </tbody>
                </table>
            </div>
        </div>
        {% endif %}
    </div>
</div>
{% endblock %}
