# Generated by Django 5.0.2 on 2025-05-18 01:29

import django.db.models.deletion
from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('paypervisit', '0008_receipttemplate_company_address_km_and_more'),
        ('settings', '0004_migrate_existing_templates'),
    ]

    operations = [
        migrations.AlterField(
            model_name='paypervisitsettings',
            name='default_receipt_template',
            field=models.ForeignKey(blank=True, limit_choices_to={'template_type': 'paypervisit'}, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='paypervisit_settings', to='settings.printtemplate'),
        ),
        migrations.DeleteModel(
            name='ReceiptTemplate',
        ),
    ]
