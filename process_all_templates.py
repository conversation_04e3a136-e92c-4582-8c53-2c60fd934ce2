import os
import subprocess
import sys

def find_html_files(directory):
    """Find all HTML files in the given directory and its subdirectories."""
    html_files = []
    for root, dirs, files in os.walk(directory):
        for file in files:
            if file.endswith('.html'):
                html_files.append(os.path.join(root, file))
    return html_files

def process_template(template_path):
    """Process a template file with translate_template.py."""
    print(f"Processing: {template_path}")
    result = subprocess.run(['python', 'translate_template.py', template_path], 
                           capture_output=True, text=True)
    if result.returncode == 0:
        print(f"Successfully processed: {template_path}")
    else:
        print(f"Error processing {template_path}: {result.stderr}")

def main():
    templates_dir = 'templates'
    
    # Process base.html first
    base_html = os.path.join(templates_dir, 'base.html')
    if os.path.exists(base_html):
        process_template(base_html)
    
    # Process sidebar.html next
    sidebar_html = os.path.join(templates_dir, 'sidebar.html')
    if os.path.exists(sidebar_html):
        process_template(sidebar_html)
    
    # Find all HTML files
    html_files = find_html_files(templates_dir)
    
    # Skip base.html and sidebar.html as they've already been processed
    html_files = [f for f in html_files if f not in [base_html, sidebar_html]]
    
    # Process all other HTML files
    for html_file in html_files:
        process_template(html_file)
    
    print(f"Processed {len(html_files) + 2} template files.")

if __name__ == "__main__":
    main()
