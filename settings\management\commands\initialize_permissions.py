from django.core.management.base import BaseCommand
from settings.utils import reset_role_permissions
from settings.models import RolePermission

class Command(BaseCommand):
    help = 'Initialize or reset role-based permissions to their default values'

    def add_arguments(self, parser):
        parser.add_argument(
            '--role',
            help='Specific role to reset permissions for (optional)',
        )

    def handle(self, *args, **options):
        role = options.get('role')
        
        if role:
            self.stdout.write(self.style.WARNING(f'Resetting permissions for role: {role}'))
            count = reset_role_permissions(role)
            self.stdout.write(self.style.SUCCESS(f'Successfully reset {count} permissions for role: {role}'))
        else:
            self.stdout.write(self.style.WARNING('Resetting permissions for all roles'))
            count = reset_role_permissions()
            self.stdout.write(self.style.SUCCESS(f'Successfully reset {count} permissions for all roles'))
