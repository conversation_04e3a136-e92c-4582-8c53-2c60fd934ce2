{% extends 'base.html' %}
{% load custom_filters %}



{% block body %}
<div class="conponentSection p-5 bg-gray-200">
    <div class="componentWrapper">
        <!-- Header with navigation -->
        <div class="flex justify-between items-center mb-4">
            <h3 class="text-2xl font-bold">Delete Template</h3>
            <div class="flex space-x-2">
                <a href="{% url 'billmanagement:template_list' %}" class="bg-blue-900 text-white px-4 py-2 rounded">
                    <i class="fa-solid fa-arrow-left mr-2"></i>Back to Templates
                </a>
            </div>
        </div>

        <!-- Delete Confirmation -->
        <div class="bg-white p-6 rounded shadow-md">
            <div class="text-center">
                <div class="mb-4">
                    <i class="fa-solid fa-triangle-exclamation text-red-600 text-5xl"></i>
                </div>
                <h4 class="text-xl font-semibold mb-2">Confirm Deletion</h4>
                <p class="text-gray-600 mb-6">Are you sure you want to delete this template? This action cannot be undone.</p>
                
                <div class="bg-gray-100 p-4 rounded-lg mb-6 max-w-md mx-auto text-left">
                    <div class="grid grid-cols-2 gap-2">
                        <div>
                            <p class="text-gray-600 text-sm">Template Name:</p>
                            <p class="font-medium">{{ template.name }}</p>
                        </div>
                        <div>
                            <p class="text-gray-600 text-sm">Language:</p>
                            <p class="font-medium">
                                {% if template.language == 'en' %}English
                                {% elif template.language == 'km' %}Khmer
                                {% else %}Bilingual
                                {% endif %}
                            </p>
                        </div>
                        <div>
                            <p class="text-gray-600 text-sm">Default:</p>
                            <p class="font-medium">{{ template.is_default|yesno:"Yes,No" }}</p>
                        </div>
                        <div>
                            <p class="text-gray-600 text-sm">Created:</p>
                            <p class="font-medium">{{ template.created_at|date:"d-M-Y" }}</p>
                        </div>
                    </div>
                </div>
                
                {% if template.is_default %}
                <div class="bg-yellow-100 border-l-4 border-yellow-500 text-yellow-700 p-4 mb-6 max-w-md mx-auto text-left">
                    <p class="font-bold">Warning!</p>
                    <p>This is currently set as the default template. If you delete it, another template will be set as default.</p>
                </div>
                {% endif %}
                
                <form method="post" class="flex justify-center space-x-4">
                    {% csrf_token %}
                    <button type="submit" class="bg-red-600 text-white px-6 py-3 rounded font-semibold">Delete Template</button>
                    <a href="{% url 'billmanagement:template_list' %}" class="bg-gray-500 text-white px-6 py-3 rounded font-semibold">Cancel</a>
                </form>
            </div>
        </div>
    </div>
</div>
{% endblock %}
