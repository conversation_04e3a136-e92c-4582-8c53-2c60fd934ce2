@echo off
echo Redis 7.x Upgrade for Legend Fitness Club
echo ========================================
echo.

REM Check if running as administrator
net session >nul 2>&1
if %errorLevel% neq 0 (
    echo This script must be run as Administrator!
    echo Right-click on this file and select "Run as administrator"
    pause
    exit /b 1
)

echo Running as Administrator - proceeding...
echo.

echo Step 1: Stopping current Redis service...
sc stop redis >nul 2>&1
if %errorlevel% equ 0 (
    echo Redis service stopped successfully
) else (
    echo Redis service was not running or already stopped
)

echo.
echo Step 2: This script will help you upgrade to Redis 7.x
echo.
echo Manual steps required:
echo 1. Download Redis 7.x from: https://github.com/tporadowski/redis/releases
echo 2. Download: Redis-x64-7.0.15.zip
echo 3. Extract to: C:\Redis7\
echo 4. Run the PowerShell script: upgrade_to_redis7.ps1
echo.
echo Or use the automated PowerShell script:
echo Right-click on "upgrade_to_redis7.ps1" and select "Run with PowerShell"
echo.

pause

echo.
echo Alternative: Quick Redis 7.x setup using existing tools...
echo.

REM Create Redis7 directory
if not exist "C:\Redis7" mkdir "C:\Redis7"

echo Redis 7.x directory created: C:\Redis7
echo.
echo Please:
echo 1. Download Redis 7.x from the link above
echo 2. Extract to C:\Redis7\
echo 3. Run: C:\Redis7\redis-server.exe
echo 4. Test with: C:\Redis7\redis-cli.exe ping
echo 5. Restart your Django server
echo.

pause
